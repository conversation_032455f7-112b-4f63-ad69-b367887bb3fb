<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Permissions Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Camera Permissions Test</h1>
    <p>This page tests whether camera permissions are working correctly after fixing the Permissions Policy.</p>
    
    <div id="status" class="status info">
        Ready to test camera permissions...
    </div>
    
    <div>
        <button id="testPermissionsBtn" onclick="testCameraPermissions()">Test Camera Permissions</button>
        <button id="requestCameraBtn" onclick="requestCamera()" disabled>Request Camera Access</button>
        <button id="stopCameraBtn" onclick="stopCamera()" disabled>Stop Camera</button>
    </div>
    
    <div style="margin-top: 20px;">
        <video id="videoElement" autoplay muted style="display: none;"></video>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>Permissions Policy Information:</h3>
        <div id="permissionsPolicyInfo" class="status info">
            Checking permissions policy...
        </div>
    </div>

    <script>
        let currentStream = null;
        
        // Check permissions policy on page load
        window.addEventListener('load', function() {
            checkPermissionsPolicy();
        });
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function checkPermissionsPolicy() {
            const policyDiv = document.getElementById('permissionsPolicyInfo');
            
            // Check if Permissions Policy API is available
            if (document.featurePolicy) {
                const cameraAllowed = document.featurePolicy.allowsFeature('camera');
                policyDiv.innerHTML = `
                    <strong>Permissions Policy Status:</strong><br>
                    Camera allowed: ${cameraAllowed ? 'Yes' : 'No'}<br>
                    API: Feature Policy (deprecated)
                `;
                policyDiv.className = `status ${cameraAllowed ? 'success' : 'error'}`;
            } else if (document.permissionsPolicy) {
                const cameraAllowed = document.permissionsPolicy.allowsFeature('camera');
                policyDiv.innerHTML = `
                    <strong>Permissions Policy Status:</strong><br>
                    Camera allowed: ${cameraAllowed ? 'Yes' : 'No'}<br>
                    API: Permissions Policy
                `;
                policyDiv.className = `status ${cameraAllowed ? 'success' : 'error'}`;
            } else {
                policyDiv.innerHTML = `
                    <strong>Permissions Policy Status:</strong><br>
                    Permissions Policy API not available in this browser
                `;
                policyDiv.className = 'status info';
            }
        }
        
        async function testCameraPermissions() {
            updateStatus('Testing camera permissions...', 'info');
            
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access is not supported in this browser.');
                }
                
                // Check permission status if available
                if (navigator.permissions && navigator.permissions.query) {
                    try {
                        const permission = await navigator.permissions.query({ name: 'camera' });
                        updateStatus(`Camera permission status: ${permission.state}`, 'info');
                        
                        if (permission.state === 'denied') {
                            updateStatus('Camera permission is denied. Please allow camera access in your browser settings.', 'error');
                            return;
                        }
                        
                        if (permission.state === 'granted') {
                            updateStatus('Camera permission is already granted!', 'success');
                            document.getElementById('requestCameraBtn').disabled = false;
                            return;
                        }
                        
                        // If 'prompt', enable the request button
                        if (permission.state === 'prompt') {
                            updateStatus('Camera permission needs to be requested.', 'info');
                            document.getElementById('requestCameraBtn').disabled = false;
                            return;
                        }
                    } catch (e) {
                        updateStatus('Permission API not available, will try direct access.', 'info');
                    }
                }
                
                // Enable request button as fallback
                document.getElementById('requestCameraBtn').disabled = false;
                updateStatus('Ready to request camera access.', 'info');
                
            } catch (error) {
                updateStatus(`Error testing permissions: ${error.message}`, 'error');
            }
        }
        
        async function requestCamera() {
            updateStatus('Requesting camera access...', 'info');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                
                currentStream = stream;
                const videoElement = document.getElementById('videoElement');
                videoElement.srcObject = stream;
                videoElement.style.display = 'block';
                
                updateStatus('Camera access granted! Video stream is active.', 'success');
                
                document.getElementById('requestCameraBtn').disabled = true;
                document.getElementById('stopCameraBtn').disabled = false;
                
            } catch (error) {
                let errorMessage = 'Failed to access camera. ';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Camera permission was denied. Please allow camera access and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera access is not supported in this browser.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Camera is already in use by another application.';
                } else {
                    errorMessage += error.message || 'Unknown error occurred.';
                }
                
                updateStatus(errorMessage, 'error');
            }
        }
        
        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                
                const videoElement = document.getElementById('videoElement');
                videoElement.srcObject = null;
                videoElement.style.display = 'none';
                
                updateStatus('Camera stopped.', 'info');
                
                document.getElementById('requestCameraBtn').disabled = false;
                document.getElementById('stopCameraBtn').disabled = true;
            }
        }
    </script>
</body>
</html>
