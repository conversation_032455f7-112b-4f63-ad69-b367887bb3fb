<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Reports</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-flag me-2 text-danger"></i>Admin Reports Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Testing Admin Reports Interface</strong><br>
                            This page tests the admin reports DataTables functionality.
                        </div>

                        <!-- Test Buttons -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" onclick="testAjaxEndpoint()">Test AJAX Endpoint</button>
                            <button type="button" class="btn btn-secondary" onclick="testAuthentication()">Test Authentication</button>
                            <button type="button" class="btn btn-success" onclick="initDataTable()">Initialize DataTable</button>
                        </div>

                        <!-- Results -->
                        <div id="testResults" class="mb-3"></div>

                        <!-- Reports Table -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="reportsTable" width="100%">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Reporter</th>
                                        <th>Reported User</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <script>
    let reportsTable;

    function showResult(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
        $('#testResults').html(`<div class="alert ${alertClass}">${message}</div>`);
    }

    function testAjaxEndpoint() {
        showResult('Testing AJAX endpoint...', 'info');
        
        $.ajax({
            url: '/admin/reports/data',
            type: 'GET',
            data: {
                draw: 1,
                start: 0,
                length: 10
            },
            success: function(response) {
                console.log('AJAX Success:', response);
                showResult(`AJAX Success! Found ${response.recordsTotal} reports. Data: ${JSON.stringify(response, null, 2)}`, 'success');
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error);
                showResult(`AJAX Error: ${status} - ${error}. Response: ${xhr.responseText}`, 'error');
            }
        });
    }

    function testAuthentication() {
        showResult('Testing authentication...', 'info');
        
        $.ajax({
            url: '/admin/reports/debug',
            type: 'GET',
            success: function(response) {
                console.log('Auth Success:', response);
                showResult(`Authentication Success! Response: ${JSON.stringify(response, null, 2)}`, 'success');
            },
            error: function(xhr, status, error) {
                console.error('Auth Error:', xhr, status, error);
                showResult(`Authentication Error: ${status} - ${error}. Response: ${xhr.responseText}`, 'error');
            }
        });
    }

    function initDataTable() {
        showResult('Initializing DataTable...', 'info');
        
        if (reportsTable) {
            reportsTable.destroy();
        }
        
        reportsTable = $('#reportsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '/admin/reports/data',
                type: 'GET',
                error: function(xhr, error, code) {
                    console.error('DataTables AJAX Error:', error, code);
                    console.error('Response:', xhr.responseText);
                    showResult(`DataTables Error: ${error} - ${code}. Response: ${xhr.responseText}`, 'error');
                }
            },
            columns: [
                { data: 'id', name: 'id' },
                { data: 'reporter', name: 'reporter' },
                { data: 'reported_user', name: 'reported_user' },
                { data: 'category', name: 'category' },
                { data: 'status', name: 'status', orderable: false },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            order: [[0, 'desc']],
            pageLength: 10,
            language: {
                processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
                emptyTable: 'No reports found',
                zeroRecords: 'No matching reports found'
            },
            initComplete: function() {
                showResult('DataTable initialized successfully!', 'success');
            }
        });
    }

    $(document).ready(function() {
        console.log('Page loaded, ready for testing');
        showResult('Page loaded. Click buttons above to test functionality.', 'info');
    });
    </script>
</body>
</html>
