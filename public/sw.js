const CACHE_NAME = 'settingwala-v8';
const STATIC_CACHE = 'settingwala-static-v8';
const DYNAMIC_CACHE = 'settingwala-dynamic-v8';

const urlsToCache = [
  '/',
  '/manifest.json',
  '/images/icon-192x192.png',
  '/images/icon-512x512.png',
  '/images/default-avatar.png',
  '/offline.html'
];

// Install event
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(function(cache) {
        return cache.addAll(urlsToCache).catch(error => {
          // Continue installation even if some files fail to cache
          return Promise.resolve();
        });
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event with advanced caching strategy
self.addEventListener('fetch', function(event) {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip unsupported schemes (chrome-extension, etc.)
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Define routes that should be handled by the browser (not service worker)
  const skipRoutes = [
    '/',
    '/home',
    '/auth/',
    '/login',
    '/register',
    '/profile',
    '/gallery',
    '/event-address',
    '/event-join-now',
    '/payment',
    '/find-person',
    '/couple-activity',
    '/transactions',
    '/notifications',
    '/booking',
    '/meeting-verification',
    '/rating',
    '/reviews',
    '/calendar',
    '/provider',
    '/chat',
    '/wallet',
    '/subscription',
    '/about-us',
    '/how-it-works',
    '/safety-tips',
    '/help-center',
    '/contact-us',
    '/privacy-policy',
    '/terms-of-service',
    '/refund-policy',
    '/admin'
  ];

  // Define external domains that should never be intercepted
  const externalDomains = [
    'accounts.google.com',
    'oauth2.googleapis.com',
    'www.googleapis.com',
    'googleapis.com',
    'google.com',
    'razorpay.com',
    'api.razorpay.com',
    'checkout.razorpay.com'
  ];

  // Skip external domains completely
  if (externalDomains.some(domain => url.hostname.includes(domain))) {
    return;
  }

  // Skip navigation requests for routes that might redirect
  if (request.mode === 'navigate') {
    const shouldSkip = skipRoutes.some(route => {
      return url.pathname === route || url.pathname.startsWith(route + '/') || url.pathname.startsWith(route + '?');
    });

    if (shouldSkip) {
      return;
    }
  }

  // Skip Google OAuth and external redirects completely
  if (url.pathname.startsWith('/auth/google/') || url.pathname.includes('google') || url.pathname.includes('oauth')) {
    return;
  }

  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // Network first for API calls
    event.respondWith(networkFirst(request));
  } else if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
    // Cache first for static assets
    event.respondWith(cacheFirst(request));
  } else {
    // Stale while revalidate for pages
    event.respondWith(staleWhileRevalidate(request));
  }
});

// Network first strategy
async function networkFirst(request) {
  try {
    // Create a new request with redirect mode set to follow
    const fetchRequest = new Request(request, { redirect: 'follow' });
    const networkResponse = await fetch(fetchRequest);
    if (networkResponse.ok && networkResponse.status < 400) {
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.put(request, networkResponse.clone());
      } catch (cacheError) {
        // Failed to cache response - handle silently
      }
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

// Cache first strategy
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    // Create a new request with redirect mode set to follow
    const fetchRequest = new Request(request, { redirect: 'follow' });
    const networkResponse = await fetch(fetchRequest);
    if (networkResponse.ok && networkResponse.status < 400) {
      try {
        const cache = await caches.open(STATIC_CACHE);
        await cache.put(request, networkResponse.clone());
      } catch (cacheError) {
        // Failed to cache response - handle silently
      }
    }
    return networkResponse;
  } catch (error) {
    return new Response('Offline', { status: 503 });
  }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);

  // Create a new request with redirect mode set to follow
  const fetchRequest = new Request(request, { redirect: 'follow' });
  const networkResponsePromise = fetch(fetchRequest).then(async response => {
    if (response.ok && response.status < 400) {
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        // Clone the response before using it
        await cache.put(request, response.clone());
      } catch (error) {
        // Failed to cache response - handle silently
      }
    }
    return response;
  }).catch(error => {
    // Network request failed - handle silently
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }
    return new Response('Offline', { status: 503 });
  });

  return cachedResponse || networkResponsePromise;
}

// Background sync for offline actions
self.addEventListener('sync', function(event) {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Handle offline actions when back online
}

// Push notifications
self.addEventListener('push', function(event) {
  let notificationData = {};
  let title = 'SettingWala';
  let body = 'New notification from SettingWala';

  // Parse FCM payload
  if (event.data) {
    try {
      const payload = event.data.json();

      // Handle FCM notification format
      if (payload.notification) {
        title = payload.notification.title || title;
        body = payload.notification.body || body;
      }

      // Handle custom data
      if (payload.data) {
        notificationData = payload.data;
      }
    } catch (e) {
      // Fallback to text if JSON parsing fails
      body = event.data.text() || body;
    }
  }

  const options = {
    body: body,
    icon: '/images/icon-192x192.png',
    badge: '/images/icon-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      ...notificationData,
      dateOfArrival: Date.now(),
      url: notificationData.click_action || '/'
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/images/icon-72x72.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/images/icon-72x72.png'
      }
    ],
    requireInteraction: false,
    silent: false
  };

  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  let urlToOpen = '/';

  // Handle different actions
  if (event.action === 'view') {
    // Use the URL from notification data or default
    urlToOpen = event.notification.data.url || '/';
  } else if (event.action === 'dismiss') {
    // Just close the notification, don't open anything
    return;
  } else {
    // Default click (not on action button)
    urlToOpen = event.notification.data.url || '/';
  }

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
      // Check if there's already a window/tab open with the target URL
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes(urlToOpen.split('?')[0]) && 'focus' in client) {
          return client.focus();
        }
      }

      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});
