<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera and Location Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        video, canvas { max-width: 100%; border: 1px solid #ccc; border-radius: 5px; }
        #capturedPhoto { max-width: 100%; border: 1px solid #ccc; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Camera and Location Test</h1>
    <p>This page tests the fixed camera functionality and enhanced location accuracy.</p>
    
    <div id="status" class="status info">Ready to test...</div>
    
    <div>
        <button onclick="requestPermissions()" id="permissionBtn">Request Permissions</button>
        <button onclick="testCameraAndLocation()" id="testBtn" disabled>Test Camera & Location</button>
        <button onclick="captureTestPhoto()" id="captureBtn" disabled>Capture Photo</button>
        <button onclick="stopTest()">Stop Test</button>
    </div>

    <div id="permissionStatus" class="status info" style="display: none;">
        <h3>Permission Required</h3>
        <p>This app needs camera and location permissions to work properly.</p>
        <ul>
            <li id="cameraPermStatus">📷 Camera: <span>Checking...</span></li>
            <li id="locationPermStatus">📍 Location: <span>Checking...</span></li>
        </ul>
        <p><small>Click "Allow" when your browser asks for permissions.</small></p>
    </div>
    
    <div id="locationInfo" style="margin: 20px 0;"></div>
    
    <video id="video" autoplay playsinline style="display: none;"></video>
    <canvas id="canvas" style="display: none;"></canvas>
    <img id="capturedPhoto" style="display: none;">
    
    <script src="/js/media-performance-optimizer.js"></script>
    <script>
        let stream = null;
        let locationWatchId = null;
        let currentLocation = null;
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updatePermissionStatus(type, status, message) {
            const element = document.getElementById(type + 'PermStatus');
            const icon = type === 'camera' ? '📷' : '📍';
            const statusText = status === 'granted' ? '✅ Granted' :
                              status === 'denied' ? '❌ Denied' :
                              status === 'prompt' ? '⏳ Requesting...' : '❓ Unknown';
            element.innerHTML = `${icon} ${type.charAt(0).toUpperCase() + type.slice(1)}: <span>${statusText}</span>`;
            if (message) {
                element.innerHTML += `<br><small>${message}</small>`;
            }
        }

        async function checkPermissions() {
            const permissionStatus = document.getElementById('permissionStatus');
            permissionStatus.style.display = 'block';

            let cameraGranted = false;
            let locationGranted = false;

            // Check camera permission
            try {
                if (navigator.permissions) {
                    const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                    updatePermissionStatus('camera', cameraPermission.state);
                    cameraGranted = cameraPermission.state === 'granted';
                } else {
                    updatePermissionStatus('camera', 'unknown', 'Permission API not supported');
                }
            } catch (error) {
                updatePermissionStatus('camera', 'unknown', 'Could not check permission');
            }

            // Check location permission
            try {
                if (navigator.permissions) {
                    const locationPermission = await navigator.permissions.query({ name: 'geolocation' });
                    updatePermissionStatus('location', locationPermission.state);
                    locationGranted = locationPermission.state === 'granted';
                } else {
                    updatePermissionStatus('location', 'unknown', 'Permission API not supported');
                }
            } catch (error) {
                updatePermissionStatus('location', 'unknown', 'Could not check permission');
            }

            return { cameraGranted, locationGranted };
        }

        async function requestPermissions() {
            updateStatus('Checking current permissions...', 'info');

            const { cameraGranted, locationGranted } = await checkPermissions();

            if (cameraGranted && locationGranted) {
                updateStatus('All permissions already granted!', 'success');
                document.getElementById('testBtn').disabled = false;
                document.getElementById('permissionStatus').style.display = 'none';
                return;
            }

            updateStatus('Requesting permissions... Please allow when prompted.', 'info');

            let cameraSuccess = cameraGranted;
            let locationSuccess = locationGranted;

            // Request camera permission
            if (!cameraGranted) {
                try {
                    updatePermissionStatus('camera', 'prompt', 'Requesting camera access...');
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    stream.getTracks().forEach(track => track.stop()); // Stop immediately
                    updatePermissionStatus('camera', 'granted');
                    cameraSuccess = true;
                } catch (error) {
                    updatePermissionStatus('camera', 'denied', error.message);
                    cameraSuccess = false;
                }
            }

            // Request location permission
            if (!locationGranted) {
                try {
                    updatePermissionStatus('location', 'prompt', 'Requesting location access...');
                    await new Promise((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                updatePermissionStatus('location', 'granted');
                                locationSuccess = true;
                                resolve(position);
                            },
                            (error) => {
                                updatePermissionStatus('location', 'denied', error.message);
                                locationSuccess = false;
                                reject(error);
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 0
                            }
                        );
                    });
                } catch (error) {
                    // Error already handled in the callback
                }
            }

            // Update UI based on results
            if (cameraSuccess && locationSuccess) {
                updateStatus('All permissions granted! You can now test the features.', 'success');
                document.getElementById('testBtn').disabled = false;
                document.getElementById('permissionStatus').style.display = 'none';
            } else {
                let errorMsg = 'Some permissions were denied: ';
                if (!cameraSuccess) errorMsg += 'Camera ';
                if (!locationSuccess) errorMsg += 'Location ';
                errorMsg += '\nPlease check your browser settings and try again.';
                updateStatus(errorMsg, 'error');
            }
        }
        
        function validateLocationAccuracy(accuracy) {
            if (accuracy <= 10) {
                return { status: 'excellent', message: 'Excellent location accuracy', color: 'success' };
            } else if (accuracy <= 50) {
                return { status: 'good', message: 'Good location accuracy', color: 'success' };
            } else if (accuracy <= 100) {
                return { status: 'fair', message: 'Fair location accuracy', color: 'warning' };
            } else {
                return { status: 'poor', message: 'Poor location accuracy', color: 'error' };
            }
        }
        
        async function testCameraAndLocation() {
            updateStatus('Testing camera and location...', 'info');

            try {
                // Check permissions first
                const { cameraGranted, locationGranted } = await checkPermissions();

                if (!cameraGranted || !locationGranted) {
                    updateStatus('Permissions not granted. Please click "Request Permissions" first.', 'error');
                    return;
                }

                // Test location first
                await testLocation();

                // Test camera
                await testCamera();

                updateStatus('Camera and location test successful!', 'success');
                document.getElementById('captureBtn').disabled = false;

            } catch (error) {
                updateStatus(`Test failed: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        async function testLocation() {
            return new Promise((resolve, reject) => {
                if (!navigator.geolocation) {
                    reject(new Error('Geolocation not supported'));
                    return;
                }
                
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        currentLocation = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy,
                            timestamp: position.timestamp
                        };
                        
                        const validation = validateLocationAccuracy(position.coords.accuracy);
                        const locationInfo = document.getElementById('locationInfo');
                        locationInfo.innerHTML = `
                            <div class="status ${validation.color}">
                                <strong>Location:</strong> ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}<br>
                                <strong>Accuracy:</strong> ±${Math.round(position.coords.accuracy)}m (${validation.message})
                            </div>
                        `;
                        
                        // Start continuous monitoring
                        startLocationMonitoring();
                        
                        resolve();
                    },
                    function(error) {
                        reject(new Error(`Location error: ${error.message}`));
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 15000,
                        maximumAge: 0
                    }
                );
            });
        }
        
        function startLocationMonitoring() {
            if (locationWatchId) return;
            
            locationWatchId = navigator.geolocation.watchPosition(
                function(position) {
                    if (!currentLocation || position.coords.accuracy < currentLocation.accuracy) {
                        currentLocation = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy,
                            timestamp: position.timestamp
                        };
                        
                        const validation = validateLocationAccuracy(position.coords.accuracy);
                        const locationInfo = document.getElementById('locationInfo');
                        locationInfo.innerHTML = `
                            <div class="status ${validation.color}">
                                <strong>Location:</strong> ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}<br>
                                <strong>Accuracy:</strong> ±${Math.round(position.coords.accuracy)}m (${validation.message})<br>
                                <small>Updated: ${new Date().toLocaleTimeString()}</small>
                            </div>
                        `;
                    }
                },
                function(error) {
                    console.log('Location monitoring error:', error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
        }
        
        async function testCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                
                const video = document.getElementById('video');
                video.srcObject = stream;
                video.style.display = 'block';
                
            } catch (error) {
                throw new Error(`Camera error: ${error.message}`);
            }
        }
        
        function captureTestPhoto() {
            const video = document.getElementById('video');
            const canvas = document.getElementById('canvas');
            const context = canvas.getContext('2d');
            
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            // This should not throw the optimizeImageDataOperations error anymore
            context.drawImage(video, 0, 0);
            
            canvas.toBlob(function(blob) {
                const img = document.getElementById('capturedPhoto');
                img.src = URL.createObjectURL(blob);
                img.style.display = 'block';
                
                updateStatus('Photo captured successfully!', 'success');
            }, 'image/jpeg', 0.8);
        }
        
        function stopTest() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            
            if (locationWatchId) {
                navigator.geolocation.clearWatch(locationWatchId);
                locationWatchId = null;
            }
            
            document.getElementById('video').style.display = 'none';
            document.getElementById('capturedPhoto').style.display = 'none';
            document.getElementById('captureBtn').disabled = true;
            document.getElementById('locationInfo').innerHTML = '';
            
            updateStatus('Test stopped.', 'info');
        }

        // Initialize on page load
        window.addEventListener('load', async function() {
            updateStatus('Page loaded. Click "Request Permissions" to start.', 'info');

            // Check if permissions are already granted
            try {
                const { cameraGranted, locationGranted } = await checkPermissions();
                if (cameraGranted && locationGranted) {
                    updateStatus('All permissions already granted! You can test the features.', 'success');
                    document.getElementById('testBtn').disabled = false;
                    document.getElementById('permissionStatus').style.display = 'none';
                }
            } catch (error) {
                console.log('Could not check initial permissions:', error);
            }
        });
    </script>
</body>
</html>
