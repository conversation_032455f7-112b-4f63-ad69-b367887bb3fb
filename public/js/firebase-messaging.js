/**
 * Firebase Cloud Messaging Integration
 * Note: Notification override is handled by notification-override.js
 */

/**
 * Firebase Cloud Messaging (FCM) Integration
 * Handles push notifications for desktop browsers and PWA
 */

// Firebase configuration (will be set dynamically)
let firebaseConfig = null;
let messaging = null;

/**
 * Initialize Firebase messaging
 */
async function initializeFirebaseMessaging() {
    try {
        // Check if Firebase is supported
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {

            return false;
        }

        // Import Firebase modules dynamically
        const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js');
        const { getMessaging, getToken, onMessage } = await import('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging.js');

        // Get Firebase config from server
        const response = await fetch('/api/firebase-config');
        if (!response.ok) {

            return false;
        }

        firebaseConfig = await response.json();
        
        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        messaging = getMessaging(app);

        // Register service worker
        await navigator.serviceWorker.register('/sw.js');

        // Only initialize if permission is already granted (manual system)
        if (Notification.permission === 'granted') {
            await getFCMToken();
        }

        // Handle foreground messages
        onMessage(messaging, (payload) => {

            
            // Show notification if page is visible
            if (document.visibilityState === 'visible') {
                showForegroundNotification(payload);
            }
        });

        // Firebase messaging initialized successfully
        return true;

    } catch (error) {

        // Silently handle Firebase initialization failures
        return false;
    }
}

/**
 * Initialize Firebase messaging with permission (called after user grants permission)
 */
async function initializeFirebaseMessagingWithPermission() {
    if (!messaging) {

        return false;
    }

    try {
        await requestNotificationPermission();

        return true;
    } catch (error) {

        return false;
    }
}

/**
 * Get FCM token (only if permission already granted)
 */
async function getFCMToken() {
    try {
        // Check current permission status
        let permission = Notification.permission;

        // Only proceed if permission is already granted
        if (permission === 'granted') {
            // Get FCM token
            const { getToken } = await import('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging.js');
            
            const currentToken = await getToken(messaging, {
                vapidKey: firebaseConfig.vapidKey // You'll need to add this to your config
            });

            if (currentToken) {
                // Send token to server
                await sendTokenToServer(currentToken);

                return currentToken;
            } else {
                return null;
            }
        } else {
            // Completely silent - no console logs or messages
            return null;
        }

    } catch (error) {
        // Completely silent error handling
        return null;
    }
}

/**
 * Actually request notification permission from browser (using original function) - DISABLED to remove popups
 */
async function requestBrowserNotificationPermission() {
    try {
        // DISABLED: No longer request browser permission to remove popups
        console.info('Browser notification permission request disabled to remove popups');

        // Check current permission status
        let permission = Notification.permission;

        // DISABLED: No longer request permission if not granted
        // if (permission === 'default') {
        //     try {
        //         // Use the special function that bypasses our override
        //         if (window.requestRealBrowserPermission) {
        //             permission = await window.requestRealBrowserPermission();
        //         } else if (window._originalNotificationRequestPermission) {
        //             permission = await window._originalNotificationRequestPermission();
        //         } else {
        //             permission = 'denied';
        //         }
        //     } catch (error) {
        //         permission = 'denied';
        //     }
        // }

        return permission === 'granted';
    } catch (error) {
        return false;
    }
}

/**
 * Send FCM token to server
 */
async function sendTokenToServer(token) {
    try {
        const response = await fetch('/api/fcm-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({ token: token })
        });

        if (response.ok) {

        } else {
            console.error('Failed to send FCM token to server');
        }

    } catch (error) {
        console.error('Error sending FCM token to server:', error);
    }
}

/**
 * Show notification when app is in foreground
 */
function showForegroundNotification(payload) {
    const title = payload.notification?.title || 'New Notification';
    const body = payload.notification?.body || '';
    const icon = '/images/icon-192x192.png';

    // Create and show browser notification
    if (Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: body,
            icon: icon,
            badge: '/images/icon-72x72.png',
            data: payload.data || {},
            requireInteraction: false
        });

        // Handle notification click
        notification.onclick = function(event) {
            event.preventDefault();
            
            const url = payload.data?.click_action || '/';
            window.focus();
            
            // Navigate to URL if different from current page
            if (window.location.pathname !== url) {
                window.location.href = url;
            }
            
            notification.close();
        };

        // Auto-close after 5 seconds
        setTimeout(() => {
            notification.close();
        }, 5000);
    }
}

/**
 * Check if notifications are supported and enabled
 */
function isNotificationSupported() {
    return 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
}

/**
 * Get current notification permission status
 */
function getNotificationPermission() {
    if (!isNotificationSupported()) {
        return 'unsupported';
    }
    return Notification.permission;
}

/**
 * Show notification permission prompt (uses custom modal) - DISABLED to remove popups
 */
async function showNotificationPrompt() {
    if (!isNotificationSupported()) {
        console.info('Browser does not support notifications');
        return false;
    }

    // Check if permission is already granted
    if (Notification.permission === 'granted') {
        console.info('Notification permission already granted');
        return true;
    }

    // DISABLED: No longer show custom modal to remove popups
    // if (window.isCustomNotificationModalAllowed && window.isCustomNotificationModalAllowed()) {
    //     if (window.notificationPermissionModal) {
    //         window.notificationPermissionModal.show();
    //         return true; // Return true to indicate modal was shown
    //     }
    // }

    // Default: don't show modal
    console.info('Notification permission popup disabled');
    return false;
}

/**
 * Smart notification permission timing
 */
async function handleSmartPermissionTiming() {
    try {
        // Check if we should request permission on login
        const response = await fetch('/api/notification-permission/can-request', {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            const data = await response.json();

            // Show custom modal if conditions are met
            if (data.should_request_on_login && window.notificationPermissionModal) {
                // Wait a bit for the page to fully load and user to settle
                setTimeout(() => {
                    // Check if notification permission is not already granted
                    if (Notification.permission === 'default') {
                        window.notificationPermissionModal.show();
                    }
                }, 2000); // 2 second delay for better UX
            }

            // Mark user as having logged in
            if (data.is_first_login) {
                await fetch('/api/notification-permission/mark-logged-in', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });
            }
        }
    } catch (error) {
        console.error('Error in smart permission timing:', error);
    }
}



/**
 * Initialize on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (document.querySelector('meta[name="user-authenticated"]')) {
        initializeFirebaseMessaging().catch(error => {

        });

        // Handle smart permission timing - DISABLED to remove popups
        // handleSmartPermissionTiming();
    }
});

/**
 * Clear notification-related browser cache and storage
 */
async function clearNotificationCache() {
    try {
        console.info('Clearing notification cache and storage...');

        // Clear localStorage entries related to notifications
        const localStorageKeys = Object.keys(localStorage).filter(key =>
            key.toLowerCase().includes('notification') ||
            key.toLowerCase().includes('permission') ||
            key.toLowerCase().includes('fcm') ||
            key.toLowerCase().includes('firebase')
        );
        localStorageKeys.forEach(key => {
            localStorage.removeItem(key);
            console.info(`Removed localStorage key: ${key}`);
        });

        // Clear sessionStorage entries related to notifications
        const sessionStorageKeys = Object.keys(sessionStorage).filter(key =>
            key.toLowerCase().includes('notification') ||
            key.toLowerCase().includes('permission') ||
            key.toLowerCase().includes('fcm') ||
            key.toLowerCase().includes('firebase')
        );
        sessionStorageKeys.forEach(key => {
            sessionStorage.removeItem(key);
            console.info(`Removed sessionStorage key: ${key}`);
        });

        // Clear IndexedDB related to Firebase/FCM if possible
        if ('indexedDB' in window) {
            try {
                // Clear Firebase-related IndexedDB databases
                const databases = ['firebase-messaging-database', 'fcm_token_details_db'];
                for (const dbName of databases) {
                    const deleteReq = indexedDB.deleteDatabase(dbName);
                    deleteReq.onsuccess = () => console.info(`Cleared IndexedDB: ${dbName}`);
                    deleteReq.onerror = () => console.info(`Could not clear IndexedDB: ${dbName}`);
                }
            } catch (error) {
                console.info('Could not clear IndexedDB:', error);
            }
        }

        // Clear server-side tracking data
        try {
            await fetch('/api/notification-permission/clear-tracking', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

        } catch (error) {

        }


        return true;
    } catch (error) {
        console.error('Error clearing notification cache:', error);
        return false;
    }
}

/**
 * Initialize Firebase messaging with permission (called from notification settings)
 */
async function initializeFirebaseMessagingWithPermission() {
    try {
        if (Notification.permission === 'granted') {
            await initializeFirebaseMessaging();
            const token = await getFCMToken();

            // Update FCM token in notification preferences
            if (token) {
                await fetch('/api/notification-preferences/fcm-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ token })
                });
            }

            return token;
        }
        return null;
    } catch (error) {
        console.error('Error initializing Firebase messaging with permission:', error);
        return null;
    }
}

// Export functions for global use
window.getNotificationPermission = getNotificationPermission;
window.isNotificationSupported = isNotificationSupported;
window.initializeFirebaseMessagingWithPermission = initializeFirebaseMessagingWithPermission;
window.clearNotificationCache = clearNotificationCache;

// Debug functions completely removed to prevent any popups

window.clearAllNotificationData = async function() {
    const result = await clearNotificationCache();
    return result;
};
