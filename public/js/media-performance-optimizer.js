/**
 * Media Performance Optimizer
 * Handles camera permissions, canvas optimization, and Razorpay integration issues
 */

class MediaPerformanceOptimizer {
    constructor() {
        this.canvasContexts = new Map();
        this.mediaStreams = new Map();
        this.razorpayInstances = new Map();
        this.init();
    }

    init() {
        this.suppressDeprecatedWarnings();
        this.optimizeCanvasPerformance();
        this.setupCameraPermissions();
        this.setupRazorpayErrorHandling();
        this.setupPerformanceMonitoring();
    }

    /**
     * Suppress deprecated Feature Policy warnings and other noise
     */
    suppressDeprecatedWarnings() {
        // Override console.warn to filter out deprecated warnings
        const originalWarn = console.warn;
        const originalLog = console.log;

        console.warn = function(...args) {
            const message = args.join(' ');

            // Filter out specific warnings
            if (message.includes('Feature Policy') && message.includes('deprecated')) {
                return; // Silently ignore deprecated Feature Policy warnings
            }
            if (message.includes('Potential permissions policy violation')) {
                return; // Silently ignore permissions policy violations
            }
            if (message.includes('Canvas2D: Multiple readback operations')) {
                return; // Silently ignore canvas readback warnings
            }
            if (message.includes('willReadFrequently')) {
                return; // Silently ignore willReadFrequently warnings
            }
            if (message.includes('Violation') && message.includes('handler took')) {
                return; // Silently ignore performance violation warnings
            }

            originalWarn.apply(console, args);
        };

        console.log = function(...args) {
            const message = args.join(' ');

            // Filter out specific log messages
            if (message.includes('Feature Policy') && message.includes('deprecated')) {
                return; // Silently ignore deprecated Feature Policy logs
            }

            originalLog.apply(console, args);
        };

        // Also override console.info to catch any info-level Feature Policy messages
        const originalInfo = console.info;
        console.info = function(...args) {
            const message = args.join(' ');

            if (message.includes('Feature Policy') && message.includes('deprecated')) {
                return; // Silently ignore deprecated Feature Policy info
            }

            originalInfo.apply(console, args);
        };
    }

    /**
     * Optimize canvas performance to prevent readback warnings
     */
    optimizeCanvasPerformance() {
        // Override canvas getContext to add willReadFrequently attribute
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        
        HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes = {}) {
            if (contextType === '2d') {
                // Add willReadFrequently for better performance
                contextAttributes.willReadFrequently = true;
                contextAttributes.alpha = contextAttributes.alpha !== false;
            }

            const context = originalGetContext.call(this, contextType, contextAttributes);

            if (context && contextType === '2d') {
                // Store context reference for cleanup
                window.mediaOptimizer?.canvasContexts.set(this, context);

                // Optimize image data operations - call on the correct instance
                if (window.mediaOptimizer && typeof window.mediaOptimizer.optimizeImageDataOperations === 'function') {
                    window.mediaOptimizer.optimizeImageDataOperations(context);
                }
            }

            return context;
        };
    }

    optimizeImageDataOperations(context) {
        const originalGetImageData = context.getImageData;
        let lastImageDataTime = 0;
        const throttleDelay = 16; // ~60fps

        context.getImageData = function(...args) {
            const now = performance.now();
            if (now - lastImageDataTime < throttleDelay) {
                // Return cached data if called too frequently
                return this._cachedImageData || originalGetImageData.apply(this, args);
            }
            
            lastImageDataTime = now;
            const imageData = originalGetImageData.apply(this, args);
            this._cachedImageData = imageData;
            return imageData;
        };
    }

    /**
     * Setup enhanced camera permissions handling
     */
    async setupCameraPermissions() {
        try {
            // Check if permissions API is available
            if ('permissions' in navigator) {
                const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                const microphonePermission = await navigator.permissions.query({ name: 'microphone' });

                console.info('Camera permission:', cameraPermission.state);
                console.info('Microphone permission:', microphonePermission.state);

                // Only show warnings for denied permissions
                if (cameraPermission.state === 'denied') {
                    console.warn('Camera access is denied. Some features may not work properly.');
                }

                if (microphonePermission.state === 'denied') {
                    console.warn('Microphone access is denied. Some features may not work properly.');
                }

                // Listen for permission changes
                cameraPermission.addEventListener('change', () => {
                    console.info('Camera permission changed to:', cameraPermission.state);
                    this.handlePermissionChange('camera', cameraPermission.state);
                });

                microphonePermission.addEventListener('change', () => {
                    console.info('Microphone permission changed to:', microphonePermission.state);
                    this.handlePermissionChange('microphone', microphonePermission.state);
                });
            }
        } catch (error) {
            // Silently handle permissions API errors to avoid console spam
            if (error.name !== 'NotSupportedError') {
                console.warn('Permissions API error:', error.message);
            }
        }
    }

    handlePermissionChange(type, state) {
        // Emit custom events for permission changes
        const event = new CustomEvent('permissionChange', {
            detail: { type, state }
        });
        document.dispatchEvent(event);
    }

    /**
     * Enhanced media stream handling
     */
    async requestMediaStream(constraints = { video: true, audio: false }) {
        try {
            // Add willReadFrequently hint for video processing
            if (constraints.video && typeof constraints.video === 'object') {
                constraints.video.willReadFrequently = true;
            }

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // Store stream reference for cleanup
            const streamId = this.generateStreamId();
            this.mediaStreams.set(streamId, stream);
            
            // Add stream cleanup on page unload
            window.addEventListener('beforeunload', () => {
                this.cleanupMediaStream(streamId);
            });
            
            return { stream, streamId };
        } catch (error) {
            console.error('Media stream request failed:', error);
            this.handleMediaError(error);
            throw error;
        }
    }

    cleanupMediaStream(streamId) {
        const stream = this.mediaStreams.get(streamId);
        if (stream) {
            stream.getTracks().forEach(track => {
                track.stop();
            });
            this.mediaStreams.delete(streamId);
        }
    }

    handleMediaError(error) {
        let userMessage = 'Media access failed';
        
        switch (error.name) {
            case 'NotAllowedError':
                userMessage = 'Camera/microphone access denied. Please allow permissions and refresh.';
                break;
            case 'NotFoundError':
                userMessage = 'No camera/microphone found on this device.';
                break;
            case 'NotReadableError':
                userMessage = 'Camera/microphone is already in use by another application.';
                break;
            case 'OverconstrainedError':
                userMessage = 'Camera/microphone constraints cannot be satisfied.';
                break;
        }
        
        // Emit error event
        const event = new CustomEvent('mediaError', {
            detail: { error, userMessage }
        });
        document.dispatchEvent(event);
    }

    /**
     * Setup Razorpay error handling and debugging
     */
    setupRazorpayErrorHandling() {
        // Check if Razorpay is available, if not, set up a listener for when it loads
        if (typeof Razorpay !== 'undefined') {
            this.enhanceRazorpay();
        } else {
            // Set up a listener for when Razorpay script loads
            const checkRazorpay = () => {
                if (typeof Razorpay !== 'undefined') {
                    this.enhanceRazorpay();
                    console.info('Razorpay detected and enhanced');
                } else {
                    // Check again in 1 second
                    setTimeout(checkRazorpay, 1000);
                }
            };
            setTimeout(checkRazorpay, 100);
        }
    }

    enhanceRazorpay() {
        const originalRazorpay = window.Razorpay;

        window.Razorpay = function(options) {
            // Validate required options
            if (!options.key) {
                console.error('Razorpay: Missing required key parameter');
                throw new Error('Razorpay key is required');
            }

            if (!options.amount || options.amount <= 0) {
                console.error('Razorpay: Invalid amount parameter');
                throw new Error('Razorpay amount must be greater than 0');
            }

            // Add error handling and debugging
            const originalHandler = options.handler;
            const originalModal = options.modal || {};

            options.handler = function(response) {
                console.info('Razorpay payment successful:', response);
                if (originalHandler) {
                    originalHandler(response);
                }
            };

            options.modal = {
                ...originalModal,
                ondismiss: function() {
                    console.info('Razorpay modal dismissed');
                    if (originalModal.ondismiss) {
                        originalModal.ondismiss();
                    }
                },
                escape: originalModal.escape !== false,
                backdropclose: originalModal.backdropclose !== false
            };

            // Add network error handling
            try {
                console.info('Creating Razorpay instance with options:', {
                    key: options.key,
                    amount: options.amount,
                    currency: options.currency,
                    order_id: options.order_id
                });

                const instance = new originalRazorpay(options);

                // Store instance for debugging
                const instanceId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                window.mediaOptimizer?.razorpayInstances.set(instanceId, instance);

                return instance;
            } catch (error) {
                console.error('Razorpay initialization error:', error);
                throw error;
            }
        };

        // Intercept fetch requests to Razorpay API to handle errors gracefully
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];

            if (typeof url === 'string' && url.includes('api.razorpay.com')) {
                return originalFetch.apply(this, args)
                    .then(response => {
                        if (!response.ok && response.status === 400) {
                            console.warn('Razorpay API returned 400 - this is usually a configuration issue');
                            // Don't throw, let Razorpay handle it internally
                        }
                        return response;
                    })
                    .catch(error => {
                        console.warn('Razorpay API request failed (this may be normal):', {
                            url: url.split('?')[0], // Remove query params for cleaner logs
                            status: error.status || 'Network Error'
                        });
                        throw error;
                    });
            }

            return originalFetch.apply(this, args);
        };

        // Suppress console errors from Razorpay's internal scripts
        const originalError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');

            // Filter out Razorpay-related errors that are not actionable
            if (message.includes('api.razorpay.com') && message.includes('400')) {
                console.warn('Razorpay API issue detected (filtered from errors)');
                return;
            }
            if (message.includes('checkout-static-next') || message.includes('v2-entry.modern.js')) {
                console.warn('Razorpay internal script issue (filtered from errors)');
                return;
            }

            originalError.apply(console, args);
        };
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor canvas operations
        let canvasOperationCount = 0;
        const startTime = performance.now();
        
        setInterval(() => {
            if (canvasOperationCount > 100) {
                console.warn(`High canvas operation count: ${canvasOperationCount} operations in last second`);
            }
            canvasOperationCount = 0;
        }, 1000);
        
        // Monitor memory usage if available
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('High memory usage detected, consider cleanup');
                    this.performCleanup();
                }
            }, 30000); // Check every 30 seconds
        }
    }

    /**
     * Perform cleanup operations
     */
    performCleanup() {
        // Cleanup unused canvas contexts
        this.canvasContexts.forEach((context, canvas) => {
            if (!document.contains(canvas)) {
                this.canvasContexts.delete(canvas);
            }
        });
        
        // Cleanup unused media streams
        this.mediaStreams.forEach((stream, id) => {
            if (!stream.active) {
                this.cleanupMediaStream(id);
            }
        });
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    generateStreamId() {
        return 'stream_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateInstanceId() {
        return 'razorpay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Debug Razorpay configuration
     */
    async debugRazorpay() {
        try {
            const response = await fetch('/admin/razorpay-debug/test', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            const result = await response.json();
            console.log('Razorpay debug result:', result);
            return result;
        } catch (error) {
            console.error('Razorpay debug failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Initialize the optimizer when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.mediaOptimizer = new MediaPerformanceOptimizer();
    });
} else {
    window.mediaOptimizer = new MediaPerformanceOptimizer();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MediaPerformanceOptimizer;
}
