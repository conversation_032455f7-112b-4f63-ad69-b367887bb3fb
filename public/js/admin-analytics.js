/**
 * Admin Analytics Dashboard JavaScript
 * Handles chart rendering and data updates
 */

class AdminAnalytics {
    constructor(options = {}) {
        this.options = options;
        this.charts = {};
        this.currentFilter = options.currentFilter || 'month';
        this.startDate = options.startDate;
        this.endDate = options.endDate;
        
        this.init();
    }

    init() {
        try {
            this.setupEventListeners();
            this.initializeCharts();
            this.updateDateInputs();
        } catch (error) {
            // Silent error handling for production
        }
    }

    setupEventListeners() {
        // Time filter buttons
        document.querySelectorAll('.time-filter').forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleTimeFilter(e.target.dataset.filter);
            });
        });

        // Custom date filter
        document.getElementById('customFilter')?.addEventListener('click', () => {
            this.handleCustomFilter();
        });

        // Export button
        document.getElementById('exportData')?.addEventListener('click', () => {
            this.exportData();
        });
    }

    handleTimeFilter(filter) {
        // Update active button
        document.querySelectorAll('.time-filter').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        });
        
        document.querySelector(`[data-filter="${filter}"]`).classList.remove('btn-outline-primary');
        document.querySelector(`[data-filter="${filter}"]`).classList.add('btn-primary');

        this.currentFilter = filter;
        this.loadData(filter);
    }

    handleCustomFilter() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!startDate || !endDate) {
            this.showToast('Please select both start and end dates', 'warning');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            this.showToast('Start date cannot be after end date', 'error');
            return;
        }

        // Update active button
        document.querySelectorAll('.time-filter').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        });

        this.currentFilter = 'custom';
        this.startDate = startDate;
        this.endDate = endDate;
        this.loadData('custom', startDate, endDate);
    }

    async loadData(filter, startDate = null, endDate = null) {
        this.showLoading(true);

        try {
            const params = new URLSearchParams({
                filter: filter
            });

            if (startDate && endDate) {
                params.append('start_date', startDate);
                params.append('end_date', endDate);
            }

            // Load overview data
            const overviewResponse = await fetch(`/admin/analytics-data?type=overview&${params}`);
            const overviewData = await overviewResponse.json();
            this.updateOverviewStats(overviewData);

            // Load user registration data
            const userResponse = await fetch(`/admin/analytics-data?type=user_registrations&${params}`);
            const userData = await userResponse.json();
            this.updateUserRegistrationChart(userData);

            // Load revenue data
            const revenueResponse = await fetch(`/admin/analytics-data?type=revenue&${params}`);
            const revenueData = await revenueResponse.json();
            this.updateRevenueCharts(revenueData);

            // Load active subscription data
            const subscriptionResponse = await fetch(`/admin/analytics-data?type=active_subscriptions&${params}`);
            const subscriptionData = await subscriptionResponse.json();
            this.updateActiveSubscriptionChart(subscriptionData);

            // Load feature usage data
            const featureResponse = await fetch(`/admin/analytics-data?type=feature_usage&${params}`);
            const featureData = await featureResponse.json();
            this.updateFeatureStats(featureData);

        } catch (error) {
            this.showToast('Failed to load analytics data', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    initializeCharts() {
        try {
            this.initRevenueChart();
            this.initUserRegistrationChart();
            this.initActiveSubscriptionChart();
            this.initRevenueSourceChart();
        } catch (error) {
            // Error handled
        }
    }

    initUserRegistrationChart() {
        const ctx = document.getElementById('userRegistrationChart');
        if (!ctx || typeof Chart === 'undefined') return;

        const registrationData = this.options.userRegistrationData || {};

        this.charts.userRegistration = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: registrationData.labels || ['Female', 'Male'],
                datasets: [{
                    data: registrationData.data || [0, 0],
                    backgroundColor: registrationData.colors || ['#8B5CF6', '#3B82F6'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    initRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx || typeof Chart === 'undefined') return;

        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.options.revenueData?.timeline?.labels || [],
                datasets: [{
                    label: 'Revenue (₹)',
                    data: this.options.revenueData?.timeline?.data || [],
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderColor: '#3B82F6',
                    borderWidth: 3,
                    pointBackgroundColor: '#3B82F6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: ₹' + context.parsed.y.toLocaleString('en-IN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            },
                            color: '#6B7280',
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#3B82F6',
                        hoverBorderColor: '#ffffff'
                    }
                }
            }
        });
    }

    initActiveSubscriptionChart() {
        const ctx = document.getElementById('activeSubscriptionChart');
        if (!ctx || typeof Chart === 'undefined') return;

        const subscriptionData = this.options.activeSubscriptionData || {};

        this.charts.activeSubscription = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: subscriptionData.labels || ['3 Months', '6 Months', '12 Months'],
                datasets: [{
                    data: subscriptionData.data || [0, 0, 0],
                    backgroundColor: subscriptionData.colors || ['#10B981', '#F59E0B', '#8B5CF6'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    initRevenueSourceChart() {
        const ctx = document.getElementById('revenueSourceChart');
        if (!ctx || typeof Chart === 'undefined') return;

        const revenueBySource = this.options.revenueData?.by_source || {};

        this.charts.revenueSource = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: revenueBySource.labels || ['Subscription Revenue', 'Booking Commission', 'Platform Fees', 'Event Revenue'],
                datasets: [{
                    data: revenueBySource.data || [0, 0, 0, 0],
                    backgroundColor: revenueBySource.colors || ['#8B5CF6', '#F59E0B', '#007bff', '#10B981'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return context.label + ': ₹' + value.toLocaleString('en-IN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    updateOverviewStats(data) {
        // Update overview cards
        document.getElementById('totalUsers').textContent = data.total_users?.toLocaleString() || '0';
        document.getElementById('totalEvents').textContent = data.total_events?.toLocaleString() || '0';
        document.getElementById('totalRevenue').textContent = '₹' + (data.total_revenue?.toLocaleString() || '0');
        document.getElementById('totalPlatformFees').textContent = '₹' + (data.total_platform_fees?.toLocaleString() || '0');
        document.getElementById('activeSubscriptions').textContent = data.active_subscriptions?.toLocaleString() || '0';

        // Update feature usage cards
        document.getElementById('totalBookings').textContent = data.total_bookings?.toLocaleString() || '0';
        document.getElementById('eventParticipants').textContent = data.event_participants?.toLocaleString() || '0';
        document.getElementById('chatMessages').textContent = data.chat_messages?.toLocaleString() || '0';
        document.getElementById('totalActiveUsers').textContent = data.total_users?.toLocaleString() || '0';
    }

    updateUserRegistrationChart(data) {
        if (this.charts.userRegistration) {
            this.charts.userRegistration.data.labels = data.labels || ['No Data'];
            this.charts.userRegistration.data.datasets[0].data = data.data || [1];
            if (data.colors) {
                this.charts.userRegistration.data.datasets[0].backgroundColor = data.colors;
            }
            this.charts.userRegistration.update();
        }
    }

    updateRevenueCharts(data) {
        if (this.charts.revenue) {
            this.charts.revenue.data.labels = data.timeline?.labels || [];
            this.charts.revenue.data.datasets[0].data = data.timeline?.data || [];
            this.charts.revenue.update();
        }

        if (this.charts.revenueSource) {
            const revenueBySource = data.by_source || {};
            this.charts.revenueSource.data.labels = revenueBySource.labels || ['Subscription Revenue', 'Booking Commission', 'Platform Fees', 'Event Revenue'];
            this.charts.revenueSource.data.datasets[0].data = revenueBySource.data || [0, 0, 0, 0];
            if (revenueBySource.colors) {
                this.charts.revenueSource.data.datasets[0].backgroundColor = revenueBySource.colors;
            }
            this.charts.revenueSource.update();
        }
    }

    updateActiveSubscriptionChart(data) {
        if (this.charts.activeSubscription) {
            this.charts.activeSubscription.data.labels = data.labels || ['No Data'];
            this.charts.activeSubscription.data.datasets[0].data = data.data || [1];
            if (data.colors) {
                this.charts.activeSubscription.data.datasets[0].backgroundColor = data.colors;
            }
            this.charts.activeSubscription.update();
        }
    }

    updateFeatureStats() {
        // Update feature usage statistics if needed
    }

    updateDateInputs() {
        if (this.startDate) {
            document.getElementById('startDate').value = this.startDate;
        }
        if (this.endDate) {
            document.getElementById('endDate').value = this.endDate;
        }
    }

    async exportData() {
        try {
            const params = new URLSearchParams({
                filter: this.currentFilter
            });

            if (this.startDate && this.endDate) {
                params.append('start_date', this.startDate);
                params.append('end_date', this.endDate);
            }

            const response = await fetch(`/admin/analytics-export?${params}`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics_${this.currentFilter}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showToast('Analytics data exported successfully', 'success');
        } catch (error) {
            this.showToast('Failed to export data', 'error');
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.toggle('d-none', !show);
        }
    }

    showToast(message, type = 'info') {
        // Simple toast implementation
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}
