<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Fixes Test Page</title>
    <meta http-equiv="Permissions-Policy" content="camera=*, microphone=*, geolocation=*, display-capture=*">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        video {
            border: 1px solid #ccc;
            margin: 10px 0;
            max-width: 100%;
        }
    </style>
</head>
<body>
    <h1>Console Fixes Test Page</h1>
    <p>This page tests the fixes for camera permissions, canvas performance, and Razorpay integration.</p>

    <!-- Camera Permissions Test -->
    <div class="test-section">
        <h2>Camera Permissions Test</h2>
        <p>Test camera access and permissions policy.</p>
        <button class="test-button" onclick="testCameraPermissions()">Test Camera Access</button>
        <button class="test-button" onclick="stopCamera()">Stop Camera</button>
        <div id="camera-status" class="status info">Ready to test camera permissions</div>
        <video id="camera-video" width="320" height="240" autoplay muted style="display: none;"></video>
    </div>

    <!-- Canvas Performance Test -->
    <div class="test-section">
        <h2>Canvas Performance Test</h2>
        <p>Test canvas operations with willReadFrequently optimization.</p>
        <button class="test-button" onclick="testCanvasPerformance()">Test Canvas Performance</button>
        <button class="test-button" onclick="stopCanvasTest()">Stop Test</button>
        <div id="canvas-status" class="status info">Ready to test canvas performance</div>
        <canvas id="test-canvas" width="320" height="240" style="display: none;"></canvas>
    </div>

    <!-- Razorpay Test -->
    <div class="test-section">
        <h2>Razorpay Integration Test</h2>
        <p>Test Razorpay configuration and error handling.</p>
        <button class="test-button" onclick="testRazorpayConfig()">Test Razorpay Config</button>
        <button class="test-button" onclick="testRazorpayModal()">Test Payment Modal</button>
        <div id="razorpay-status" class="status info">Ready to test Razorpay integration</div>
        <div id="razorpay-details" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
    </div>

    <!-- Console Output -->
    <div class="test-section">
        <h2>Console Output</h2>
        <p>Monitor console messages and errors.</p>
        <button class="test-button" onclick="clearConsoleOutput()">Clear Output</button>
        <div id="console-output"></div>
    </div>

    <!-- Load the media performance optimizer -->
    <script src="js/media-performance-optimizer.js"></script>
    
    <script>
        let cameraStream = null;
        let canvasTestInterval = null;
        let consoleMessages = [];

        // Override console methods to capture output
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        const originalInfo = console.info;

        function addToConsoleOutput(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] [${type.toUpperCase()}] ${args.join(' ')}`;
            consoleMessages.push(message);
            updateConsoleDisplay();
        }

        console.log = function(...args) {
            addToConsoleOutput('log', ...args);
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            addToConsoleOutput('warn', ...args);
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            addToConsoleOutput('error', ...args);
            originalError.apply(console, args);
        };

        console.info = function(...args) {
            addToConsoleOutput('info', ...args);
            originalInfo.apply(console, args);
        };

        function updateConsoleDisplay() {
            const output = document.getElementById('console-output');
            output.textContent = consoleMessages.slice(-20).join('\n');
            output.scrollTop = output.scrollHeight;
        }

        function clearConsoleOutput() {
            consoleMessages = [];
            updateConsoleDisplay();
        }

        async function testCameraPermissions() {
            const statusEl = document.getElementById('camera-status');
            const videoEl = document.getElementById('camera-video');

            try {
                statusEl.textContent = 'Requesting camera access...';
                statusEl.className = 'status info';

                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { facingMode: 'user' }
                });

                cameraStream = stream;
                videoEl.srcObject = stream;
                videoEl.style.display = 'block';

                statusEl.textContent = 'Camera access granted successfully!';
                statusEl.className = 'status success';

                console.log('Camera test: Access granted');

            } catch (error) {
                statusEl.textContent = `Camera access failed: ${error.message}`;
                statusEl.className = 'status error';
                console.error('Camera test failed:', error);
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                
                const videoEl = document.getElementById('camera-video');
                videoEl.style.display = 'none';
                videoEl.srcObject = null;

                const statusEl = document.getElementById('camera-status');
                statusEl.textContent = 'Camera stopped';
                statusEl.className = 'status info';

                console.log('Camera test: Stream stopped');
            }
        }

        function testCanvasPerformance() {
            const statusEl = document.getElementById('canvas-status');
            const canvasEl = document.getElementById('test-canvas');
            
            canvasEl.style.display = 'block';
            const ctx = canvasEl.getContext('2d', { willReadFrequently: true });

            statusEl.textContent = 'Running canvas performance test...';
            statusEl.className = 'status info';

            let frameCount = 0;
            const startTime = performance.now();

            canvasTestInterval = setInterval(() => {
                // Draw some test content
                ctx.fillStyle = `hsl(${frameCount % 360}, 50%, 50%)`;
                ctx.fillRect(0, 0, canvasEl.width, canvasEl.height);
                
                // Test getImageData (this should not cause warnings now)
                const imageData = ctx.getImageData(0, 0, 100, 100);
                
                frameCount++;

                if (frameCount >= 60) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    statusEl.textContent = `Canvas test completed: ${frameCount} frames in ${duration.toFixed(2)}ms`;
                    statusEl.className = 'status success';
                    
                    clearInterval(canvasTestInterval);
                    canvasTestInterval = null;
                    
                    console.log(`Canvas test: ${frameCount} frames, ${duration.toFixed(2)}ms total`);
                }
            }, 16); // ~60fps
        }

        function stopCanvasTest() {
            if (canvasTestInterval) {
                clearInterval(canvasTestInterval);
                canvasTestInterval = null;
                
                const statusEl = document.getElementById('canvas-status');
                statusEl.textContent = 'Canvas test stopped';
                statusEl.className = 'status info';
                
                console.log('Canvas test: Stopped by user');
            }
        }

        async function testRazorpayConfig() {
            const statusEl = document.getElementById('razorpay-status');

            try {
                statusEl.textContent = 'Testing Razorpay configuration...';
                statusEl.className = 'status info';

                // Test using the public status endpoint
                const response = await fetch('/api/razorpay-status', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    statusEl.textContent = `Razorpay test: ${result.message}`;
                    statusEl.className = result.success ? 'status success' : 'status error';

                    console.log('Razorpay test result:', result);

                    if (result.status) {
                        console.log('Configuration details:', result.status);

                        // Update details display
                        const detailsEl = document.getElementById('razorpay-details');
                        detailsEl.innerHTML = `
                            Environment: ${result.status.environment || 'Unknown'}<br>
                            Key Format: ${result.status.key_format || 'Unknown'}<br>
                            Configured: ${result.status.configured ? 'Yes' : 'No'}
                        `;
                    }
                } else {
                    const errorText = await response.text();
                    statusEl.textContent = `Razorpay test: HTTP ${response.status} - ${response.statusText}`;
                    statusEl.className = 'status error';
                    console.warn('Razorpay test: HTTP error', response.status, errorText);
                }

            } catch (error) {
                statusEl.textContent = `Razorpay test failed: ${error.message}`;
                statusEl.className = 'status error';
                console.error('Razorpay test error:', error);
            }
        }

        function testRazorpayModal() {
            const statusEl = document.getElementById('razorpay-status');

            try {
                // Check if Razorpay is loaded
                if (typeof Razorpay === 'undefined') {
                    statusEl.textContent = 'Razorpay script not loaded. Loading from CDN...';
                    statusEl.className = 'status info';

                    // Load Razorpay script
                    const script = document.createElement('script');
                    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
                    script.onload = function() {
                        statusEl.textContent = 'Razorpay script loaded successfully!';
                        statusEl.className = 'status success';
                        console.log('Razorpay script loaded');
                    };
                    script.onerror = function() {
                        statusEl.textContent = 'Failed to load Razorpay script';
                        statusEl.className = 'status error';
                        console.error('Failed to load Razorpay script');
                    };
                    document.head.appendChild(script);
                } else {
                    statusEl.textContent = 'Razorpay script is already loaded and available';
                    statusEl.className = 'status success';
                    console.log('Razorpay is available:', typeof Razorpay);
                }

            } catch (error) {
                statusEl.textContent = `Razorpay modal test failed: ${error.message}`;
                statusEl.className = 'status error';
                console.error('Razorpay modal test error:', error);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Console fixes test page loaded');
            console.log('Media performance optimizer should be active');
            
            // Test if the optimizer is working
            if (window.mediaOptimizer) {
                console.log('Media optimizer detected and active');
            } else {
                console.warn('Media optimizer not detected');
            }
        });
    </script>
</body>
</html>
