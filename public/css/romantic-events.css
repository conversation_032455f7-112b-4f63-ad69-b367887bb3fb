/* SettingWala Dating App - Romantic Theme CSS */

/* CSS Variables for Romantic Theme */
:root {
    --rose-pink: #4F46E5;
    --blush-pink: #E0E7FF;
    --warm-peach: #F3F4F6;
    --ivory: #FFF8F1;
    --charcoal: #333333;
    --soft-gray: #666666;
    --white: #FFFFFF;
    --header-primary: #4F46E5;
    --header-secondary: #6366F1;
    --header-light: #E0E7FF;
    --gradient-romantic: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
    --gradient-soft: linear-gradient(135deg, #E0E7FF 0%, #F3F4F6 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
    --gradient-header: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
    --gradient-header-light: linear-gradient(135deg, #E0E7FF 0%, #F3F4F6 100%);
    --shadow-soft: 0 4px 20px rgba(79, 70, 229, 0.15);
    --shadow-hover: 0 8px 30px rgba(79, 70, 229, 0.25);
    --shadow-header: 0 4px 20px rgba(79, 70, 229, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* ===== TAILWIND CUSTOM STYLES ===== */
/* Heart animations removed */

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse-soft {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
}

.animate-pulse-soft {
    animation: pulse-soft 2s ease-in-out infinite;
}

/* Payment Button Enhancements */
.payment-button-shimmer {
    position: relative;
    overflow: hidden;
}

.payment-button-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.payment-button-shimmer:hover::before {
    left: 100%;
}

/* Custom gradient backgrounds */
.bg-romantic-gradient {
    background: linear-gradient(135deg, #FF5C8D 0%, #FF8A80 100%);
}

.bg-soft-gradient {
    background: linear-gradient(135deg, #FFE4E1 0%, #FFDAB9 100%);
}

/* Hover effects for cards */
.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(255, 92, 141, 0.2);
}

/* Custom container for better responsive design */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container {
        padding: 0 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 4rem;
    }
}

/* ===== ROMANTIC HEADER STYLES ===== */
.romantic-header {
    background: white;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.romantic-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(79, 70, 229, 0.02) 0%, transparent 100%);
    pointer-events: none;
}

/* Logo Styles */
.romantic-logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.romantic-logo-img {
    height: 60px;
    width: auto;
    border-radius: 8px;
    transition: var(--transition);
}

.romantic-logo-img:hover {
    transform: scale(1.05);
}

.romantic-logo-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-header);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: var(--shadow-header);
}

.romantic-logo-icon:hover {
    background: linear-gradient(135deg, #3730A3 0%, #4338CA 100%);
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(79, 70, 229, 0.25);
}

/* Brand Text */
.romantic-brand-text {
    color: var(--charcoal);
}

.romantic-brand-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    color: var(--charcoal);
}

.romantic-brand-subtitle {
    font-family: 'Nunito', sans-serif;
    font-size: 0.875rem;
    opacity: 0.7;
    margin: 0;
    line-height: 1;
    color: var(--soft-gray);
}

/* Navigation Links */
.romantic-nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    color: var(--soft-gray);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
    font-size: 0.9rem;
}

.romantic-nav-link:hover {
    background: var(--header-light);
    color: var(--header-primary);
    transform: translateY(-1px);
    text-decoration: none;
}

.romantic-nav-link-active {
    background: var(--header-light);
    color: var(--header-primary);
    font-weight: 600;
}

/* Profile Button */
.romantic-profile-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 18px;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(99, 102, 241, 0.08) 100%);
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 25px;
    color: var(--charcoal);
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.1);
    position: relative;
    overflow: hidden;
}

.romantic-profile-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.romantic-profile-btn:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.12) 0%, rgba(99, 102, 241, 0.12) 100%);
    border-color: rgba(79, 70, 229, 0.4);
    color: var(--header-primary);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(79, 70, 229, 0.2);
}

.romantic-profile-btn:hover::before {
    left: 0;
}

.romantic-profile-active {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15) 0%, rgba(99, 102, 241, 0.15) 100%);
    border-color: rgba(79, 70, 229, 0.5);
    color: var(--header-primary);
    font-weight: 600;
    box-shadow: 0 6px 25px rgba(79, 70, 229, 0.2);
}

.romantic-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(79, 70, 229, 0.3);
}

.romantic-avatar-fallback {
    width: 32px;
    height: 32px;
    background: var(--gradient-header);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.romantic-profile-name {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--charcoal);
}

/* Dropdown Menu */
.romantic-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(79, 70, 229, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.1);
    min-width: 220px;
    overflow: hidden;
    z-index: 1000;
    backdrop-filter: blur(10px);
    transform-origin: top right;
    animation: dropdownFadeIn 0.3s ease-out;
}

/* Dropdown Animation */
@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.romantic-dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 18px;
    color: var(--charcoal);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 0.9rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.romantic-dropdown-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--header-primary) 0%, var(--header-secondary) 100%);
    transition: width 0.3s ease;
    z-index: -1;
}

.romantic-dropdown-item:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(99, 102, 241, 0.08) 100%);
    color: var(--header-primary);
    text-decoration: none;
    transform: translateX(4px);
}

.romantic-dropdown-item:hover::before {
    width: 4px;
}

.romantic-dropdown-active {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.12) 0%, rgba(99, 102, 241, 0.12) 100%);
    color: var(--header-primary);
    font-weight: 600;
}

.romantic-dropdown-active::before {
    width: 4px;
}

.romantic-dropdown-danger:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(220, 38, 38, 0.08) 100%);
    color: #dc2626;
}

.romantic-dropdown-danger:hover::before {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    width: 4px;
}

.romantic-dropdown-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(79, 70, 229, 0.2) 50%, transparent 100%);
    margin: 8px 16px;
}

/* Dropdown Icon Styling */
.romantic-dropdown-item i,
.romantic-dropdown-item svg {
    transition: all 0.3s ease;
    opacity: 0.7;
}

.romantic-dropdown-item:hover i,
.romantic-dropdown-item:hover svg {
    opacity: 1;
    transform: scale(1.1);
}

.romantic-dropdown-active i,
.romantic-dropdown-active svg {
    opacity: 1;
    color: var(--header-primary);
}

/* Special styling for danger item */
.romantic-dropdown-danger i,
.romantic-dropdown-danger svg {
    color: #dc2626;
}

.romantic-dropdown-danger:hover i,
.romantic-dropdown-danger:hover svg {
    color: #b91c1c;
    transform: scale(1.1);
}

/* Notification Button */
.romantic-notification-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    color: var(--header-primary);
    text-decoration: none;
    transition: var(--transition);
}

.romantic-notification-btn:hover {
    background: rgba(79, 70, 229, 0.15);
    border-color: rgba(79, 70, 229, 0.3);
    transform: translateY(-1px);
    color: var(--header-primary);
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.romantic-notification-active {
    background: rgba(79, 70, 229, 0.2);
    border-color: rgba(79, 70, 229, 0.4);
}

/* Mobile Toggle */
.romantic-mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 8px;
    color: var(--header-primary);
    cursor: pointer;
    transition: var(--transition);
}

.romantic-mobile-toggle:hover {
    background: rgba(79, 70, 229, 0.15);
    border-color: rgba(79, 70, 229, 0.3);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

/* Header Login Button */
.header-login-btn {
    background: var(--gradient-header);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: var(--shadow-header);
}

.header-login-btn:hover {
    background: linear-gradient(135deg, #3730A3 0%, #4338CA 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(79, 70, 229, 0.25);
    color: white;
    text-decoration: none;
}

/* Mobile Menu */
.mobile-menu {
    background: white;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.mobile-menu-content {
    padding: 16px;
}

/* Notification Badge - High Specificity to Override Tailwind */
.romantic-header .notification-badge,
#notification-badge,
#mobile-notification-badge {
    position: absolute !important;
    top: -6px !important;
    right: -6px !important;
    background: #dc2626 !important;
    color: white !important;
    border-radius: 50% !important;
    min-width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
    font-weight: 700 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid white !important;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4) !important;
    z-index: 1000 !important;
    line-height: 1 !important;
    text-align: center !important;
}

.romantic-header .notification-badge.show,
#notification-badge.show,
#mobile-notification-badge.show {
    display: flex !important;
}

.romantic-header .notification-badge.has-notifications,
#notification-badge.has-notifications,
#mobile-notification-badge.has-notifications {
    animation: notification-pulse 2s ease-in-out infinite !important;
    background: #dc2626 !important;
}

@keyframes notification-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    }
    50% {
        transform: scale(1.15);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.6);
    }
}

/* Mobile notification badge adjustments */
.mobile-nav-icon .notification-badge {
    top: -4px;
    right: -4px;
    min-width: 18px;
    height: 18px;
    font-size: 11px;
}

.mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--charcoal);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-bottom: 4px;
}

.mobile-nav-item:hover {
    background: var(--header-light);
    color: var(--header-primary);
    text-decoration: none;
}

.mobile-nav-item-active {
    background: var(--header-light);
    color: var(--header-primary);
    font-weight: 600;
}

.mobile-nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-nav-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== ROMANTIC FOOTER STYLES ===== */
.romantic-footer {
    background: linear-gradient(135deg, var(--charcoal) 0%, #2d2d2d 100%);
    color: white;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.romantic-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    /* Heart pattern removed */
    opacity: 0.3;
    pointer-events: none;
}

.romantic-footer .max-w-7xl {
    max-width: 1200px;
    position: relative;
    z-index: 1;
}

/* Footer Brand */
.romantic-footer-brand {
    margin-bottom: 24px;
}

.romantic-footer-logo {
    max-height: 60px;
    width: auto;
}

.romantic-footer-logo-default {
    display: flex;
    align-items: center;
    gap: 12px;
}

.romantic-footer-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-romantic);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.romantic-footer-brand-text {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
}

.romantic-footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 24px;
    font-size: 0.9rem;
}

/* Footer Columns */
.romantic-footer-column {
    margin-bottom: 0;
}

.romantic-footer-column-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
    position: relative;
}

.romantic-footer-column-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gradient-romantic);
    border-radius: 1px;
}

/* Footer Links */
.romantic-footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-top: 20px;
}

.romantic-footer-links li {
    margin-bottom: 14px;
}

.romantic-footer-link {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
    padding: 4px 0;
    border-radius: 4px;
}

.romantic-footer-link:hover {
    color: var(--header-primary);
    text-decoration: none;
    transform: translateX(6px);
    background: rgba(79, 70, 229, 0.1);
    padding-left: 8px;
}

/* Social Media */
.romantic-footer-social {
    margin-top: 24px;
}

.romantic-footer-social-title {
    font-size: 1rem;
    font-weight: 600;
    color: white;
    margin-bottom: 16px;
}

.romantic-footer-social-links {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.romantic-footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.romantic-footer-social-link:hover {
    background: var(--gradient-romantic);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

/* Footer Bottom */
.romantic-footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 32px;
}

.romantic-footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 24px 0;
}

.romantic-footer-copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.romantic-footer-badges {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.romantic-footer-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    font-weight: 500;
}

.romantic-footer-badge:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* ===== ROMANTIC CARDS ===== */
.romantic-card {
    background: white;
    border-radius: 16px;
    border: 1px solid rgba(236, 72, 153, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.romantic-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(79, 70, 229, 0.2);
    border-color: var(--header-primary);
}

.romantic-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.romantic-card:hover::before {
    left: 100%;
}

.romantic-card .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.romantic-card .card-title {
    font-family: 'Playfair Display', serif;
    color: var(--charcoal);
    font-weight: 600;
}

.romantic-card .badge {
    font-size: 0.85rem;
    padding: 6px 16px;
    border-radius: 20px;
    font-weight: 600;
}

.event-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(79, 70, 229, 0.1);
    overflow: hidden;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);
    border-color: var(--header-primary);
}

.event-card .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Text Colors */
.text-romantic {
    color: var(--rose-pink) !important;
}

/* Heart Float Animation - Disabled */
.heart-float {
    /* animation removed */
}

/* Heart animations removed */
}

/* Event Meta Items */
.event-meta-item {
    background: var(--blush-pink);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.event-meta-item:hover {
    background: rgba(236, 72, 153, 0.15);
}

/* Badge Styles */
.badge-romantic {
    background: linear-gradient(135deg, #ec4899 0%, #dc2626 100%);
    color: white;
}

.badge-couple {
    background: linear-gradient(135deg, #db2777 0%, #7c3aed 100%);
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

/* Alert Styles */
.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #1e40af;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: #92400e;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem !important;
    }

    .display-5 {
        font-size: 2rem !important;
    }

    .event-card {
        margin-bottom: 1rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .hero-stats {
        margin-bottom: 2rem !important;
    }

    .hero-stats .col-md-4 {
        margin-bottom: 1rem;
    }

    .romantic-card {
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 1.25rem !important;
    }
}

/* ===== ROMANTIC BUTTONS ===== */
.btn-romantic {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--gradient-romantic);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.btn-romantic:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    color: white;
    text-decoration: none;
}

.btn-romantic-outline {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: transparent;
    color: var(--rose-pink);
    text-decoration: none;
    border: 2px solid var(--rose-pink);
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.btn-romantic-outline:hover {
    background: var(--rose-pink);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

/* ===== ANIMATIONS ===== */
/* Heart animations removed */

@keyframes romantic-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes romantic-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 92, 141, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 92, 141, 0.6);
    }
}

.heart-float {
    /* animation removed */
}

.heart-beat {
    /* animation removed */
}

.romantic-pulse {
    animation: romantic-pulse 2s ease-in-out infinite;
}

.romantic-glow {
    animation: romantic-glow 2s ease-in-out infinite;
}

/* Floating Hearts Background - Disabled */
.floating-hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 0;
}

/* Heart animations removed */

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .romantic-header {
        padding: 0;
    }

    .romantic-brand-title {
        font-size: 1.25rem;
    }

    .romantic-brand-subtitle {
        font-size: 0.75rem;
    }

    .romantic-logo-img {
        height: 50px;
    }

    .romantic-logo-icon {
        width: 50px;
        height: 50px;
    }

    .romantic-footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .romantic-footer-badges {
        justify-content: center;
    }

    .romantic-footer-social-links {
        justify-content: center;
    }

    .btn-romantic,
    .btn-romantic-outline {
        padding: 10px 20px;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .romantic-brand-text {
        display: none;
    }

    .romantic-footer-column {
        text-align: center;
        margin-bottom: 32px;
    }

    .romantic-footer-column-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .romantic-footer-description {
        text-align: center;
    }

    .romantic-footer-brand {
        text-align: center;
        margin-bottom: 32px;
    }

    .romantic-footer-social {
        text-align: center;
    }
}

/* ===== UTILITY CLASSES ===== */
.shadow-soft {
    box-shadow: var(--shadow-soft);
}

.shadow-hover {
    box-shadow: var(--shadow-hover);
}

.gradient-romantic {
    background: var(--gradient-romantic);
}

.gradient-soft {
    background: var(--gradient-soft);
}

.text-romantic {
    color: var(--rose-pink);
}

.bg-romantic {
    background-color: var(--rose-pink);
}

.border-romantic {
    border-color: var(--rose-pink);
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-header);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--header-primary);
}

/* ===== LANDING PAGE STYLES ===== */

/* Brand Colors for Landing Page */
:root {
    --brand-primary: #4F46E5;
    --brand-secondary: #6366F1;
    --brand-light: #F8FAFC;
    --primary: #1F2937;
    --secondary: #6B7280;
}

/* Hero Section */
.hero-section {
    background: #ffffff;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(99, 102, 241, 0.02) 100%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    color: var(--primary);
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Buttons */
.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
    color: white;
    text-decoration: none;
    background: linear-gradient(135deg, #3730A3 0%, #4338CA 100%);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    background: transparent;
    color: #4F46E5;
    text-decoration: none;
    border: 2px solid #4F46E5;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-secondary:hover {
    background: #4F46E5;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* Cards */
.modern-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.feature-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    text-align: center;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--secondary);
    line-height: 1.6;
    text-align: center;
}

/* Step Cards */
.step-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

/* Value Cards */
.value-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.value-card.featured {
    border: 2px solid #4F46E5;
    transform: scale(1.05);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.value-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.value-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* CTA Section */
.cta-section {
    background: #ffffff;
    position: relative;
}

.cta-card {
    background: white;
    border-radius: 30px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Text Colors */
.text-brand-primary {
    color: var(--brand-primary);
}

.text-brand-secondary {
    color: var(--brand-secondary);
}

.text-primary {
    color: var(--primary);
}

.text-secondary {
    color: var(--secondary);
}

/* ===== ANIMATIONS FOR LANDING PAGE ===== */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse-soft {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

/* ===== HERO SECTION ANIMATIONS ===== */
@keyframes step-card-entrance {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes badge-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(239, 68, 68, 0.5);
    }
}

@keyframes icon-hover {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
}

@keyframes arrow-flow {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: -20;
    }
}

@keyframes svg-character-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

@keyframes svg-phone-glow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(79, 70, 229, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 15px rgba(79, 70, 229, 0.6));
    }
}

@keyframes svg-activity-wiggle {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(-2deg);
    }
    75% {
        transform: rotate(2deg);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
}

.animate-slide-up {
    animation: slide-up 0.8s ease-out forwards;
}

.animate-pulse-soft {
    animation: pulse-soft 3s ease-in-out infinite;
}

.animate-delay-1 {
    animation-delay: 0.2s;
}

.animate-delay-2 {
    animation-delay: 0.4s;
}

.animate-delay-3 {
    animation-delay: 0.6s;
}

/* ===== HERO SECTION STYLES ===== */
.step-card-container {
    animation: step-card-entrance 0.8s ease-out forwards;
    opacity: 0;
}

.step-card-container .bg-red-500 {
    animation: badge-pulse 2s ease-in-out infinite;
}

.step-card-container:hover .w-24.h-24 svg {
    animation: icon-hover 0.6s ease-in-out;
}

/* Enhanced card hover effects */
.step-card-container .bg-white {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-card-container .bg-white:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* SVG Animation Classes */
.step-card-container svg g[style*="svg-character-bounce"] {
    animation: svg-character-bounce 3s ease-in-out infinite;
}

.step-card-container svg g[style*="svg-phone-glow"] {
    animation: svg-phone-glow 2s ease-in-out infinite;
}

.step-card-container svg g[style*="svg-activity-wiggle"] {
    animation: svg-activity-wiggle 1.5s ease-in-out infinite;
}

/* Arrow animations */
.step-card-container svg path[stroke-dasharray] {
    animation: arrow-flow 2s linear infinite;
}

/* Hover enhancements for better interactivity */
.step-card-container:hover {
    transform: scale(1.02);
}

.step-card-container:hover .bg-red-500 {
    animation-duration: 1s;
}

.step-card-container:hover svg {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* ===== HERO SECTION V2 ANIMATIONS ===== */
@keyframes scene-entrance {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes arrow-entrance {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes character-sway {
    0%, 100% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(3deg);
    }
}

@keyframes floating-elements {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-15px) rotate(180deg);
    }
}

@keyframes gradient-shift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Hero V2 specific styles */
.hero-v2 .step-badge {
    filter: drop-shadow(0 4px 8px rgba(255, 75, 43, 0.3));
}

.hero-v2 .scene-1,
.hero-v2 .scene-2,
.hero-v2 .scene-3 {
    animation: scene-entrance 1s ease-out forwards;
}

.hero-v2 .connecting-arrows {
    animation: arrow-entrance 1s ease-out forwards;
}

/* Floating background elements */
.animate-float {
    animation: floating-elements 6s ease-in-out infinite;
}

/* Gradient text animation */
.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
}

/* Responsive adjustments for hero section */
@media (max-width: 768px) {
    .step-card-container .w-24.h-24 {
        width: 4rem;
        height: 4rem;
    }

    .step-card-container .w-12.h-12 svg {
        width: 2rem;
        height: 2rem;
    }

    .step-card-container .absolute.-top-4.-left-4 .w-12.h-12 {
        width: 2.5rem;
        height: 2.5rem;
    }

    .step-card-container .absolute.-top-4.-left-4 .text-lg {
        font-size: 0.875rem;
    }
}

/* ===== RESPONSIVE DESIGN FOR LANDING PAGE ===== */
@media (max-width: 1024px) {
    .hero-title {
        font-size: 3rem;
    }

    .section-title {
        font-size: 2.25rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .btn-primary-enhanced,
    .btn-secondary-enhanced {
        padding: 14px 28px;
        font-size: 1rem;
        gap: 10px;
    }

    .trust-indicator {
        padding: 12px 20px;
        gap: 10px;
    }

    .trust-icon {
        width: 40px;
        height: 40px;
    }

    .floating-circle {
        display: none;
    }

    .modern-card,
    .step-card,
    .value-card {
        padding: 1.5rem;
    }

    .cta-card {
        padding: 2rem;
    }

    .feature-icon,
    .value-icon {
        width: 60px;
        height: 60px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 20px;
        font-size: 0.85rem;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .btn-primary-enhanced,
    .btn-secondary-enhanced {
        padding: 12px 24px;
        font-size: 0.95rem;
        gap: 8px;
        width: 100%;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .trust-indicator {
        padding: 10px 16px;
        gap: 8px;
        flex-direction: column;
        text-align: center;
        margin-bottom: 1rem;
    }

    .trust-icon {
        width: 36px;
        height: 36px;
    }

    .trust-divider {
        display: none;
    }

    .floating-circle {
        display: none;
    }

    .modern-card,
    .step-card,
    .value-card,
    .cta-card {
        padding: 1.25rem;
    }

    .feature-icon,
    .value-icon {
        width: 50px;
        height: 50px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* ===== CREATIVE CTA SECTION STYLES ===== */

/* Floating Background Elements */
.floating-circle {
    animation: float 6s ease-in-out infinite;
}

.animation-delay-1 {
    animation-delay: 1.5s;
}

.animation-delay-2 {
    animation-delay: 3s;
}

.animation-delay-3 {
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

/* Gentle Pulse Animation */
.animate-pulse-gentle {
    animation: pulse-gentle 3s ease-in-out infinite;
}

@keyframes pulse-gentle {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.5);
    }
}

/* Enhanced CTA Buttons */
.cta-button-wrapper {
    position: relative;
    display: inline-block;
}

.btn-primary-enhanced,
.btn-secondary-enhanced {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    z-index: 2;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, #4F46E5 0%, #6366F1 50%, #8B5CF6 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.btn-secondary-enhanced {
    background: white;
    color: #4F46E5;
    border: 2px solid #4F46E5;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.btn-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.btn-secondary-enhanced .btn-icon-wrapper {
    background: rgba(79, 70, 229, 0.1);
}

.btn-text {
    flex: 1;
    text-align: center;
}

.btn-arrow {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

/* Button Hover Effects */
.cta-button-wrapper:hover .btn-primary-enhanced,
.cta-button-wrapper:hover .btn-secondary-enhanced {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4);
}

.cta-button-wrapper:hover .btn-primary-enhanced {
    background: linear-gradient(135deg, #3730A3 0%, #4338CA 50%, #7C3AED 100%);
}

.cta-button-wrapper:hover .btn-secondary-enhanced {
    background: #4F46E5;
    color: white;
}

.cta-button-wrapper:hover .btn-secondary-enhanced .btn-icon-wrapper {
    background: rgba(255, 255, 255, 0.2);
}

.cta-button-wrapper:hover .btn-icon-wrapper {
    transform: rotate(360deg) scale(1.1);
}

.cta-button-wrapper:hover .btn-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Button Glow Effect */
.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    background: linear-gradient(135deg, #4F46E5, #6366F1, #8B5CF6);
    opacity: 0;
    filter: blur(20px);
    transition: opacity 0.3s ease;
    z-index: 1;
}

.btn-glow.secondary {
    background: linear-gradient(135deg, #4F46E5, #6366F1);
}

.cta-button-wrapper:hover .btn-glow {
    opacity: 0.3;
}

/* Trust Indicators */
.trust-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(79, 70, 229, 0.1);
}

.trust-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: rgba(79, 70, 229, 0.2);
}

.trust-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(79, 70, 229, 0.1);
    transition: all 0.3s ease;
}

.trust-indicator:hover .trust-icon {
    transform: scale(1.1);
    background: rgba(79, 70, 229, 0.15);
}

.trust-content {
    text-align: left;
}

.trust-title {
    font-weight: 600;
    color: #1F2937;
    font-size: 0.95rem;
    margin-bottom: 2px;
}

.trust-subtitle {
    font-size: 0.85rem;
    color: #6B7280;
}

.trust-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(to bottom, transparent, #E5E7EB, transparent);
    display: none;
}

@media (min-width: 640px) {
    .trust-divider {
        display: block;
    }
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.btn-primary,
.btn-secondary,
.modern-card,
.step-card,
.value-card {
    will-change: transform;
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: transform, box-shadow, background-color, border-color, color;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

/* Focus states for accessibility */
.btn-primary:focus,
.btn-secondary:focus,
.romantic-nav-link:focus,
.romantic-dropdown-item:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .romantic-header,
    .romantic-footer,
    .btn-primary,
    .btn-secondary {
        display: none !important;
    }

    .hero-section,
    .modern-card,
    .step-card,
    .value-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}