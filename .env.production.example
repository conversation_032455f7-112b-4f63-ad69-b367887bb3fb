# Production Environment Configuration
# Copy this to .env for production deployment

APP_NAME="Dating App"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=https://your-domain.com

# Logging Configuration (Suppress logs in production)
LOG_CHANNEL=null
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Cache Configuration
CACHE_STORE=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Firebase Configuration (for notifications)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Payment Configuration (Razorpay)
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret

# File Storage
FILESYSTEM_DISK=public

# Performance Optimization
OPTIMIZE_AUTOLOADER=true
CACHE_CONFIG=true
CACHE_ROUTES=true
CACHE_VIEWS=true

# Security
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Disable unnecessary features in production
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false

# Notification Settings
NOTIFICATION_QUEUE_ENABLED=true
NOTIFICATION_BATCH_SIZE=100

# Rate Limiting
RATE_LIMIT_ENABLED=true
API_RATE_LIMIT=60

# Maintenance Mode
MAINTENANCE_MODE=false
