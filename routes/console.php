<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule escrow payment processing every 5 minutes
Schedule::command('escrow:process-payments')->everyFiveMinutes();

// Schedule meeting reminders every minute
Schedule::command('meeting:send-reminders')->everyMinute();

// Schedule automatic booking cancellation every minute (consolidated command)
Schedule::command('bookings:auto-cancel')->everyMinute();

// Schedule subscription status updates every hour
Schedule::command('subscription:update-status')->hourly();

// Schedule automatic user unsuspension every hour
Schedule::command('users:unsuspend-expired')->hourly();

// Schedule subscription expiry reminders daily at 9 AM
Schedule::command('subscription:send-reminders')->dailyAt('09:00');

// Schedule review reminders daily at 6 PM
Schedule::command('reviews:send-reminders')->dailyAt('18:00');

// Schedule site mode checks every minute
Schedule::command('site:check-modes')->everyMinute();

// Schedule no-show detection every 2 hours
Schedule::command('bookings:detect-no-show')->everyTwoHours();

// Schedule automatic meeting end processing every 5 minutes
Schedule::command('meetings:auto-end')->everyFiveMinutes();
