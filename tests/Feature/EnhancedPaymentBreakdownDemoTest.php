<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\UserWallet;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class EnhancedPaymentBreakdownDemoTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $provider;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with complete profiles
        $this->client = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'contact_number' => '**********',
            'gender' => 'male',
            'date_of_birth' => '1990-01-01',
            'interests' => 'Reading, Movies',
            'expectation' => 'Looking for meaningful connections',
            'profile_picture' => 'profile.jpg'
        ]);

        $this->provider = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'hourly_rate' => 1000,
            'email_verified_at' => now(),
            'contact_number' => '**********',
            'gender' => 'female',
            'date_of_birth' => '1992-01-01',
            'interests' => 'Travel, Music',
            'expectation' => 'Professional networking',
            'profile_picture' => 'provider.jpg'
        ]);

        // Set up settings
        Setting::set('platform_fee', 50);
        Setting::set('commission_percentage', 10);

        // Create initial booking (30 minutes - minimum allowed duration)
        $this->booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->addHours(2), // 2 hours from now
            'duration_hours' => 0.5, // 30 minutes (0.5 hours)
            'actual_duration_hours' => 0.5,
            'hourly_rate' => 1000,
            'platform_fee' => 50,
            'base_amount' => 500, // 0.5 * 1000
            'total_amount' => 550, // 500 + 50
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Coffee Shop',
            'commission_percentage' => 10,
            'commission_amount' => 50,
            'provider_amount' => 450,
            'paid_at' => now()
        ]);
    }

    /** @test */
    public function it_demonstrates_enhanced_payment_breakdown_for_time_extension_scenario()
    {
        // Set up wallet with some balance
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 500;
        $wallet->save();

        // Scenario: Extend booking from 2:15 PM - 2:45 PM (30 min) to 2:15 PM - 3:15 PM (1 hour)
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 1.0, // Extend to 1 hour
                'location' => 'Coffee Shop',
                'notes' => 'Extended meeting for detailed discussion'
            ]);


        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();
        $breakdown = $data['payment_breakdown'];

        // Verify the enhanced payment breakdown
        $this->assertArrayHasKey('payment_breakdown', $data);

        // Original booking: 30 minutes (0.5h) * ₹1000 + ₹50 = ₹550
        $this->assertEquals(550, $breakdown['original_amount']);
        $this->assertEquals(0.5, $breakdown['original_duration']);

        // New booking: 1 hour * ₹1000 + ₹50 = ₹1050
        $this->assertEquals(1050, $breakdown['new_amount']);
        $this->assertEquals(1.0, $breakdown['new_duration']);

        // Difference: ₹1050 - ₹550 = ₹500
        $this->assertEquals(500, $breakdown['difference_amount']);
        
        // Payment method breakdown
        $this->assertEquals(500, $breakdown['wallet_balance']); // Original wallet balance
        $this->assertEquals(500, $breakdown['wallet_usage']); // Use full wallet balance
        $this->assertEquals(0, $breakdown['online_payment_required']); // No online payment needed (wallet covers full difference)
        $this->assertEquals(0, $breakdown['refund_amount']); // No refund for extension

        // Since wallet covers full difference, no additional payment is required
        $this->assertFalse(isset($data['payment_required']) && $data['payment_required']);
        $this->assertStringContainsString('successfully', $data['message']);

        echo "\n=== Enhanced Payment Breakdown Demo ===\n";
        echo "Original Booking: {$breakdown['original_duration']} hours for ₹{$breakdown['original_amount']}\n";
        echo "Updated Booking: {$breakdown['new_duration']} hours for ₹{$breakdown['new_amount']}\n";
        echo "Difference Amount: ₹{$breakdown['difference_amount']}\n";
        echo "Wallet Balance: ₹{$breakdown['wallet_balance']}\n";
        echo "Wallet Usage: ₹{$breakdown['wallet_usage']}\n";
        echo "Online Payment Required: ₹{$breakdown['online_payment_required']}\n";
        echo "Refund Amount: ₹{$breakdown['refund_amount']}\n";
        echo "=====================================\n";
    }

    /** @test */
    public function it_demonstrates_payment_breakdown_for_time_reduction_with_refund()
    {
        // First, update the booking to be 1 hour so we can reduce it
        $this->booking->update([
            'duration_hours' => 1.0,
            'actual_duration_hours' => 1.0,
            'base_amount' => 1000,
            'total_amount' => 1050
        ]);

        // Set up wallet
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 200;
        $wallet->save();

        // Scenario: Reduce booking from 1 hour to 30 minutes
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 0.5, // Reduce to 30 minutes
                'location' => 'Coffee Shop',
                'notes' => 'Reduced meeting duration'
            ]);


        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();
        $breakdown = $data['payment_breakdown'];

        // Original: 1 hour * ₹1000 + ₹50 = ₹1050
        $this->assertEquals(1050, $breakdown['original_amount']);
        $this->assertEquals(1.0, $breakdown['original_duration']);
        
        // New: 0.5 hour * ₹1000 + ₹50 = ₹550
        $this->assertEquals(550, $breakdown['new_amount']);
        $this->assertEquals(0.5, $breakdown['new_duration']);
        
        // Difference: ₹550 - ₹1050 = -₹500 (refund)
        $this->assertEquals(-500, $breakdown['difference_amount']);
        
        // Payment method breakdown for refund
        $this->assertEquals(0, $breakdown['wallet_usage']); // No additional payment needed
        $this->assertEquals(0, $breakdown['online_payment_required']); // No online payment needed
        $this->assertEquals(500, $breakdown['refund_amount']); // ₹500 will be refunded to wallet

        echo "\n=== Time Reduction with Refund Demo ===\n";
        echo "Original Booking: {$breakdown['original_duration']} hours for ₹{$breakdown['original_amount']}\n";
        echo "Updated Booking: {$breakdown['new_duration']} hours for ₹{$breakdown['new_amount']}\n";
        echo "Difference Amount: ₹{$breakdown['difference_amount']}\n";
        echo "Refund to Wallet: ₹{$breakdown['refund_amount']}\n";
        echo "=====================================\n";
    }
}
