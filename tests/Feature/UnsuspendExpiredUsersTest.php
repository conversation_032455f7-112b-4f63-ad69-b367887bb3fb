<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

class UnsuspendExpiredUsersTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that expired suspended users are automatically unsuspended.
     */
    public function test_expired_suspended_users_are_unsuspended()
    {
        // Create a user with expired suspension
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'is_suspended' => true,
            'suspension_end_date' => Carbon::now()->subHours(2), // Expired 2 hours ago
            'suspension_reason' => 'Test suspension'
        ]);

        // Run the unsuspension command
        Artisan::call('users:unsuspend-expired');

        // Refresh user from database
        $user->refresh();

        // Assert user is no longer suspended
        $this->assertFalse($user->is_suspended);
        $this->assertNull($user->suspension_end_date);
        $this->assertNull($user->suspension_reason);

        // Assert notification was created
        $this->assertDatabaseHas('notifications', [
            'user_id' => $user->id,
            'type' => 'account_unsuspended',
            'title' => 'Account Reactivated'
        ]);
    }

    /**
     * Test that non-expired suspended users are not unsuspended.
     */
    public function test_non_expired_suspended_users_remain_suspended()
    {
        // Create a user with future suspension end date
        $user = User::factory()->create([
            'name' => 'Test User 2',
            'email' => '<EMAIL>',
            'is_suspended' => true,
            'suspension_end_date' => Carbon::now()->addHours(2), // Expires in 2 hours
            'suspension_reason' => 'Test suspension'
        ]);

        // Run the unsuspension command
        Artisan::call('users:unsuspend-expired');

        // Refresh user from database
        $user->refresh();

        // Assert user is still suspended
        $this->assertTrue($user->is_suspended);
        $this->assertNotNull($user->suspension_end_date);
        $this->assertNotNull($user->suspension_reason);

        // Assert no unsuspension notification was created
        $this->assertDatabaseMissing('notifications', [
            'user_id' => $user->id,
            'type' => 'account_unsuspended'
        ]);
    }

    /**
     * Test that users without suspension end date are not affected.
     */
    public function test_users_without_suspension_end_date_are_not_affected()
    {
        // Create a suspended user without suspension_end_date (permanent suspension)
        $user = User::factory()->create([
            'name' => 'Test User 3',
            'email' => '<EMAIL>',
            'is_suspended' => true,
            'suspension_end_date' => null,
            'suspension_reason' => 'Permanent suspension'
        ]);

        // Run the unsuspension command
        Artisan::call('users:unsuspend-expired');

        // Refresh user from database
        $user->refresh();

        // Assert user is still suspended
        $this->assertTrue($user->is_suspended);
        $this->assertNull($user->suspension_end_date);
        $this->assertEquals('Permanent suspension', $user->suspension_reason);
    }

    /**
     * Test dry-run mode doesn't actually unsuspend users.
     */
    public function test_dry_run_mode_does_not_unsuspend_users()
    {
        // Create a user with expired suspension
        $user = User::factory()->create([
            'name' => 'Test User 4',
            'email' => '<EMAIL>',
            'is_suspended' => true,
            'suspension_end_date' => Carbon::now()->subHours(1),
            'suspension_reason' => 'Test suspension'
        ]);

        // Run the command in dry-run mode
        Artisan::call('users:unsuspend-expired', ['--dry-run' => true]);

        // Refresh user from database
        $user->refresh();

        // Assert user is still suspended (dry-run didn't change anything)
        $this->assertTrue($user->is_suspended);
        $this->assertNotNull($user->suspension_end_date);
        $this->assertNotNull($user->suspension_reason);

        // Assert no notification was created
        $this->assertDatabaseMissing('notifications', [
            'user_id' => $user->id,
            'type' => 'account_unsuspended'
        ]);
    }
}
