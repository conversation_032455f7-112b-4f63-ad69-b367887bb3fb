<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\MeetingVerification;
use App\Models\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MeetingPhotoRequirementsTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $provider;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users manually
        $this->client = User::create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client',
            'gender' => 'male',
            'date_of_birth' => '1990-01-01',
        ]);

        $this->provider = User::create([
            'name' => 'Test Provider',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'provider',
            'gender' => 'female',
            'date_of_birth' => '1992-01-01',
            'hourly_rate' => 1000,
        ]);

        // Create a test booking manually
        $this->booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'provider_status' => 'accepted',
            'booking_date' => now()->addHour(),
            'duration_hours' => 2,
            'hourly_rate' => 1000,
            'total_amount' => 2000,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'meeting_location' => 'Test Location',
        ]);

        Storage::fake('public');
    }

    /** @test */
    public function it_does_not_create_meeting_start_reminder_notification_when_first_start_photo_uploaded()
    {
        // Act as client and upload start photo
        $this->actingAs($this->client);
        
        $photo = UploadedFile::fake()->image('start_photo.jpg');
        
        $response = $this->postJson("/meeting-verification/{$this->booking->id}/start-photo", [
            'photo' => $photo,
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'accuracy' => 10,
            'address' => 'Test Address',
            'full_address' => 'Full Test Address',
        ]);
        
        $response->assertStatus(200);
        
        // Assert that no meeting_start_reminder notification was created
        $this->assertDatabaseMissing('notifications', [
            'user_id' => $this->provider->id,
            'type' => 'meeting_start_reminder',
        ]);
    }

    /** @test */
    public function it_requires_both_participants_in_photos()
    {
        // This test verifies that the UI guidance is in place
        // The actual validation would need to be implemented with AI/ML
        // For now, we test that the confirmation dialog is triggered
        
        $this->actingAs($this->client);
        
        $response = $this->get('/notifications');
        
        // Check that the photo instructions are present
        $response->assertSee('Both participants must be visible in the same photo together');
        $response->assertSee('Make sure you and your meeting partner are both clearly visible');
    }

    /** @test */
    public function it_shows_updated_camera_modal_titles()
    {
        $this->actingAs($this->client);
        
        $response = $this->get('/notifications');
        
        // Check that the camera modal shows updated titles
        $response->assertSee('Take Meeting Start Photo Together');
        $response->assertSee('Take Meeting End Photo Together');
    }

    /** @test */
    public function it_enforces_photo_requirements_for_active_meetings()
    {
        // Create meeting verification
        $verification = MeetingVerification::create([
            'booking_id' => $this->booking->id,
        ]);
        
        // Test that both start photos are required
        $this->assertFalse($verification->hasBothStartPhotos());
        
        // Upload client start photo
        $verification->update([
            'client_start_photo' => 'path/to/client_start.jpg',
            'client_start_time' => now(),
        ]);
        
        $this->assertFalse($verification->hasBothStartPhotos());
        
        // Upload provider start photo
        $verification->update([
            'provider_start_photo' => 'path/to/provider_start.jpg',
            'provider_start_time' => now(),
        ]);
        
        $this->assertTrue($verification->hasBothStartPhotos());
        
        // Test that both end photos are required
        $this->assertFalse($verification->hasBothEndPhotos());
        
        // Upload both end photos
        $verification->update([
            'client_end_photo' => 'path/to/client_end.jpg',
            'client_end_time' => now(),
            'provider_end_photo' => 'path/to/provider_end.jpg',
            'provider_end_time' => now(),
        ]);
        
        $this->assertTrue($verification->hasBothEndPhotos());
        $this->assertTrue($verification->hasAllRequiredPhotos());
    }

    /** @test */
    public function it_prevents_end_photo_upload_before_start_photos()
    {
        $this->actingAs($this->client);
        
        $photo = UploadedFile::fake()->image('end_photo.jpg');
        
        $response = $this->postJson("/meeting-verification/{$this->booking->id}/end-photo", [
            'photo' => $photo,
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'accuracy' => 10,
            'address' => 'Test Address',
            'full_address' => 'Full Test Address',
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Meeting has not started yet. Both users must upload start photos first.'
        ]);
    }
}
