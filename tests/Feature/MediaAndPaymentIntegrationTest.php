<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Setting;
use App\Http\Middleware\SecurityHeaders;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class MediaAndPaymentIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test Razorpay credentials
        Setting::create([
            'key' => 'razorpay_key_id',
            'value' => 'rzp_test_Go3jN8rdNmRJ7P',
            'group' => 'payment',
            'type' => 'text'
        ]);
        
        Setting::create([
            'key' => 'razorpay_key_secret',
            'value' => 'sbD3JVTl7W7UJ18O43cRmtCE',
            'group' => 'payment',
            'type' => 'text'
        ]);
    }

    /**
     * Test that security headers middleware sets correct permissions policy for camera and payments.
     */
    public function test_security_headers_allow_camera_and_payment_access()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        // Check Permissions-Policy header
        $this->assertTrue($response->headers->has('Permissions-Policy'));
        $permissionsPolicy = $response->headers->get('Permissions-Policy');

        // Camera permissions (updated to allow all origins for better compatibility)
        $this->assertStringContainsString('camera=*', $permissionsPolicy);
        $this->assertStringNotContainsString('camera=()', $permissionsPolicy);

        // Microphone permissions
        $this->assertStringContainsString('microphone=*', $permissionsPolicy);

        // Payment permissions for Razorpay
        $this->assertStringContainsString('payment=(self "https://checkout.razorpay.com" "https://api.razorpay.com")', $permissionsPolicy);

        // Additional media permissions
        $this->assertStringContainsString('display-capture=*', $permissionsPolicy);
        $this->assertStringContainsString('screen-wake-lock=*', $permissionsPolicy);
    }

    /**
     * Test that CSP headers allow necessary Razorpay domains.
     */
    public function test_csp_headers_allow_razorpay_domains()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        $csp = $response->headers->get('Content-Security-Policy');

        // Check script-src allows Razorpay
        $this->assertStringContainsString('https://checkout.razorpay.com', $csp);

        // Check connect-src allows Razorpay API
        $this->assertStringContainsString('https://api.razorpay.com', $csp);
        $this->assertStringContainsString('https://checkout.razorpay.com', $csp);

        // Check frame-src allows Razorpay
        $this->assertStringContainsString('frame-src \'self\' https://checkout.razorpay.com https://api.razorpay.com', $csp);

        // Check media-src for camera/video
        $this->assertStringContainsString('media-src \'self\' blob:', $csp);
    }

    /**
     * Test Razorpay debug controller functionality.
     */
    public function test_razorpay_debug_controller_works()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->getJson('/admin/razorpay-debug/test');

        $this->assertTrue(
            $response->status() === 200 || $response->status() === 400,
            'Debug endpoint should return either success or configuration error'
        );

        $data = $response->json();
        $this->assertArrayHasKey('success', $data);
        $this->assertArrayHasKey('debug_info', $data);

        if ($data['success']) {
            $this->assertArrayHasKey('test_order_id', $data['debug_info']);
        } else {
            $this->assertArrayHasKey('message', $data);
        }
    }

    /**
     * Test that the debug page is accessible to admins.
     */
    public function test_razorpay_debug_page_accessible_to_admin()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->get('/admin/razorpay-debug/');
        $response->assertStatus(200);
        $response->assertViewIs('admin.razorpay-debug');
    }

    /**
     * Test that the debug page is not accessible to regular users.
     */
    public function test_razorpay_debug_page_not_accessible_to_regular_users()
    {
        $user = User::factory()->create(['is_admin' => false]);
        $this->actingAs($user);

        $response = $this->get('/admin/razorpay-debug/');
        $response->assertRedirect();
    }

    /**
     * Test order creation with valid parameters.
     */
    public function test_razorpay_test_order_creation()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->postJson('/admin/razorpay-debug/test-order', [
            'amount' => 1.00,
            'currency' => 'INR',
            'receipt' => 'test_receipt_' . time()
        ]);

        // Should either succeed or fail with proper error handling
        $this->assertTrue(
            $response->status() === 200 || $response->status() === 400,
            'Order creation should return either success or proper error'
        );

        $data = $response->json();
        $this->assertArrayHasKey('success', $data);
        $this->assertArrayHasKey('message', $data);
    }

    /**
     * Test that media performance optimizer script file exists.
     */
    public function test_media_performance_optimizer_script_exists()
    {
        $scriptPath = public_path('js/media-performance-optimizer.js');
        $this->assertFileExists($scriptPath, 'Media performance optimizer script should exist');

        $content = file_get_contents($scriptPath);
        $this->assertStringContainsString('MediaPerformanceOptimizer', $content);
        $this->assertStringContainsString('optimizeCanvasPerformance', $content);
        $this->assertStringContainsString('setupCameraPermissions', $content);
    }

    /**
     * Test camera permissions test page exists and works.
     */
    public function test_camera_permissions_test_page_exists()
    {
        // Check if the test file exists in public directory
        $testFilePath = public_path('test-camera-permissions.html');
        $this->assertFileExists($testFilePath, 'Camera permissions test file should exist');
    }

    /**
     * Test that security headers prevent common vulnerabilities.
     */
    public function test_security_headers_prevent_vulnerabilities()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        // Check security headers
        $this->assertEquals('DENY', $response->headers->get('X-Frame-Options'));
        $this->assertEquals('nosniff', $response->headers->get('X-Content-Type-Options'));
        $this->assertEquals('1; mode=block', $response->headers->get('X-XSS-Protection'));
        $this->assertEquals('strict-origin-when-cross-origin', $response->headers->get('Referrer-Policy'));

        // Check cache control for security headers
        $this->assertStringContainsString('no-cache', $response->headers->get('Cache-Control'));
        $this->assertStringContainsString('no-store', $response->headers->get('Cache-Control'));
    }

    /**
     * Test that Razorpay configuration validation works.
     */
    public function test_razorpay_configuration_validation()
    {
        // Test with valid configuration
        $keyId = Setting::get('razorpay_key_id');
        $keySecret = Setting::get('razorpay_key_secret');

        $this->assertNotEmpty($keyId, 'Razorpay Key ID should be configured');
        $this->assertNotEmpty($keySecret, 'Razorpay Key Secret should be configured');
        $this->assertStringStartsWith('rzp_', $keyId, 'Razorpay Key ID should have correct format');
    }

    /**
     * Test error handling for invalid Razorpay configuration.
     */
    public function test_razorpay_error_handling_with_invalid_config()
    {
        // Temporarily set invalid configuration
        Setting::where('key', 'razorpay_key_id')->update(['value' => '']);
        Setting::where('key', 'razorpay_key_secret')->update(['value' => '']);

        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->getJson('/admin/razorpay-debug/test');
        $response->assertStatus(400);

        $data = $response->json();
        $this->assertFalse($data['success']);
        $this->assertStringContainsString('not configured', $data['message']);
    }
}
