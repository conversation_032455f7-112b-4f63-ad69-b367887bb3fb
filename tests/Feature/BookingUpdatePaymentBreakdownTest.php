<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\UserWallet;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class BookingUpdatePaymentBreakdownTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $client;
    protected $provider;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with complete profiles
        $this->client = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'contact_number' => '**********',
            'gender' => 'male',
            'date_of_birth' => '1990-01-01',
            'interests' => 'Reading, Movies',
            'expectation' => 'Looking for meaningful connections',
            'profile_picture' => 'profile.jpg'
        ]);

        $this->provider = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'hourly_rate' => 1000,
            'email_verified_at' => now(),
            'contact_number' => '**********',
            'gender' => 'female',
            'date_of_birth' => '1992-01-01',
            'interests' => 'Travel, Music',
            'expectation' => 'Professional networking',
            'profile_picture' => 'provider.jpg'
        ]);

        // Set up settings
        Setting::set('platform_fee', 50);
        Setting::set('commission_percentage', 10);

        // Create initial booking (1 hour for testing reductions)
        $this->booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->addHours(2),
            'duration_hours' => 1.0, // 1 hour
            'actual_duration_hours' => 1.0,
            'hourly_rate' => 1000,
            'platform_fee' => 50,
            'base_amount' => 1000, // 1.0 * 1000
            'total_amount' => 1050, // 1000 + 50
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Test Location',
            'commission_percentage' => 10,
            'commission_amount' => 100,
            'provider_amount' => 900,
            'paid_at' => now()
        ]);
    }

    /** @test */
    public function it_calculates_payment_breakdown_for_time_extension_with_sufficient_wallet_balance()
    {
        // Set up wallet with sufficient balance
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 1000;
        $wallet->save();

        // Update booking to 2 hours (from 1 hour)
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 2.0,
                'location' => 'Test Location',
                'notes' => 'Extended booking'
            ]);


        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();

        // Verify payment breakdown
        $this->assertArrayHasKey('payment_breakdown', $data);
        $breakdown = $data['payment_breakdown'];

        // Original: 1.0h * 1000 + 50 = 1050
        // New: 2.0h * 1000 + 50 = 2050
        // Difference: 1000
        $this->assertEquals(1050, $breakdown['original_amount']);
        $this->assertEquals(2050, $breakdown['new_amount']);
        $this->assertEquals(1000, $breakdown['difference_amount']);
        $this->assertEquals(1.0, $breakdown['original_duration']);
        $this->assertEquals(2.0, $breakdown['new_duration']);
        $this->assertEquals(1000, $breakdown['wallet_balance']);
        $this->assertEquals(1000, $breakdown['wallet_usage']); // Full difference covered by wallet
        $this->assertEquals(0, $breakdown['online_payment_required']); // No online payment needed
        $this->assertEquals(0, $breakdown['refund_amount']);
    }

    /** @test */
    public function it_calculates_payment_breakdown_for_time_extension_with_insufficient_wallet_balance()
    {
        // Set up wallet with insufficient balance
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 200; // Less than the 500 difference
        $wallet->save();

        // Update booking to 2 hours (from 1 hour)
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 2.0,
                'location' => 'Test Location',
                'notes' => 'Extended booking'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();
        $breakdown = $data['payment_breakdown'];

        // Difference: 1000, Wallet: 200, Online: 800
        $this->assertEquals(1000, $breakdown['difference_amount']);
        $this->assertEquals(200, $breakdown['wallet_balance']);
        $this->assertEquals(200, $breakdown['wallet_usage']); // Partial coverage from wallet
        $this->assertEquals(800, $breakdown['online_payment_required']); // Remaining amount
        $this->assertEquals(0, $breakdown['refund_amount']);
    }

    /** @test */
    public function it_calculates_payment_breakdown_for_time_reduction_with_refund()
    {
        // Set up wallet
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 100;
        $wallet->save();

        // Update booking to 30 minutes (from 1 hour)
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 0.5,
                'location' => 'Test Location',
                'notes' => 'Reduced booking'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();
        $breakdown = $data['payment_breakdown'];

        // Original: 1050, New: 0.5h * 1000 + 50 = 550, Difference: -500 (refund)
        $this->assertEquals(1050, $breakdown['original_amount']);
        $this->assertEquals(550, $breakdown['new_amount']);
        $this->assertEquals(-500, $breakdown['difference_amount']);
        $this->assertEquals(1.0, $breakdown['original_duration']);
        $this->assertEquals(0.5, $breakdown['new_duration']);
        $this->assertEquals(0, $breakdown['wallet_usage']); // No additional payment needed
        $this->assertEquals(0, $breakdown['online_payment_required']); // No online payment needed
        $this->assertEquals(500, $breakdown['refund_amount']); // Refund amount
    }

    /** @test */
    public function it_calculates_payment_breakdown_for_no_time_change()
    {
        // Set up wallet
        $wallet = UserWallet::getOrCreate($this->client->id);
        $wallet->balance = 100;
        $wallet->save();

        // Update booking with same duration but different location
        $response = $this->actingAs($this->client)
            ->putJson("/booking/{$this->booking->id}/update", [
                'booking_date' => $this->booking->booking_date->format('Y-m-d H:i:s'),
                'duration_hours' => 1.0, // Same as original
                'location' => 'New Test Location',
                'notes' => 'Changed location only'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $data = $response->json();
        $breakdown = $data['payment_breakdown'];

        // No amount difference
        $this->assertEquals(1050, $breakdown['original_amount']);
        $this->assertEquals(1050, $breakdown['new_amount']);
        $this->assertEquals(0, $breakdown['difference_amount']);
        $this->assertEquals(0, $breakdown['wallet_usage']);
        $this->assertEquals(0, $breakdown['online_payment_required']);
        $this->assertEquals(0, $breakdown['refund_amount']);
    }
}
