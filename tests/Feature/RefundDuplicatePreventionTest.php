<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class RefundDuplicatePreventionTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $provider;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->client = User::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'client'
        ]);

        $this->provider = User::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'provider'
        ]);

        // Create a paid booking
        $this->booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->addHours(2),
            'duration_hours' => 2,
            'hourly_rate' => 100,
            'platform_fee' => 19,
            'base_amount' => 200,
            'total_amount' => 219,
            'status' => 'pending',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_refunds_when_calling_processRefund_multiple_times()
    {
        // Get initial wallet balance
        $wallet = UserWallet::getOrCreate($this->client->id);
        $initialBalance = $wallet->balance;

        // Process refund first time
        $result1 = $this->booking->processRefund('Test refund');
        $this->assertTrue($result1);

        // Check wallet balance increased
        $wallet->refresh();
        $this->assertEquals($initialBalance + 219, $wallet->balance);

        // Check refund status is set
        $this->booking->refresh();
        $this->assertEquals('processed', $this->booking->refund_status);

        // Try to process refund again
        $result2 = $this->booking->processRefund('Duplicate refund attempt');
        $this->assertFalse($result2);

        // Check wallet balance didn't change
        $wallet->refresh();
        $this->assertEquals($initialBalance + 219, $wallet->balance);

        // Check only one refund transaction exists
        $refundTransactions = WalletTransaction::where('user_id', $this->client->id)
            ->where('booking_id', $this->booking->id)
            ->where('type', 'refund')
            ->count();
        
        $this->assertEquals(1, $refundTransactions);
    }

    /** @test */
    public function it_prevents_provider_actions_on_already_refunded_bookings()
    {
        // Process refund first
        $this->booking->processRefund('Initial refund');
        
        // Try to reject the booking after refund
        $response = $this->actingAs($this->provider)
            ->postJson("/api/provider/bookings/{$this->booking->id}/reject", [
                'reason' => 'Test rejection'
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'This booking has already been refunded and cannot be rejected.'
            ]);
    }

    /** @test */
    public function it_prevents_client_cancellation_on_already_refunded_bookings()
    {
        // Process refund first
        $this->booking->processRefund('Initial refund');
        
        // Try to cancel the booking after refund
        $response = $this->actingAs($this->client)
            ->postJson("/api/bookings/{$this->booking->id}/cancel", [
                'reason' => 'Test cancellation'
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'This booking has already been refunded and cannot be cancelled again.'
            ]);
    }

    /** @test */
    public function it_handles_auto_cancellation_without_duplicate_refunds()
    {
        // Get initial wallet balance
        $wallet = UserWallet::getOrCreate($this->client->id);
        $initialBalance = $wallet->balance;

        // Process auto-cancellation
        $result = $this->booking->processAutoCancellation();
        $this->assertTrue($result);

        // Check wallet balance increased only once
        $wallet->refresh();
        $this->assertEquals($initialBalance + 219, $wallet->balance);

        // Check refund status is set
        $this->booking->refresh();
        $this->assertEquals('processed', $this->booking->refund_status);

        // Try to process another refund
        $result2 = $this->booking->processRefund('Another refund attempt');
        $this->assertFalse($result2);

        // Check wallet balance didn't change
        $wallet->refresh();
        $this->assertEquals($initialBalance + 219, $wallet->balance);
    }
}
