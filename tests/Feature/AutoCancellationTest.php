<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\UserWallet;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

class AutoCancellationTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $provider;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->client = User::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>'
        ]);
        
        $this->provider = User::factory()->create([
            'name' => 'Test Provider',
            'email' => '<EMAIL>',
            'is_time_spending_enabled' => true,
            'hourly_rate' => 100.00
        ]);

        // Create wallets
        UserWallet::getOrCreate($this->client->id);
        UserWallet::getOrCreate($this->provider->id);
    }

    /** @test */
    public function it_auto_cancels_booking_when_start_time_passes_without_provider_response()
    {
        // Create a booking with start time in the past
        $booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->subHours(2), // 2 hours ago
            'duration_hours' => 1,
            'hourly_rate' => 100.00,
            'total_amount' => 100.00,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Test Location',
            'notes' => 'Test booking'
        ]);

        // Get initial wallet balance
        $initialBalance = $this->client->wallet->balance;

        // Run auto-cancellation command
        Artisan::call('bookings:auto-cancel');

        // Refresh booking from database
        $booking->refresh();

        // Assert booking was auto-cancelled
        $this->assertEquals('auto_cancelled', $booking->status);
        $this->assertEquals('rejected', $booking->provider_status);
        $this->assertEquals('processed', $booking->refund_status);
        $this->assertEquals(100.00, $booking->refund_amount);
        $this->assertNotNull($booking->cancelled_at);
        $this->assertNotNull($booking->refunded_at);

        // Assert refund was processed
        $this->client->wallet->refresh();
        $this->assertEquals($initialBalance + 100.00, $this->client->wallet->balance);

        // Assert notifications were sent
        $clientNotification = Notification::where('user_id', $this->client->id)
            ->where('type', 'booking_auto_cancelled')
            ->first();
        $this->assertNotNull($clientNotification);

        $providerNotification = Notification::where('user_id', $this->provider->id)
            ->where('type', 'booking_opportunity_missed')
            ->first();
        $this->assertNotNull($providerNotification);
    }

    /** @test */
    public function it_auto_cancels_booking_when_meeting_end_time_passes()
    {
        // Create a booking where the entire meeting time has passed
        $booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->subHours(3), // Started 3 hours ago
            'duration_hours' => 2, // 2-hour meeting, so it ended 1 hour ago
            'hourly_rate' => 100.00,
            'total_amount' => 200.00,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Test Location',
            'notes' => 'Test booking'
        ]);

        // Run auto-cancellation command
        Artisan::call('bookings:auto-cancel');

        // Refresh booking from database
        $booking->refresh();

        // Assert booking was auto-cancelled
        $this->assertEquals('auto_cancelled', $booking->status);
        $this->assertEquals('rejected', $booking->provider_status);
        $this->assertStringContains('Meeting time has passed', $booking->rejection_reason);
    }

    /** @test */
    public function it_does_not_auto_cancel_future_bookings()
    {
        // Create a booking with future start time
        $booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->addHours(2), // 2 hours in the future
            'duration_hours' => 1,
            'hourly_rate' => 100.00,
            'total_amount' => 100.00,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Test Location'
        ]);

        // Run auto-cancellation command
        Artisan::call('bookings:auto-cancel');

        // Refresh booking from database
        $booking->refresh();

        // Assert booking was NOT auto-cancelled
        $this->assertEquals('confirmed', $booking->status);
        $this->assertEquals('pending', $booking->provider_status);
        $this->assertNull($booking->cancelled_at);
    }

    /** @test */
    public function it_does_not_auto_cancel_already_accepted_bookings()
    {
        // Create a booking that was already accepted
        $booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->subHours(1), // 1 hour ago
            'duration_hours' => 1,
            'hourly_rate' => 100.00,
            'total_amount' => 100.00,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'accepted', // Already accepted
            'meeting_location' => 'Test Location'
        ]);

        // Run auto-cancellation command
        Artisan::call('bookings:auto-cancel');

        // Refresh booking from database
        $booking->refresh();

        // Assert booking was NOT auto-cancelled
        $this->assertEquals('confirmed', $booking->status);
        $this->assertEquals('accepted', $booking->provider_status);
        $this->assertNull($booking->cancelled_at);
    }

    /** @test */
    public function it_prevents_double_processing_of_same_booking()
    {
        // Create a booking with start time in the past
        $booking = TimeSpendingBooking::create([
            'client_id' => $this->client->id,
            'provider_id' => $this->provider->id,
            'booking_date' => Carbon::now()->subHours(1),
            'duration_hours' => 1,
            'hourly_rate' => 100.00,
            'total_amount' => 100.00,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'provider_status' => 'pending',
            'meeting_location' => 'Test Location'
        ]);

        $initialBalance = $this->client->wallet->balance;

        // Run auto-cancellation command twice
        Artisan::call('bookings:auto-cancel');
        Artisan::call('bookings:auto-cancel');

        // Refresh booking and wallet
        $booking->refresh();
        $this->client->wallet->refresh();

        // Assert booking was only processed once
        $this->assertEquals('auto_cancelled', $booking->status);
        $this->assertEquals($initialBalance + 100.00, $this->client->wallet->balance);

        // Assert only one set of notifications was sent
        $clientNotifications = Notification::where('user_id', $this->client->id)
            ->where('type', 'booking_auto_cancelled')
            ->count();
        $this->assertEquals(1, $clientNotifications);
    }
}
