<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Http\Middleware\SecurityHeaders;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CameraPermissionsPolicyTest extends TestCase
{

    /**
     * Test that the SecurityHeaders middleware sets correct Permissions-Policy.
     */
    public function test_security_headers_middleware_sets_correct_permissions_policy()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        // Check that the Permissions-Policy header is set
        $this->assertTrue($response->headers->has('Permissions-Policy'));

        // Get the header value
        $permissionsPolicy = $response->headers->get('Permissions-Policy');

        // Assert that camera=self is present (allowing camera access for the current origin)
        $this->assertStringContainsString('camera=self', $permissionsPolicy);

        // Also check that microphone and geolocation are properly configured
        $this->assertStringContainsString('microphone=self', $permissionsPolicy);
        $this->assertStringContainsString('geolocation=self', $permissionsPolicy);

        // Ensure payment policy is correctly configured for Razorpay
        $this->assertStringContainsString('payment=(self "https://checkout.razorpay.com" "https://api.razorpay.com")', $permissionsPolicy);
    }

    /**
     * Test that the Permissions-Policy header does not deny camera access.
     */
    public function test_permissions_policy_does_not_deny_camera()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        $permissionsPolicy = $response->headers->get('Permissions-Policy');

        // Ensure camera is not denied with empty parentheses
        $this->assertStringNotContainsString('camera=()', $permissionsPolicy);

        // Ensure microphone is not denied with empty parentheses
        $this->assertStringNotContainsString('microphone=()', $permissionsPolicy);
    }

    /**
     * Test that other security headers are properly set.
     */
    public function test_security_headers_are_properly_configured()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        // Check Content Security Policy
        $this->assertTrue($response->headers->has('Content-Security-Policy'));

        // Check other security headers
        $this->assertEquals('DENY', $response->headers->get('X-Frame-Options'));
        $this->assertEquals('nosniff', $response->headers->get('X-Content-Type-Options'));
        $this->assertEquals('1; mode=block', $response->headers->get('X-XSS-Protection'));
        $this->assertEquals('strict-origin-when-cross-origin', $response->headers->get('Referrer-Policy'));
    }

    /**
     * Test that the middleware correctly fixes the camera permissions issue.
     */
    public function test_camera_permissions_fix()
    {
        $middleware = new SecurityHeaders();
        $request = Request::create('/', 'GET');

        $response = $middleware->handle($request, function ($request) {
            return new Response('Test content');
        });

        $permissionsPolicy = $response->headers->get('Permissions-Policy');

        // The main fix: ensure camera=self is present (not camera=())
        $this->assertStringContainsString('camera=self', $permissionsPolicy);

        // Ensure the old problematic configuration is not present
        $this->assertStringNotContainsString('camera=()', $permissionsPolicy);
    }
}
