<?php

namespace Database\Factories;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SubscriptionPlan>
 */
class SubscriptionPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionPlan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['Basic Plan', 'Premium Plan', 'Pro Plan', 'Monthly Plan', 'Yearly Plan']),
            'duration_months' => $this->faker->randomElement([1, 3, 6, 12]),
            'amount' => $this->faker->randomFloat(2, 99, 9999),
            'original_price' => $this->faker->randomFloat(2, 99, 9999),
            'discount_price' => null,
            'description' => $this->faker->sentence(),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Indicate that the plan has a discount.
     */
    public function withDiscount(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $attributes['original_price'] ?? $this->faker->randomFloat(2, 199, 9999);
            $discountPrice = $originalPrice * 0.8; // 20% discount
            
            return [
                'original_price' => $originalPrice,
                'discount_price' => $discountPrice,
                'amount' => $discountPrice, // Amount should be the effective price
            ];
        });
    }

    /**
     * Indicate that the plan is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a monthly plan.
     */
    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Monthly Plan',
            'duration_months' => 1,
        ]);
    }

    /**
     * Create a yearly plan.
     */
    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Yearly Plan',
            'duration_months' => 12,
        ]);
    }
}
