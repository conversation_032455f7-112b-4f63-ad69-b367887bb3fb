<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Push notification preferences
            $table->boolean('push_notifications_enabled')->default(true);
            $table->boolean('push_general')->default(true);
            $table->boolean('push_payment')->default(true);
            $table->boolean('push_match')->default(true);
            $table->boolean('push_partner_swapping')->default(true);
            $table->boolean('push_booking')->default(true);
            $table->boolean('push_meeting_reminders')->default(true);
            $table->boolean('push_subscription')->default(true);

            // Email notification preferences
            $table->boolean('email_notifications_enabled')->default(true);
            $table->boolean('email_general')->default(false);
            $table->boolean('email_payment')->default(true);
            $table->boolean('email_match')->default(true);
            $table->boolean('email_partner_swapping')->default(true);
            $table->boolean('email_booking')->default(true);
            $table->boolean('email_meeting_reminders')->default(false);
            $table->boolean('email_subscription')->default(true);

            // In-app notification preferences
            $table->boolean('inapp_notifications_enabled')->default(true);
            $table->boolean('inapp_general')->default(true);
            $table->boolean('inapp_payment')->default(true);
            $table->boolean('inapp_match')->default(true);
            $table->boolean('inapp_partner_swapping')->default(true);
            $table->boolean('inapp_booking')->default(true);
            $table->boolean('inapp_meeting_reminders')->default(true);
            $table->boolean('inapp_subscription')->default(true);

            // Quiet hours
            $table->boolean('quiet_hours_enabled')->default(false);
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();
            $table->string('timezone')->default('UTC');

            $table->timestamps();

            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};
