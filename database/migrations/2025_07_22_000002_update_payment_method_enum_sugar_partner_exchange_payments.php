<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum to include new payment methods
        if (DB::getDriverName() === 'mysql') {
            DB::statement("ALTER TABLE sugar_partner_exchange_payments MODIFY COLUMN payment_method ENUM('razorpay', 'wallet', 'wallet_partial', 'hybrid', 'stripe', 'paypal') DEFAULT 'razorpay'");
        }
        // For SQLite and other databases, the enum constraint is not enforced at the database level
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original enum values
        if (DB::getDriverName() === 'mysql') {
            DB::statement("ALTER TABLE sugar_partner_exchange_payments MODIFY COLUMN payment_method ENUM('razorpay', 'wallet', 'stripe', 'paypal') DEFAULT 'razorpay'");
        }
    }
};
