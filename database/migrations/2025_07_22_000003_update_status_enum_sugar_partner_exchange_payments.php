<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table exists before modifying
        if (Schema::hasTable('sugar_partner_exchange_payments')) {
            // Check database driver to handle enum differently
            $driver = DB::getDriverName();

            if ($driver === 'mysql') {
                // Check if the enum already contains 'partial_completed'
                $result = DB::select("SHOW COLUMNS FROM sugar_partner_exchange_payments WHERE Field = 'status'");
                if (!empty($result)) {
                    $enumValues = $result[0]->Type;
                    if (strpos($enumValues, 'partial_completed') === false) {
                        // Update the enum to include new status
                        DB::statement("ALTER TABLE sugar_partner_exchange_payments MODIFY COLUMN status ENUM('pending', 'completed', 'failed', 'refunded', 'partial_completed') DEFAULT 'pending'");
                    }
                }
            } else {
                // For SQLite and other databases, just ensure the column exists
                // SQLite doesn't have native ENUM support, so this is handled by the application layer
                if (!Schema::hasColumn('sugar_partner_exchange_payments', 'status')) {
                    Schema::table('sugar_partner_exchange_payments', function (Blueprint $table) {
                        $table->string('status')->default('pending');
                    });
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if table exists before modifying
        if (Schema::hasTable('sugar_partner_exchange_payments')) {
            $driver = DB::getDriverName();

            if ($driver === 'mysql') {
                // Revert to original enum values
                DB::statement("ALTER TABLE sugar_partner_exchange_payments MODIFY COLUMN status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending'");
            }
            // For SQLite and other databases, no action needed as enum is handled by application layer
        }
    }
};
