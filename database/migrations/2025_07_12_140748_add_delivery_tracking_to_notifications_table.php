<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->enum('delivery_status', ['pending', 'sent', 'failed', 'token_invalid'])->default('pending')->after('sent_at');
            $table->string('fcm_message_id')->nullable()->after('delivery_status');
            $table->text('delivery_error')->nullable()->after('fcm_message_id');
            $table->timestamp('delivery_attempted_at')->nullable()->after('delivery_error');
            $table->integer('delivery_attempts')->default(0)->after('delivery_attempted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn([
                'delivery_status',
                'fcm_message_id',
                'delivery_error',
                'delivery_attempted_at',
                'delivery_attempts'
            ]);
        });
    }
};
