<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reporter_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('reported_user_id')->constrained('users')->onDelete('cascade');
            $table->enum('category', [
                'inappropriate_behavior',
                'fake_profile',
                'harassment',
                'spam',
                'inappropriate_content',
                'scam_fraud',
                'underage_user',
                'violence_threats',
                'hate_speech',
                'other'
            ]);
            $table->text('description')->nullable();
            $table->string('evidence_file')->nullable();
            $table->enum('status', ['pending', 'under_review', 'resolved', 'dismissed'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['reporter_id', 'created_at']);
            $table->index(['reported_user_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['category', 'created_at']);
            $table->index(['reviewed_by', 'reviewed_at']);

            // Prevent duplicate reports within 24 hours
            $table->unique(['reporter_id', 'reported_user_id', 'category'], 'unique_daily_report');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_reports');
    }
};
