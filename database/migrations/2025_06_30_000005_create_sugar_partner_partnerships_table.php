<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugar_partner_partnerships', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user1_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('user2_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('exchange_id')->constrained('sugar_partner_exchanges')->onDelete('cascade');
            $table->enum('status', ['active', 'ended_by_user1', 'ended_by_user2', 'ended_mutual'])->default('active');
            $table->text('end_reason')->nullable();
            $table->foreignId('ended_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('chat_enabled')->default(true);
            $table->timestamp('started_at');
            $table->timestamp('ended_at')->nullable();
            $table->timestamps();

            // Ensure unique partnership between two users
            $table->unique(['user1_id', 'user2_id']);
            
            // Index for faster queries
            $table->index(['status', 'chat_enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugar_partner_partnerships');
    }
};
