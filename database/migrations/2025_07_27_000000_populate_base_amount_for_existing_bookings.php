<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\TimeSpendingBooking;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Populate base_amount for existing bookings where it's null
        TimeSpendingBooking::whereNull('base_amount')
            ->chunkById(100, function ($bookings) {
                foreach ($bookings as $booking) {
                    $baseAmount = $booking->hourly_rate * $booking->duration_hours;
                    $booking->update(['base_amount' => $baseAmount]);
                }
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Set base_amount back to null for all bookings
        TimeSpendingBooking::query()->update(['base_amount' => null]);
    }
};
