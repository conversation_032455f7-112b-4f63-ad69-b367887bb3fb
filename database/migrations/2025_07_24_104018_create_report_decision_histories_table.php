<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_decision_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('report_id')->constrained('user_reports')->onDelete('cascade');
            $table->foreignId('admin_id')->constrained('users')->onDelete('cascade');

            // Decision details
            $table->enum('action_type', [
                'initial_decision',
                'status_change',
                'suspension_applied',
                'suspension_lifted',
                'warning_sent',
                'decision_reversed',
                'admin_notes_updated'
            ]);
            $table->string('previous_status')->nullable();
            $table->string('new_status')->nullable();
            $table->text('reason')->nullable();
            $table->text('admin_notes')->nullable();

            // Suspension related fields
            $table->boolean('suspension_applied')->default(false);
            $table->boolean('suspension_lifted')->default(false);
            $table->string('suspension_duration')->nullable();
            $table->text('suspension_reason')->nullable();

            // Warning related fields
            $table->boolean('warning_sent')->default(false);
            $table->text('warning_message')->nullable();

            // Metadata for additional context
            $table->json('metadata')->nullable();
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['report_id', 'created_at']);
            $table->index(['admin_id', 'created_at']);
            $table->index(['action_type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_decision_histories');
    }
};
