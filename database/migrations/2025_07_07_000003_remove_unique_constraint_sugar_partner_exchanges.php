<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the table exists before trying to modify it
        if (!Schema::hasTable('sugar_partner_exchanges')) {
            return;
        }

        try {
            // Use Laravel's schema builder to drop the unique constraint
            Schema::table('sugar_partner_exchanges', function (Blueprint $table) {
                $table->dropUnique(['user1_id', 'user2_id']);
            });
        } catch (Exception $e) {
            // Constraint might not exist, which is fine
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add the unique constraint
        Schema::table('sugar_partner_exchanges', function (Blueprint $table) {
            $table->unique(['user1_id', 'user2_id']);
        });
    }
};
