<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_notification_preferences', function (Blueprint $table) {
            // Add browser permission status and FCM token fields
            $table->enum('browser_permission_status', ['default', 'granted', 'denied'])->default('default')->after('timezone');
            $table->text('fcm_token')->nullable()->after('browser_permission_status');
        });

        // Update all existing defaults to false (disabled by default)
        // Use database-agnostic approach for changing column defaults
        if (DB::getDriverName() === 'mysql') {
            DB::statement('ALTER TABLE user_notification_preferences
                MODIFY COLUMN push_notifications_enabled BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_general BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_payment BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_match BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_partner_swapping BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_booking BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_meeting_reminders BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN push_subscription BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_notifications_enabled BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_general BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_payment BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_match BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_partner_swapping BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_booking BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_meeting_reminders BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_subscription BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_notifications_enabled BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_general BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_payment BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_match BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_partner_swapping BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_booking BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_meeting_reminders BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN inapp_subscription BOOLEAN DEFAULT FALSE
            ');
        }
        // For SQLite and other databases, we'll just update existing records
        // The default values for new records will be handled by the model

        // Update existing records to have all notifications disabled by default
        // This ensures existing users also have notifications disabled unless they explicitly enable them
        DB::table('user_notification_preferences')->update([
            'push_notifications_enabled' => false,
            'push_general' => false,
            'push_payment' => false,
            'push_match' => false,
            'push_partner_swapping' => false,
            'push_booking' => false,
            'push_meeting_reminders' => false,
            'push_subscription' => false,
            'email_notifications_enabled' => false,
            'email_general' => false,
            'email_payment' => false,
            'email_match' => false,
            'email_partner_swapping' => false,
            'email_booking' => false,
            'email_meeting_reminders' => false,
            'email_subscription' => false,
            'inapp_notifications_enabled' => false,
            'inapp_general' => false,
            'inapp_payment' => false,
            'inapp_match' => false,
            'inapp_partner_swapping' => false,
            'inapp_booking' => false,
            'inapp_meeting_reminders' => false,
            'inapp_subscription' => false,
            'browser_permission_status' => 'default'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_notification_preferences', function (Blueprint $table) {
            $table->dropColumn(['browser_permission_status', 'fcm_token']);
        });

        // Restore original defaults (all enabled)
        if (DB::getDriverName() === 'mysql') {
            DB::statement('ALTER TABLE user_notification_preferences
                MODIFY COLUMN push_notifications_enabled BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_general BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_payment BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_match BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_partner_swapping BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_booking BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_meeting_reminders BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN push_subscription BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_notifications_enabled BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_general BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_payment BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_match BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_partner_swapping BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_booking BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN email_meeting_reminders BOOLEAN DEFAULT FALSE,
                MODIFY COLUMN email_subscription BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_notifications_enabled BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_general BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_payment BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_match BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_partner_swapping BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_booking BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_meeting_reminders BOOLEAN DEFAULT TRUE,
                MODIFY COLUMN inapp_subscription BOOLEAN DEFAULT TRUE
            ');
        }
    }
};
