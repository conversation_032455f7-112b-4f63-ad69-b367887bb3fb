<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, get all exchanges that need reordering
        $exchanges = DB::table('sugar_partner_exchanges')
            ->whereRaw('user1_id > user2_id')
            ->get();

        foreach ($exchanges as $exchange) {
            // Check if there's already an exchange with the correct ordering
            $existingCorrectOrder = DB::table('sugar_partner_exchanges')
                ->where('user1_id', $exchange->user2_id)
                ->where('user2_id', $exchange->user1_id)
                ->first();

            if ($existingCorrectOrder) {
                // If there's already an exchange with correct ordering, 
                // we need to merge the data or delete the duplicate
                
                // For now, let's delete the incorrectly ordered one
                // since both are completed exchanges
                DB::table('sugar_partner_exchanges')
                    ->where('id', $exchange->id)
                    ->delete();
                    
                echo "Deleted duplicate exchange ID {$exchange->id}\n";
            } else {
                // Swap the user IDs to correct ordering
                DB::table('sugar_partner_exchanges')
                    ->where('id', $exchange->id)
                    ->update([
                        'user1_id' => $exchange->user2_id,
                        'user2_id' => $exchange->user1_id,
                    ]);
                    
                echo "Fixed ordering for exchange ID {$exchange->id}\n";
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not easily reversible since we're deleting duplicates
        // Manual intervention would be required to restore deleted exchanges
    }
};
