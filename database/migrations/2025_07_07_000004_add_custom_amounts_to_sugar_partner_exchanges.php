<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sugar_partner_exchanges', function (Blueprint $table) {
            $table->decimal('custom_user1_amount', 10, 2)->nullable()->after('exchange_price');
            $table->decimal('custom_user2_amount', 10, 2)->nullable()->after('custom_user1_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sugar_partner_exchanges', function (Blueprint $table) {
            $table->dropColumn(['custom_user1_amount', 'custom_user2_amount']);
        });
    }
};
