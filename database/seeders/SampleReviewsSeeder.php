<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\RatingReview;
use App\Models\TimeSpendingBooking;
use Carbon\Carbon;

class SampleReviewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();
        
        if ($users->count() < 2) {
            $this->command->info('Need at least 2 users to create sample reviews. Please create more users first.');
            return;
        }

        // Sample review texts
        $reviewTexts = [
            'Had an amazing time! Very professional and friendly. Highly recommend.',
            'Great experience overall. Good conversation and very punctual.',
            'Wonderful person to spend time with. Made me feel comfortable throughout.',
            'Excellent service and very accommodating. Will definitely book again.',
            'Really enjoyed our time together. Very genuine and easy to talk to.',
            'Professional and courteous. Exceeded my expectations.',
            'Had a lovely time. Very respectful and understanding.',
            'Great personality and very engaging. Time flew by quickly.',
            'Very reliable and trustworthy. Felt safe and comfortable.',
            'Outstanding experience! Couldn\'t have asked for better.',
            'Friendly and approachable. Made the experience very enjoyable.',
            'Very patient and understanding. Great listener.',
            'Exceeded all expectations. Truly a wonderful person.',
            'Professional yet warm and friendly. Perfect balance.',
            'Had such a good time! Looking forward to booking again.',
        ];

        $createdReviews = 0;

        foreach ($users as $user) {
            // Skip if user doesn't have time spending enabled
            if (!$user->is_time_spending_enabled) {
                continue;
            }

            // Create 3-8 reviews for each user
            $reviewCount = rand(3, 8);
            
            for ($i = 0; $i < $reviewCount; $i++) {
                // Get a random reviewer (different from the reviewee)
                $reviewer = $users->where('id', '!=', $user->id)->random();
                
                // Create a fake completed booking first
                $bookingDate = Carbon::now()->subDays(rand(1, 90));
                $duration = [0.5, 1, 1.5, 2, 3, 4][rand(0, 5)];
                
                $baseAmount = $user->hourly_rate * $duration;
                $booking = TimeSpendingBooking::create([
                    'client_id' => $reviewer->id,
                    'provider_id' => $user->id,
                    'booking_date' => $bookingDate,
                    'duration_hours' => $duration,
                    'hourly_rate' => $user->hourly_rate,
                    'base_amount' => $baseAmount,
                    'total_amount' => $baseAmount, // For sample data, no platform fees
                    'meeting_location' => $user->service_location ?? 'Sample Location',
                    'notes' => 'Sample booking for review testing',
                    'status' => 'confirmed',
                    'provider_status' => 'accepted',
                    'payment_status' => 'paid',
                    'created_at' => $bookingDate,
                    'updated_at' => $bookingDate,
                ]);

                // Create the review
                $rating = $this->getWeightedRating(); // Bias towards higher ratings
                $reviewText = rand(0, 100) < 80 ? $reviewTexts[array_rand($reviewTexts)] : null; // 80% chance of text review
                $isAnonymous = rand(0, 100) < 20; // 20% chance of anonymous review

                RatingReview::create([
                    'booking_id' => $booking->id,
                    'reviewer_id' => $reviewer->id,
                    'reviewee_id' => $user->id,
                    'rating' => $rating,
                    'review_text' => $reviewText,
                    'is_anonymous' => $isAnonymous,
                    'is_approved' => true,
                    'created_at' => $bookingDate->addHours(rand(1, 48)), // Review created 1-48 hours after booking
                    'updated_at' => $bookingDate->addHours(rand(1, 48)),
                ]);

                $createdReviews++;
            }
        }

        $this->command->info("Created {$createdReviews} sample reviews successfully!");
    }

    /**
     * Get a weighted rating (bias towards higher ratings)
     */
    private function getWeightedRating(): int
    {
        $weights = [
            1 => 5,   // 5% chance
            2 => 10,  // 10% chance  
            3 => 15,  // 15% chance
            4 => 30,  // 30% chance
            5 => 40,  // 40% chance
        ];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $rating => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $rating;
            }
        }

        return 5; // Default to 5 stars
    }
}
