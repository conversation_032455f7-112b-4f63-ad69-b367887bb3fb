<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserReport;
use Illuminate\Auth\Access\Response;

class ReportPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // Any authenticated user can create reports
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can modify decisions on the report.
     */
    public function modifyDecision(User $user, UserReport $userReport): bool
    {
        // Only admins can modify decisions
        if (!$user->isAdmin()) {
            return false;
        }

        // Can't modify decisions on pending or under review reports
        // (use regular actions for those)
        if ($userReport->isPending() || $userReport->isUnderReview()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can reverse decisions on the report.
     */
    public function reverseDecision(User $user, UserReport $userReport): bool
    {
        // Only admins can reverse decisions
        if (!$user->isAdmin()) {
            return false;
        }

        // Can only reverse resolved or dismissed reports
        return $userReport->isResolved() || $userReport->isDismissed();
    }

    /**
     * Determine whether the user can lift suspensions.
     */
    public function liftSuspension(User $user, UserReport $userReport): bool
    {
        // Only admins can lift suspensions
        if (!$user->isAdmin()) {
            return false;
        }

        // Can only lift suspension if the reported user is actually suspended
        return $userReport->reportedUser && $userReport->reportedUser->is_suspended;
    }

    /**
     * Determine whether the user can change report status.
     */
    public function changeStatus(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view decision history.
     */
    public function viewDecisionHistory(User $user, UserReport $userReport): bool
    {
        return $user->isAdmin();
    }
}
