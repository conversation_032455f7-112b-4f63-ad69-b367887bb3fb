<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UnsuspendExpiredUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:unsuspend-expired {--dry-run : Show what would be unsuspended without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically unsuspend users whose suspension period has expired';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking for expired user suspensions...');
        
        $currentTime = Carbon::now();
        $isDryRun = $this->option('dry-run');
        
        // Find users who are suspended but their suspension period has expired
        $expiredSuspensions = User::where('is_suspended', true)
            ->whereNotNull('suspension_end_date')
            ->where('suspension_end_date', '<=', $currentTime)
            ->get();

        if ($expiredSuspensions->isEmpty()) {
            $this->info('✅ No expired suspensions found.');
            return 0;
        }

        $this->info("📋 Found {$expiredSuspensions->count()} expired suspension(s):");
        
        $unsuspendedCount = 0;
        
        foreach ($expiredSuspensions as $user) {
            $expiredHours = $currentTime->diffInHours($user->suspension_end_date);
            
            $this->line("   👤 {$user->name} (ID: {$user->id}) - Expired {$expiredHours} hours ago");
            
            if (!$isDryRun) {
                // Store original suspension data for logging
                $originalSuspensionReason = $user->suspension_reason;
                $originalSuspensionEndDate = $user->suspension_end_date;

                // Unsuspend the user
                $user->update([
                    'is_suspended' => false,
                    'suspension_end_date' => null,
                    'suspension_reason' => null,
                ]);

                // Send notification to user about unsuspension
                $this->sendUnsuspensionNotification($user, $originalSuspensionReason, $expiredHours);

                // Log the automatic unsuspension
                Log::info('User automatically unsuspended', [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'original_suspension_reason' => $originalSuspensionReason,
                    'original_suspension_end_date' => $originalSuspensionEndDate,
                    'expired_hours' => $expiredHours,
                    'unsuspended_at' => $currentTime,
                    'action' => 'automatic_unsuspension',
                ]);

                $unsuspendedCount++;
                $this->info("      ✅ Unsuspended successfully");
            } else {
                $this->info("      🔍 Would be unsuspended (dry-run mode)");
            }
        }
        
        if (!$isDryRun) {
            $this->info("🎉 Successfully unsuspended {$unsuspendedCount} user(s).");
            
            // Log summary
            Log::info('Automatic unsuspension completed', [
                'total_found' => $expiredSuspensions->count(),
                'total_unsuspended' => $unsuspendedCount,
                'executed_at' => $currentTime,
            ]);
        } else {
            $this->info("🔍 Dry-run completed. {$expiredSuspensions->count()} user(s) would be unsuspended.");
        }
        
        return 0;
    }

    /**
     * Send notification to user about automatic unsuspension
     */
    private function sendUnsuspensionNotification(User $user, ?string $originalReason, int $expiredHours): void
    {
        try {
            // Create in-app notification
            Notification::create([
                'user_id' => $user->id,
                'type' => 'account_unsuspended',
                'title' => 'Account Reactivated',
                'message' => 'Your account suspension has been automatically lifted.',
                'body' => 'Good news! Your account suspension period has ended and your account has been automatically reactivated. You can now use all platform features normally.',
                'data' => [
                    'suspension_reason' => $originalReason,
                    'expired_hours' => $expiredHours,
                    'unsuspended_at' => now()->toISOString(),
                    'action_type' => 'automatic_unsuspension',
                    'action_url' => url('/'),
                    'action_text' => 'Go to Dashboard'
                ],
            ]);

            $this->info("      📧 Notification sent to user");

        } catch (\Exception $e) {
            Log::error('Failed to send unsuspension notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->warn("      ⚠️  Failed to send notification: " . $e->getMessage());
        }
    }
}
