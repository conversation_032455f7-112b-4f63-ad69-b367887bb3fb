<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationPermissionService;
use App\Models\User;

class TestNotificationPermissionSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:test-permission {--user-id= : Test with specific user ID} {--clear : Clear tracking data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the notification permission tracking system';

    private NotificationPermissionService $permissionService;

    public function __construct(NotificationPermissionService $permissionService)
    {
        parent::__construct();
        $this->permissionService = $permissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔔 Testing Notification Permission System');
        $this->newLine();

        // Clear tracking data if requested
        if ($this->option('clear')) {
            $this->permissionService->clearTrackingData();
            $this->info('✅ Tracking data cleared');
            $this->newLine();
        }

        // Get user for testing
        $userId = $this->option('user-id');
        if (!$userId) {
            $users = User::where('role', 'user')->limit(3)->get();
            if ($users->isEmpty()) {
                $this->error('No users found for testing');
                return;
            }
            
            $this->info('Available users for testing:');
            foreach ($users as $user) {
                $this->info("  - ID: {$user->id}, Name: {$user->name}, Email: {$user->email}");
            }
            
            $userId = $this->ask('Enter user ID to test with', $users->first()->id);
        }

        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        // Simulate login
        auth()->login($user);
        $this->info("Testing with user: {$user->name} ({$user->email})");
        $this->newLine();

        // Test permission status
        $this->info('📊 Current Permission Status:');
        $status = $this->permissionService->getPermissionStatus();
        
        foreach ($status as $key => $value) {
            $displayValue = is_bool($value) ? ($value ? 'Yes' : 'No') : ($value ?? 'N/A');
            $this->info("  - " . ucwords(str_replace('_', ' ', $key)) . ": {$displayValue}");
        }
        $this->newLine();

        // Test scenarios
        $this->info('🧪 Testing Permission Request Scenarios:');
        
        // Scenario 1: Can request permission
        if ($this->permissionService->canRequestPermission()) {
            $this->info('  ✅ Can request permission now');
            
            if ($this->confirm('Simulate permission request?', true)) {
                $this->permissionService->recordPermissionRequest();
                $this->info('  📝 Permission request recorded');
                
                $grantedChoice = $this->choice('Simulate user response:', ['granted', 'denied'], 0);
                
                if ($grantedChoice === 'granted') {
                    $this->permissionService->recordPermissionGranted();
                    $this->info('  ✅ Permission granted recorded');
                } else {
                    $this->permissionService->recordPermissionDenied();
                    $this->info('  ❌ Permission denied recorded');
                }
            }
        } else {
            $timeUntilNext = $this->permissionService->getTimeUntilNextRequest();
            $minutes = ceil($timeUntilNext / 60);
            $this->warn("  ⏳ Cannot request permission (cooldown: {$minutes} minutes remaining)");
        }
        
        $this->newLine();

        // Test login scenarios
        $this->info('🚪 Testing Login Scenarios:');
        
        if ($this->permissionService->shouldRequestOnLogin()) {
            $this->info('  ✅ Should show permission modal on login');
        } else {
            $this->warn('  ⏸️  Should NOT show permission modal on login');
            
            $reasons = [];
            if (!$this->permissionService->canRequestPermission()) {
                $reasons[] = 'Cooldown period active';
            }
            if ($this->permissionService->hasUserDeniedBefore()) {
                $reasons[] = 'User denied before in this session';
            }
            if ($this->permissionService->hasUserGrantedBefore()) {
                $reasons[] = 'User already granted in this session';
            }
            
            if (!empty($reasons)) {
                $this->info('    Reasons: ' . implode(', ', $reasons));
            }
        }
        
        $this->newLine();

        // Show updated status
        $this->info('📊 Updated Permission Status:');
        $updatedStatus = $this->permissionService->getPermissionStatus();
        
        foreach ($updatedStatus as $key => $value) {
            $displayValue = is_bool($value) ? ($value ? 'Yes' : 'No') : ($value ?? 'N/A');
            $this->info("  - " . ucwords(str_replace('_', ' ', $key)) . ": {$displayValue}");
        }
        
        $this->newLine();
        $this->info('🎉 Permission system test completed!');
        
        if ($this->confirm('Clear tracking data for next test?', false)) {
            $this->permissionService->clearTrackingData();
            $this->info('✅ Tracking data cleared');
        }
    }
}
