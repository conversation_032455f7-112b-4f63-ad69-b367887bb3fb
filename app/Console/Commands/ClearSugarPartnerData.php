<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerExchangePayment;
use App\Models\SugarPartnerRejection;
use App\Models\SugarPartnerPartnership;
use App\Models\SugarPartnerHardReject;

class ClearSugarPartnerData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sugar-partner:clear-data {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all Sugar Partner exchange data (exchanges, payments, rejections, partnerships)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will permanently delete ALL Sugar Partner data (exchanges, payments, rejections, partnerships). Are you sure?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('Starting Sugar Partner data cleanup...');

        DB::beginTransaction();
        try {
            // Get counts before deletion
            $exchangeCount = SugarPartnerExchange::count();
            $paymentCount = SugarPartnerExchangePayment::count();
            $rejectionCount = SugarPartnerRejection::count();
            $partnershipCount = SugarPartnerPartnership::count();
            $hardRejectCount = SugarPartnerHardReject::count();

            $this->info("Found data to delete:");
            $this->line("- Sugar Partner Exchanges: {$exchangeCount}");
            $this->line("- Sugar Partner Payments: {$paymentCount}");
            $this->line("- Sugar Partner Rejections: {$rejectionCount}");
            $this->line("- Sugar Partner Partnerships: {$partnershipCount}");
            $this->line("- Sugar Partner Hard Rejects: {$hardRejectCount}");

            // Delete in correct order to respect foreign key constraints
            $this->info('Deleting Sugar Partner Partnerships...');
            SugarPartnerPartnership::query()->delete();

            $this->info('Deleting Sugar Partner Hard Rejects...');
            SugarPartnerHardReject::query()->delete();

            $this->info('Deleting Sugar Partner Rejections...');
            SugarPartnerRejection::query()->delete();

            $this->info('Deleting Sugar Partner Exchange Payments...');
            SugarPartnerExchangePayment::query()->delete();

            $this->info('Deleting Sugar Partner Exchanges...');
            SugarPartnerExchange::query()->delete();

            // Also clear related notifications
            $this->info('Deleting Sugar Partner related notifications...');
            DB::table('notifications')
                ->whereIn('type', [
                    'sugar_partner_exchange_initiated',
                    'sugar_partner_payment_received',
                    'sugar_partner_profile_shared',
                    'sugar_partner_response_received',
                    'sugar_partner_match_success',
                    'sugar_partner_mismatch',
                    'sugar_partner_rejection',
                    'sugar_partner_partnership_ended'
                ])
                ->delete();

            DB::commit();

            $this->info('✅ Sugar Partner data cleared successfully!');
            $this->line("Deleted:");
            $this->line("- {$exchangeCount} exchanges");
            $this->line("- {$paymentCount} payments");
            $this->line("- {$rejectionCount} rejections");
            $this->line("- {$partnershipCount} partnerships");
            $this->line("- {$hardRejectCount} hard rejects");
            $this->line("- Related notifications");

            return 0;

        } catch (\Exception $e) {
            DB::rollback();
            $this->error('❌ Failed to clear Sugar Partner data: ' . $e->getMessage());
            return 1;
        }
    }
}
