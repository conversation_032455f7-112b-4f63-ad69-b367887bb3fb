<?php

namespace App\Console\Commands;

use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use App\Models\RatingReview;
use App\Helpers\FeatureHelper;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendReviewReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reviews:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send review reminders for completed meetings';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Check if rating & review system is active
        if (!FeatureHelper::isRatingReviewSystemActive()) {
            $this->info('Rating & Review system is not active. Skipping review reminders.');
            return self::SUCCESS;
        }

        $this->info('Sending review reminders for completed meetings...');

        // Get completed bookings from the last 7 days that haven't been reviewed
        $completedBookings = TimeSpendingBooking::with(['client', 'provider'])
            ->where('provider_status', 'accepted')
            ->where('payment_status', 'paid')
            ->get()
            ->filter(function($booking) {
                // Calculate end time and check if booking is completed but within last 7 days
                $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
                $bookingEndTime = Carbon::parse($booking->booking_date)->addHours($durationHours);
                $sevenDaysAgo = Carbon::now()->subDays(7);

                return $bookingEndTime->isPast() && $bookingEndTime->isAfter($sevenDaysAgo);
            });

        $remindersSent = 0;

        foreach ($completedBookings as $booking) {
            // Send reminder to client if they haven't reviewed
            if (RatingReview::canReviewBooking($booking->id, $booking->client_id)) {
                $this->sendReviewReminder($booking, $booking->client_id, $booking->provider->name);
                $remindersSent++;
            }

            // Send reminder to provider if they haven't reviewed
            if (RatingReview::canReviewBooking($booking->id, $booking->provider_id)) {
                $this->sendReviewReminder($booking, $booking->provider_id, $booking->client->name);
                $remindersSent++;
            }
        }

        $this->info("Review reminders sent: {$remindersSent}");

        return self::SUCCESS;
    }

    /**
     * Send a review reminder notification.
     */
    private function sendReviewReminder($booking, $userId, $otherUserName): void
    {
        try {
            Notification::createReviewReminder($userId, $booking, $otherUserName);
        } catch (\Exception $e) {
            Log::error('Failed to send review reminder', [
                'booking_id' => $booking->id,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
