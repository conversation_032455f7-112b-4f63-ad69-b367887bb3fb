<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSpendingBooking;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutoRejectBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:auto-cancel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically cancel unaccepted bookings where the scheduled start time or meeting time has passed and process refunds';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Check if auto-cancellation is enabled
            if (!config('booking.auto_cancellation.enabled', true)) {
                return 0;
            }

            // Get current time in IST
            $currentTime = Carbon::now('Asia/Kolkata');

            // Find bookings that should be auto-cancelled
            // Criteria: paid bookings where provider hasn't responded and start time has passed
            $startTimeExpiredBookings = TimeSpendingBooking::with(['client', 'provider'])
                ->where('provider_status', 'pending')  // Provider hasn't accepted/rejected
                ->where('payment_status', 'paid')      // Booking is paid
                ->whereIn('status', ['confirmed', 'pending'])  // Include both confirmed and pending statuses
                ->where('booking_date', '<=', $currentTime)  // Start time has passed
                ->get();

            // Find bookings where the entire meeting time (start + duration) has passed
            $meetingEndExpiredBookings = TimeSpendingBooking::with(['client', 'provider'])
                ->where('provider_status', 'pending')
                ->where('payment_status', 'paid')
                ->whereIn('status', ['confirmed', 'pending'])
                ->get()
                ->filter(function($booking) {
                    // Check if the entire meeting time (start + duration) has passed
                    return $booking->isPastMeetingTime();
                });

            // Combine both collections and remove duplicates
            $bookingsToCancel = $startTimeExpiredBookings->merge($meetingEndExpiredBookings)->unique('id');

            if ($bookingsToCancel->isEmpty()) {
                return 0;
            }

            $processedCount = 0;
            $errorCount = 0;

            foreach ($bookingsToCancel as $booking) {
                try {
                    // Double-check that booking still needs cancellation (prevent race conditions)
                    $freshBooking = TimeSpendingBooking::find($booking->id);
                    if (!$freshBooking || $freshBooking->provider_status !== 'pending' || $freshBooking->payment_status !== 'paid') {
                        continue; // Skip if already processed
                    }

                    DB::transaction(function () use ($freshBooking) {
                        $this->processAutoCancellation($freshBooking);
                    });

                    $processedCount++;

                } catch (\Exception $e) {
                    $errorCount++;

                    Log::error('Auto-cancellation failed for booking', [
                        'booking_id' => $booking->id,
                        'client_id' => $booking->client_id,
                        'provider_id' => $booking->provider_id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            if ($processedCount > 0) {
                Log::info("Auto-cancellation completed: {$processedCount} bookings processed, {$errorCount} errors");
            }

            return 0;

        } catch (\Exception $e) {
            Log::error('Auto-cancellation process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }

    /**
     * Process auto-cancellation for a single booking.
     */
    private function processAutoCancellation(TimeSpendingBooking $booking): void
    {
        // Determine the cancellation reason based on the type of expiration
        $isPastMeetingTime = $booking->isPastMeetingTime();
        $rejectionReason = $isPastMeetingTime
            ? 'Auto-cancelled: Meeting time has passed without provider response'
            : 'Auto-cancelled: Provider did not respond before meeting start time';

        // Update booking status to auto-cancelled
        $booking->update([
            'status' => 'auto_cancelled',
            'provider_status' => 'rejected',
            'rejection_reason' => $rejectionReason,
            'provider_responded_at' => now(),
            'cancelled_at' => now(),
            'cancellation_reason' => $rejectionReason,
        ]);

        // Process refund using the model's centralized method
        $refundReason = $isPastMeetingTime
            ? 'Meeting time passed - Auto-cancelled'
            : 'Provider did not respond before meeting start time';

        // Use the model's processRefund method for consistency and duplicate prevention
        $refundProcessed = $booking->processRefund($refundReason);

        if (!$refundProcessed) {
            Log::warning('Auto-reject refund failed for booking', [
                'booking_id' => $booking->id,
                'payment_status' => $booking->payment_status,
                'refund_status' => $booking->refund_status,
                'reason' => $refundReason
            ]);
        }

        // Send appropriate notification based on cancellation type
        if ($isPastMeetingTime) {
            $this->sendMeetingTimePassedNotification($booking, $clientWallet->fresh()->balance);
        } else {
            $this->sendClientNotification($booking, $clientWallet->fresh()->balance);
        }

        // Send notification to provider about missed opportunity (if enabled)
        if (config('booking.auto_cancellation.send_provider_notification', true)) {
            $this->sendProviderNotification($booking);
        }

        // Log critical auto-cancellation for admin tracking
        Log::warning('Booking auto-cancelled due to provider non-response', [
            'booking_id' => $booking->id,
            'refund_amount' => $booking->total_amount,
            'cancellation_type' => $isPastMeetingTime ? 'meeting_time_passed' : 'start_time_passed',
        ]);
    }

    /**
     * Send notification to client about auto-cancellation and refund.
     */
    private function sendClientNotification(TimeSpendingBooking $booking, float $walletBalance): void
    {
        Notification::create([
            'user_id' => $booking->client_id,
            'type' => 'booking_auto_cancelled',
            'title' => 'Booking Auto-Cancelled - Refund Processed',
            'message' => "Your booking with {$booking->provider->name} for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled. ₹{$booking->total_amount} has been refunded to your wallet.",
            'body' => "We're sorry to inform you that your booking request with {$booking->provider->name} for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled. The provider did not respond to your request before the scheduled meeting time. The full amount of ₹{$booking->total_amount} has been refunded to your wallet. You can use this amount for future bookings.",
            'data' => [
                'booking_id' => $booking->id,
                'provider_id' => $booking->provider_id,
                'provider_name' => $booking->provider->name,
                'booking_date' => $booking->booking_date->toISOString(),
                'booking_time' => $booking->booking_date->format('h:i A'),
                'duration_hours' => $booking->duration_hours,
                'refund_amount' => $booking->total_amount,
                'wallet_balance' => $walletBalance,
                'auto_cancelled' => true,
                'reason' => 'provider_no_response',
                'meeting_location' => $booking->meeting_location,
            ],
        ]);
    }

    /**
     * Send notification for meeting time passed scenarios.
     */
    private function sendMeetingTimePassedNotification(TimeSpendingBooking $booking, float $walletBalance): void
    {
        Notification::create([
            'user_id' => $booking->client_id,
            'type' => 'booking_auto_cancelled',
            'title' => 'Booking Auto-Cancelled',
            'message' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled because the meeting time has passed. You have been refunded ₹{$booking->total_amount}.",
            'body' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled because the meeting time has passed. You have been refunded ₹{$booking->total_amount}.",
            'data' => [
                'booking_id' => $booking->id,
                'provider_id' => $booking->provider_id,
                'provider_name' => $booking->provider->name ?? 'Provider',
                'booking_date' => $booking->booking_date->format('M d, Y'),
                'booking_time' => $booking->booking_date->format('h:i A'),
                'refund_amount' => $booking->total_amount,
                'wallet_balance' => $walletBalance,
                'auto_cancelled' => true,
                'reason' => 'meeting_time_passed',
            ],
        ]);
    }

    /**
     * Send notification to provider about missed booking opportunity.
     */
    private function sendProviderNotification(TimeSpendingBooking $booking): void
    {
        Notification::create([
            'user_id' => $booking->provider_id,
            'type' => 'booking_opportunity_missed',
            'title' => 'Missed Booking Opportunity',
            'message' => "You missed a booking opportunity with {$booking->client->name} for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}. The booking was automatically cancelled.",
            'body' => "You had a booking request from {$booking->client->name} for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} worth ₹{$booking->total_amount}. Since you didn't respond before the scheduled meeting time, the booking was automatically cancelled and the client has been refunded. Please respond to booking requests promptly to avoid missing future opportunities.",
            'data' => [
                'booking_id' => $booking->id,
                'client_id' => $booking->client_id,
                'client_name' => $booking->client->name,
                'booking_date' => $booking->booking_date->toISOString(),
                'booking_time' => $booking->booking_date->format('h:i A'),
                'duration_hours' => $booking->duration_hours,
                'missed_amount' => $booking->total_amount,
                'provider_amount' => $booking->provider_amount,
                'auto_cancelled' => true,
                'reason' => 'provider_no_response',
                'meeting_location' => $booking->meeting_location,
            ],
        ]);
    }
}
