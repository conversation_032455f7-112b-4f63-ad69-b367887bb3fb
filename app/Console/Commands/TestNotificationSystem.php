<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\ComprehensiveNotificationService;
use App\Services\FirebaseService;

class TestNotificationSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:test {--user-id= : Test with specific user ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the comprehensive notification system';

    private ComprehensiveNotificationService $notificationService;
    private FirebaseService $firebaseService;

    public function __construct(ComprehensiveNotificationService $notificationService, FirebaseService $firebaseService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
        $this->firebaseService = $firebaseService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔔 Testing Comprehensive Notification System');
        $this->newLine();

        // Test Firebase Configuration
        $this->info('1. Testing Firebase Configuration...');
        $firebaseTest = $this->firebaseService->testConfiguration();

        if ($firebaseTest['success']) {
            $this->info('   ✅ Firebase configuration is valid');
        } else {
            $this->error('   ❌ Firebase configuration failed: ' . $firebaseTest['error']);
        }
        $this->newLine();

        // Test Database Schema
        $this->info('2. Testing Database Schema...');
        try {
            $userCount = User::count();
            $preferencesCount = \App\Models\UserNotificationPreference::count();
            $notificationsCount = \App\Models\Notification::count();

            $this->info("   ✅ Users: {$userCount}");
            $this->info("   ✅ Notification Preferences: {$preferencesCount}");
            $this->info("   ✅ Notifications: {$notificationsCount}");
        } catch (\Exception $e) {
            $this->error('   ❌ Database schema error: ' . $e->getMessage());
        }
        $this->newLine();

        // Test User Selection
        $userId = $this->option('user-id');
        if (!$userId) {
            $users = User::where('role', 'user')->limit(5)->get();
            if ($users->isEmpty()) {
                $this->error('No users found for testing');
                return;
            }

            $this->info('3. Available users for testing:');
            foreach ($users as $user) {
                $this->info("   - ID: {$user->id}, Name: {$user->name}, Email: {$user->email}");
            }

            $userId = $this->ask('Enter user ID to test with', $users->first()->id);
        }

        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing with user: {$user->name} ({$user->email})");
        $this->newLine();

        // Test Notification Preferences
        $this->info('4. Testing Notification Preferences...');
        try {
            $preferences = $user->getNotificationPreferences();
            $this->info('   ✅ User notification preferences loaded');
            $this->info("   - Push notifications: " . ($preferences->push_notifications_enabled ? 'Enabled' : 'Disabled'));
            $this->info("   - Email notifications: " . ($preferences->email_notifications_enabled ? 'Enabled' : 'Disabled'));
            $this->info("   - In-app notifications: " . ($preferences->inapp_notifications_enabled ? 'Enabled' : 'Disabled'));
        } catch (\Exception $e) {
            $this->error('   ❌ Notification preferences error: ' . $e->getMessage());
        }
        $this->newLine();

        // Test Notification Sending
        $this->info('5. Testing Notification Sending...');

        $testNotifications = [
            ['type' => 'general', 'title' => 'Test General Notification', 'body' => 'This is a test general notification'],
            ['type' => 'payment', 'title' => 'Test Payment Notification', 'body' => 'This is a test payment notification'],
            ['type' => 'match', 'title' => 'Test Match Notification', 'body' => 'This is a test match notification'],
        ];

        foreach ($testNotifications as $testNotification) {
            $this->info("   Testing {$testNotification['type']} notification...");

            $result = $this->notificationService->sendNotification(
                $user,
                $testNotification['title'],
                $testNotification['body'],
                $testNotification['type'],
                ['test' => true],
                ['inapp', 'push'] // Test both channels
            );

            foreach ($result as $channel => $channelResult) {
                if ($channelResult['success']) {
                    $this->info("     ✅ {$channel}: Success");
                } elseif (isset($channelResult['skipped']) && $channelResult['skipped']) {
                    $this->warn("     ⚠️  {$channel}: Skipped - {$channelResult['reason']}");
                } else {
                    $this->error("     ❌ {$channel}: Failed - " . ($channelResult['error'] ?? 'Unknown error'));
                }
            }
        }
        $this->newLine();

        // Test Statistics
        $this->info('6. Testing Statistics...');
        try {
            $stats = $this->notificationService->getStatistics();
            $this->info('   ✅ Statistics loaded successfully');
            $this->info("   - Total notifications: {$stats['total_notifications']}");
            $this->info("   - Notifications today: {$stats['notifications_today']}");
            $this->info("   - Users with FCM tokens: {$stats['users_with_fcm_tokens']}");
            $this->info("   - Firebase success rate: {$stats['firebase_stats']['success_rate']}%");
        } catch (\Exception $e) {
            $this->error('   ❌ Statistics error: ' . $e->getMessage());
        }
        $this->newLine();

        $this->info('🎉 Notification system test completed!');
        $this->info('Check the admin panel for detailed statistics and delivery status.');
    }
}
