<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class StartScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scheduler:start {--daemon : Run as daemon}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the Laravel scheduler to run scheduled tasks automatically';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('daemon')) {
            $this->runAsDaemon();
        } else {
            $this->runOnce();
        }
    }

    /**
     * Run scheduler once.
     */
    private function runOnce()
    {
        $this->info('Running scheduled tasks once...');
        
        $exitCode = $this->call('schedule:run');
        
        if ($exitCode === 0) {
            $this->info('Scheduled tasks completed successfully.');
        } else {
            $this->error('Some scheduled tasks failed.');
        }
    }

    /**
     * Run scheduler as daemon.
     */
    private function runAsDaemon()
    {
        $this->info('Starting Laravel scheduler daemon...');
        $this->info('Press Ctrl+C to stop the scheduler.');
        $this->info('This will run scheduled tasks automatically every minute.');

        while (true) {
            try {
                $this->call('schedule:run');
            } catch (\Exception $e) {
                $this->error('Scheduler error: ' . $e->getMessage());
                \Log::error('Scheduler daemon error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Sleep for 60 seconds before next check
            sleep(60);
        }
    }
}
