<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSpendingBooking;
use Carbon\Carbon;

class ProcessAutoEndMeetings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meetings:auto-end {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically end meetings that have passed their scheduled end time';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $this->info('Processing automatic meeting end...');

        // Find meetings that should be auto-ended
        $meetingsToEnd = TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
            ->where('provider_status', 'accepted')
            ->where('payment_status', 'paid')
            ->whereNotIn('status', [
                'auto_completed',
                'auto_ended_partial', 
                'auto_ended_no_photos',
                'manually_ended',
                'cancelled',
                'disputed'
            ])
            ->get()
            ->filter(function($booking) {
                return $booking->shouldBeAutoEnded();
            });

        if ($meetingsToEnd->isEmpty()) {
            $this->info('No meetings found that need to be auto-ended.');
            return 0;
        }

        $this->info("Found {$meetingsToEnd->count()} meetings to process:");

        $processedCount = 0;
        $errorCount = 0;

        foreach ($meetingsToEnd as $booking) {
            $photoStatus = $booking->getPhotoVerificationStatus();
            $endTime = $booking->getScheduledEndTime();
            
            $this->line("Processing Booking #{$booking->id}:");
            $this->line("  - Client: {$booking->client->name}");
            $this->line("  - Provider: {$booking->provider->name}");
            $this->line("  - Scheduled End: {$endTime->format('Y-m-d H:i:s')}");
            $this->line("  - Photos Status: " . ($photoStatus['has_all_photos'] ? 'All photos' : 
                       ($photoStatus['has_any_photos'] ? 'Partial photos' : 'No photos')));

            if (!$isDryRun) {
                if ($booking->processAutoEnd()) {
                    $this->info("  ✓ Successfully processed");
                    $processedCount++;
                } else {
                    $this->error("  ✗ Failed to process");
                    $errorCount++;
                }
            } else {
                $this->info("  → Would be processed (dry run)");
                $processedCount++;
            }
            
            $this->line('');
        }

        if (!$isDryRun) {
            $this->info("Processing complete:");
            $this->info("  - Successfully processed: {$processedCount}");
            if ($errorCount > 0) {
                $this->error("  - Errors: {$errorCount}");
            }
        } else {
            $this->info("Dry run complete - {$processedCount} meetings would be processed");
        }

        return 0;
    }
}
