<?php

namespace App\Jobs;

use App\Models\PushSubscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Notifications\DatabaseNotification;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class SendPushNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 30;

    public function __construct(
        private PushSubscription $subscription,
        private DatabaseNotification $notification
    ) {}

    public function handle(): void
    {
        try {
            // Check if subscription is still active
            if (!$this->subscription->is_active) {
                return;
            }

            // Get notification data
            $data = $this->notification->data;
            
            // Create Firebase message
            $message = CloudMessage::new()
                ->withNotification(
                    Notification::create(
                        $data['title'],
                        $data['message']
                    )
                    ->withImageUrl($data['icon'] ?? null)
                )
                ->withData([
                    'notification_id' => $this->notification->id,
                    'category' => $data['category'],
                    'priority' => $data['priority'],
                    'action_data' => json_encode($data['action_data'] ?? []),
                    'sound' => $data['sound'] ?? 'default',
                    'click_action' => $this->getClickAction($data),
                ])
                ->withWebPushConfig([
                    'notification' => [
                        'title' => $data['title'],
                        'body' => $data['message'],
                        'icon' => $data['icon'] ?? '/images/notification-icon.png',
                        'badge' => '/images/notification-badge.png',
                        'sound' => $data['sound'] ?? 'default',
                        'tag' => $data['category'],
                        'renotify' => true,
                        'requireInteraction' => $data['priority'] === 'high',
                        'actions' => $this->getNotificationActions($data),
                        'data' => [
                            'notification_id' => $this->notification->id,
                            'url' => $this->getClickAction($data),
                        ],
                    ],
                    'fcm_options' => [
                        'link' => $this->getClickAction($data),
                    ],
                ]);

            // Send via Firebase
            $factory = (new Factory)->withServiceAccount(config('firebase.credentials'));
            $messaging = $factory->createMessaging();
            
            $result = $messaging->send($message);

            // Update notification as sent
            $this->notification->update([
                'is_push_sent' => true,
                'sent_at' => now(),
            ]);

            // Update subscription last used
            $this->subscription->updateLastUsed();

        } catch (\Exception $e) {
            // Mark notification as failed
            $this->notification->update([
                'failed_at' => now(),
                'failure_reason' => $e->getMessage(),
            ]);

            // If subscription is invalid, mark as inactive
            if ($this->isSubscriptionError($e)) {
                $this->subscription->markInactive();
            }

            throw $e;
        }
    }

    /**
     * Get click action URL for notification
     */
    private function getClickAction(array $data): string
    {
        $actionData = $data['action_data'] ?? [];
        
        return match($data['category']) {
            'payment' => $actionData['payment_url'] ?? '/profile/payments',
            'match' => $actionData['profile_url'] ?? '/matches',
            'partner_swapping' => $actionData['exchange_url'] ?? '/sugar-partner',
            'booking_request' => $actionData['booking_url'] ?? '/bookings',
            'meeting_reminder' => $actionData['meeting_url'] ?? '/meetings',
            'subscription' => $actionData['subscription_url'] ?? '/profile/subscription',
            'email_verification' => $actionData['verification_url'] ?? '/email/verify',
            default => $actionData['url'] ?? '/notifications',
        };
    }

    /**
     * Get notification actions based on category
     */
    private function getNotificationActions(array $data): array
    {
        return match($data['category']) {
            'match' => [
                [
                    'action' => 'view_profile',
                    'title' => 'View Profile',
                    'icon' => '/images/icons/profile.png',
                ],
                [
                    'action' => 'send_message',
                    'title' => 'Send Message',
                    'icon' => '/images/icons/message.png',
                ],
            ],
            'partner_swapping' => [
                [
                    'action' => 'accept',
                    'title' => 'Accept',
                    'icon' => '/images/icons/accept.png',
                ],
                [
                    'action' => 'decline',
                    'title' => 'Decline',
                    'icon' => '/images/icons/decline.png',
                ],
            ],
            'booking_request' => [
                [
                    'action' => 'accept_booking',
                    'title' => 'Accept',
                    'icon' => '/images/icons/accept.png',
                ],
                [
                    'action' => 'view_details',
                    'title' => 'View Details',
                    'icon' => '/images/icons/details.png',
                ],
            ],
            default => [
                [
                    'action' => 'view',
                    'title' => 'View',
                    'icon' => '/images/icons/view.png',
                ],
            ],
        };
    }

    /**
     * Check if the error is related to invalid subscription
     */
    private function isSubscriptionError(\Exception $e): bool
    {
        $message = strtolower($e->getMessage());
        
        return str_contains($message, 'invalid registration') ||
               str_contains($message, 'not registered') ||
               str_contains($message, 'invalid token') ||
               str_contains($message, 'unregistered');
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        // Mark notification as failed
        $this->notification->update([
            'failed_at' => now(),
            'failure_reason' => $exception->getMessage(),
        ]);

        // If it's a subscription error, mark subscription as inactive
        if ($this->isSubscriptionError($exception)) {
            $this->subscription->markInactive();
        }
    }
}
