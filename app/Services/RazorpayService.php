<?php

namespace App\Services;

use Razorpay\Api\Api;
use Razorpay\Api\Errors\SignatureVerificationError;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use Exception;

class RazorpayService
{
    protected $api;
    protected $keyId;
    protected $keySecret;

    public function __construct()
    {
        $this->keyId = config('razorpay.key_id') ?: Setting::get('razorpay_key_id');
        $this->keySecret = config('razorpay.key_secret') ?: Setting::get('razorpay_key_secret');
        
        if (empty($this->keyId) || empty($this->keySecret)) {
            throw new Exception('Razorpay credentials not configured');
        }
        
        $this->api = new Api($this->keyId, $this->keySecret);
    }

    /**
     * Create a Razorpay order
     */
    public function createOrder(array $orderData): array
    {
        try {
            // Validate required fields
            $this->validateOrderData($orderData);
            
            // Ensure amount is in paise (integer)
            $orderData['amount'] = (int) round($orderData['amount'] * 100);
            
            // Set default currency if not provided
            if (!isset($orderData['currency'])) {
                $orderData['currency'] = 'INR';
            }
            
            // Create the order
            $order = $this->api->order->create($orderData);
            
            Log::info('Razorpay order created successfully', [
                'order_id' => $order['id'],
                'amount' => $orderData['amount'],
                'receipt' => $orderData['receipt'] ?? null
            ]);
            
            return [
                'success' => true,
                'order' => $order,
                'razorpay_key' => $this->keyId
            ];
            
        } catch (Exception $e) {
            Log::error('Razorpay order creation failed', [
                'error' => $e->getMessage(),
                'order_data' => $orderData,
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify payment signature
     */
    public function verifyPaymentSignature(array $attributes): bool
    {
        try {
            $this->api->utility->verifyPaymentSignature($attributes);
            
            Log::info('Payment signature verified successfully', [
                'payment_id' => $attributes['razorpay_payment_id'] ?? null,
                'order_id' => $attributes['razorpay_order_id'] ?? null
            ]);
            
            return true;
            
        } catch (SignatureVerificationError $e) {
            Log::error('Payment signature verification failed', [
                'error' => $e->getMessage(),
                'attributes' => $attributes
            ]);
            
            return false;
        }
    }

    /**
     * Fetch payment details
     */
    public function fetchPayment(string $paymentId): ?array
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);
            return $payment->toArray();
            
        } catch (Exception $e) {
            Log::error('Failed to fetch payment details', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Fetch order details
     */
    public function fetchOrder(string $orderId): ?array
    {
        try {
            $order = $this->api->order->fetch($orderId);
            return $order->toArray();
            
        } catch (Exception $e) {
            Log::error('Failed to fetch order details', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Get Razorpay checkout options
     */
    public function getCheckoutOptions(array $orderData, array $userData = []): array
    {
        $options = [
            'key' => $this->keyId,
            'amount' => (int) round($orderData['amount'] * 100),
            'currency' => $orderData['currency'] ?? 'INR',
            'name' => config('app.name', 'SettingWala'),
            'description' => $orderData['description'] ?? 'Payment',
            'order_id' => $orderData['order_id'],
            'theme' => config('razorpay.options.theme'),
            'modal' => config('razorpay.options.modal'),
            'retry' => config('razorpay.options.retry'),
            'timeout' => config('razorpay.options.timeout'),
            'remember_customer' => config('razorpay.options.remember_customer'),
            'readonly' => config('razorpay.options.readonly')
        ];

        // Add prefill data if user data is provided
        if (!empty($userData)) {
            $options['prefill'] = [
                'name' => $userData['name'] ?? '',
                'email' => $userData['email'] ?? '',
                'contact' => $userData['contact'] ?? ''
            ];
        }

        // Add image if available
        if (isset($orderData['image'])) {
            $options['image'] = $orderData['image'];
        }

        return $options;
    }

    /**
     * Validate order data
     */
    private function validateOrderData(array $orderData): void
    {
        if (!isset($orderData['amount']) || $orderData['amount'] <= 0) {
            throw new Exception('Invalid amount provided');
        }

        if (!isset($orderData['receipt']) || empty($orderData['receipt'])) {
            throw new Exception('Receipt is required');
        }
    }

    /**
     * Get Razorpay key ID
     */
    public function getKeyId(): string
    {
        return $this->keyId;
    }

    /**
     * Check if credentials are configured
     */
    public static function isConfigured(): bool
    {
        $keyId = config('razorpay.key_id') ?: Setting::get('razorpay_key_id');
        $keySecret = config('razorpay.key_secret') ?: Setting::get('razorpay_key_secret');
        
        return !empty($keyId) && !empty($keySecret);
    }
}
