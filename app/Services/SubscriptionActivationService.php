<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSubscription;
use App\Services\UserChangeLogService;
use App\Services\ComprehensiveNotificationService;
use App\Enums\NotificationType;

class SubscriptionActivationService
{
    protected UserChangeLogService $changeLogService;
    protected ComprehensiveNotificationService $notificationService;

    public function __construct(
        UserChangeLogService $changeLogService,
        ComprehensiveNotificationService $notificationService
    ) {
        $this->changeLogService = $changeLogService;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle automatic profile configuration when Time Spending subscription is activated.
     *
     * @param User $user
     * @param UserSubscription $subscription
     * @return array
     */
    public function configureProfileForTimeSpendingSubscription(User $user, UserSubscription $subscription): array
    {
        $changes = [];
        $originalData = $user->toArray();

        // Track changes for is_public_profile
        if (!$user->is_public_profile) {
            $changes['is_public_profile'] = [
                'old' => false,
                'new' => true
            ];
        }

        // Track changes for show_gallery_images
        if (!$user->show_gallery_images) {
            $changes['show_gallery_images'] = [
                'old' => false,
                'new' => true
            ];
        }

        // Apply the changes if any
        if (!empty($changes)) {
            $user->update([
                'is_public_profile' => true,
                'show_gallery_images' => true,
            ]);

            // Log the changes (only once)
            $this->changeLogService->logSubscriptionProfileChanges($user, $changes, 'time_spending');

            // Send notification to user
            $this->sendProfileConfigurationNotification($user, $subscription, $changes);
        }

        return $changes;
    }

    /**
     * Send notification to user about automatic profile configuration.
     *
     * @param User $user
     * @param UserSubscription $subscription
     * @param array $changes
     * @return void
     */
    protected function sendProfileConfigurationNotification(User $user, UserSubscription $subscription, array $changes): void
    {
        $changedSettings = [];

        if (isset($changes['is_public_profile'])) {
            $changedSettings[] = 'Profile visibility set to public';
        }

        if (isset($changes['show_gallery_images'])) {
            $changedSettings[] = 'Gallery images enabled for display';
        }

        if (empty($changedSettings)) {
            return;
        }

        $settingsText = implode(' and ', $changedSettings);
        $planName = $subscription->subscriptionPlan->name ?? 'Time Spending';

        $title = 'Profile Settings Updated';
        $message = "Your {$settingsText} due to your {$planName} subscription activation. This ensures maximum visibility for Time Spending bookings.";

        $actionData = [
            'subscription_id' => $subscription->id,
            'plan_name' => $planName,
            'changes' => $changes,
            'redirect_url' => route('profile.edit', ['tab' => 'settings'])
        ];

        // Create notification directly without using the comprehensive service for now
        try {
            \App\Models\Notification::create([
                'user_id' => $user->id,
                'type' => NotificationType::SUBSCRIPTION_PROFILE_UPDATED->value,
                'title' => $title,
                'message' => $message,
                'data' => json_encode($actionData),
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the subscription activation
            \Log::error('Failed to send profile configuration notification: ' . $e->getMessage());
        }
    }

    /**
     * Check if user profile needs configuration for Time Spending.
     *
     * @param User $user
     * @return bool
     */
    public function needsProfileConfiguration(User $user): bool
    {
        return !$user->is_public_profile || !$user->show_gallery_images;
    }

    /**
     * Get profile configuration requirements for Time Spending.
     *
     * @param User $user
     * @return array
     */
    public function getProfileConfigurationRequirements(User $user): array
    {
        $requirements = [];

        if (!$user->is_public_profile) {
            $requirements[] = [
                'field' => 'is_public_profile',
                'current' => false,
                'required' => true,
                'description' => 'Profile must be public to receive Time Spending bookings'
            ];
        }

        if (!$user->show_gallery_images) {
            $requirements[] = [
                'field' => 'show_gallery_images',
                'current' => false,
                'required' => true,
                'description' => 'Gallery images must be enabled to showcase yourself to potential clients'
            ];
        }

        return $requirements;
    }

    /**
     * Revert automatic profile changes when subscription expires.
     *
     * @param User $user
     * @param UserSubscription $subscription
     * @return array
     */
    public function revertProfileChangesOnExpiry(User $user, UserSubscription $subscription): array
    {
        // Get the original changes made during activation
        $activationChanges = $user->changeLogs()
            ->where('action', 'subscription_auto_update')
            ->whereJsonContains('metadata->subscription_type', 'time_spending')
            ->get();

        $revertedChanges = [];

        // Only revert if user doesn't have another active subscription
        $hasOtherActiveSubscription = UserSubscription::where('user_id', $user->id)
            ->where('id', '!=', $subscription->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->exists();

        if (!$hasOtherActiveSubscription) {
            // Note: We don't automatically revert profile settings on expiry
            // as users might want to keep their profile public
            // This method is here for future use if needed
        }

        return $revertedChanges;
    }
}
