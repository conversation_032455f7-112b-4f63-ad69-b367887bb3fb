<?php

namespace App\Services;

use App\Services\ComprehensiveNotificationService;
use App\Enums\NotificationType;
use App\Models\User;

class NotificationTriggerService
{
    public function __construct(
        private ComprehensiveNotificationService $notificationService
    ) {}

    /**
     * Trigger payment-related notifications
     */
    public function triggerPaymentNotification(User $user, string $type, array $paymentData): void
    {
        $this->notificationService->sendPaymentNotification($user, $type, $paymentData);
    }

    /**
     * Trigger match-related notifications
     */
    public function triggerMatchNotification(User $user, string $type, array $matchData): void
    {
        $this->notificationService->sendMatchNotification($user, $type, $matchData);
    }

    /**
     * Trigger booking-related notifications
     */
    public function triggerBookingNotification(User $user, string $type, array $bookingData): void
    {
        $this->notificationService->sendBookingNotification($user, $type, $bookingData);
    }

    /**
     * Trigger partner exchange notifications
     */
    public function triggerPartnerExchangeNotification(User $user, string $type, array $exchangeData): void
    {
        $notificationType = match($type) {
            'request' => NotificationType::PARTNER_EXCHANGE_REQUEST,
            'response' => NotificationType::PARTNER_EXCHANGE_RESPONSE,
            'confirmed' => NotificationType::PARTNER_EXCHANGE_CONFIRMED,
            'cancelled' => NotificationType::PARTNER_EXCHANGE_CANCELLED,
            default => NotificationType::PARTNER_EXCHANGE_REQUEST,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getPartnerExchangeMessage($type, $exchangeData);

        $this->notificationService->sendNotification($user, $notificationType, $title, $message, $exchangeData);
    }

    /**
     * Trigger meeting reminder notifications
     */
    public function triggerMeetingReminderNotification(User $user, string $type, array $meetingData): void
    {
        $notificationType = match($type) {
            'upcoming' => NotificationType::MEETING_UPCOMING,
            'location_reminder' => NotificationType::MEETING_LOCATION_REMINDER,
            'time_confirmation' => NotificationType::MEETING_TIME_CONFIRMATION,
            'cancelled' => NotificationType::MEETING_CANCELLED,
            'rescheduled' => NotificationType::MEETING_RESCHEDULED,
            default => NotificationType::MEETING_UPCOMING,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getMeetingReminderMessage($type, $meetingData);

        $this->notificationService->sendNotification($user, $notificationType, $title, $message, $meetingData);
    }

    /**
     * Trigger subscription-related notifications
     */
    public function triggerSubscriptionNotification(User $user, string $type, array $subscriptionData): void
    {
        $notificationType = match($type) {
            'expiring' => NotificationType::SUBSCRIPTION_EXPIRING,
            'expired' => NotificationType::SUBSCRIPTION_EXPIRED,
            'renewed' => NotificationType::SUBSCRIPTION_RENEWED,
            'plan_changed' => NotificationType::PLAN_CHANGED,
            'renewal_reminder' => NotificationType::RENEWAL_REMINDER,
            default => NotificationType::SUBSCRIPTION_EXPIRING,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getSubscriptionMessage($type, $subscriptionData);

        $this->notificationService->sendNotification($user, $notificationType, $title, $message, $subscriptionData);
    }

    /**
     * Trigger email verification notifications
     */
    public function triggerEmailVerificationNotification(User $user, string $type, array $emailData = []): void
    {
        $notificationType = match($type) {
            'required' => NotificationType::EMAIL_VERIFICATION_REQUIRED,
            'success' => NotificationType::EMAIL_VERIFICATION_SUCCESS,
            'failed' => NotificationType::EMAIL_VERIFICATION_FAILED,
            'changed' => NotificationType::EMAIL_CHANGED,
            default => NotificationType::EMAIL_VERIFICATION_REQUIRED,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getEmailVerificationMessage($type, $emailData);

        $this->notificationService->sendNotification($user, $notificationType, $title, $message, $emailData);
    }

    /**
     * Trigger general notifications
     */
    public function triggerGeneralNotification(User $user, string $type, array $data = []): void
    {
        $notificationType = match($type) {
            'announcement' => NotificationType::GENERAL_ANNOUNCEMENT,
            'app_update' => NotificationType::APP_UPDATE,
            'feature_introduction' => NotificationType::FEATURE_INTRODUCTION,
            'system_maintenance' => NotificationType::SYSTEM_MAINTENANCE,
            default => NotificationType::GENERAL_ANNOUNCEMENT,
        };

        $title = $data['title'] ?? $notificationType->getDisplayName();
        $message = $data['message'] ?? $this->getGeneralMessage($type, $data);

        $this->notificationService->sendNotification($user, $notificationType, $title, $message, $data);
    }

    // Helper methods for generating messages

    private function getPartnerExchangeMessage(string $type, array $data): string
    {
        return match($type) {
            'request' => "New partner exchange request from {$data['requester_name']}.",
            'response' => "{$data['responder_name']} responded to your partner exchange request.",
            'confirmed' => "Partner exchange with {$data['partner_name']} has been confirmed!",
            'cancelled' => "Partner exchange with {$data['partner_name']} has been cancelled.",
            default => "Partner exchange notification",
        };
    }

    private function getMeetingReminderMessage(string $type, array $data): string
    {
        return match($type) {
            'upcoming' => "Reminder: Meeting with {$data['partner_name']} in {$data['time_until']}.",
            'location_reminder' => "Don't forget: Meeting location is {$data['location']}.",
            'time_confirmation' => "Meeting time confirmed: {$data['meeting_time']} with {$data['partner_name']}.",
            'cancelled' => "Meeting with {$data['partner_name']} has been cancelled.",
            'rescheduled' => "Meeting with {$data['partner_name']} has been rescheduled to {$data['new_time']}.",
            default => "Meeting reminder",
        };
    }

    private function getSubscriptionMessage(string $type, array $data): string
    {
        return match($type) {
            'expiring' => "Your {$data['plan_name']} subscription expires in {$data['days_until']} days.",
            'expired' => "Your {$data['plan_name']} subscription has expired.",
            'renewed' => "Your {$data['plan_name']} subscription has been renewed successfully.",
            'plan_changed' => "Your subscription has been changed to {$data['new_plan']}.",
            'renewal_reminder' => "Don't forget to renew your {$data['plan_name']} subscription.",
            default => "Subscription notification",
        };
    }

    private function getEmailVerificationMessage(string $type, array $data): string
    {
        return match($type) {
            'required' => "Please verify your email address to complete your account setup.",
            'success' => "Your email address has been verified successfully!",
            'failed' => "Email verification failed. Please try again.",
            'changed' => "Your email address has been changed to {$data['new_email']}.",
            default => "Email verification notification",
        };
    }

    private function getGeneralMessage(string $type, array $data): string
    {
        return match($type) {
            'announcement' => $data['content'] ?? "New announcement available.",
            'app_update' => "A new app update is available with exciting features!",
            'feature_introduction' => "Check out our new feature: {$data['feature_name']}!",
            'system_maintenance' => "Scheduled maintenance: {$data['maintenance_time']}.",
            default => "General notification",
        };
    }

    /**
     * Trigger notifications for multiple users
     */
    public function triggerBulkNotification(array $users, string $category, string $type, array $data): void
    {
        foreach ($users as $user) {
            match($category) {
                'payment' => $this->triggerPaymentNotification($user, $type, $data),
                'match' => $this->triggerMatchNotification($user, $type, $data),
                'booking' => $this->triggerBookingNotification($user, $type, $data),
                'partner_exchange' => $this->triggerPartnerExchangeNotification($user, $type, $data),
                'meeting_reminder' => $this->triggerMeetingReminderNotification($user, $type, $data),
                'subscription' => $this->triggerSubscriptionNotification($user, $type, $data),
                'email_verification' => $this->triggerEmailVerificationNotification($user, $type, $data),
                'general' => $this->triggerGeneralNotification($user, $type, $data),
                default => null,
            };
        }
    }

    /**
     * Trigger broadcast notification to all users
     */
    public function triggerBroadcastNotification(string $category, string $type, array $data): int
    {
        $users = User::where('is_active', true)->get();
        $this->triggerBulkNotification($users->toArray(), $category, $type, $data);
        
        return $users->count();
    }
}
