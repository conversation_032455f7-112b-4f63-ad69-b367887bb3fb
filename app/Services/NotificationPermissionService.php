<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class NotificationPermissionService
{
    const PERMISSION_REQUEST_COOLDOWN = 3600; // 1 hour in seconds
    const SESSION_KEY = 'notification_permission_last_requested';
    const CACHE_PREFIX = 'notification_permission_';

    /**
     * Check if we can request notification permission for the current user session
     */
    public function canRequestPermission(): bool
    {
        $lastRequested = $this->getLastRequestTime();
        
        if (!$lastRequested) {
            return true;
        }

        $cooldownExpires = $lastRequested->addSeconds(self::PERMISSION_REQUEST_COOLDOWN);
        return Carbon::now()->isAfter($cooldownExpires);
    }

    /**
     * Record that permission was requested for this session
     */
    public function recordPermissionRequest(): void
    {
        $now = Carbon::now();
        
        // Store in session for immediate access
        Session::put(self::SESSION_KEY, $now->toISOString());
        
        // Also store in cache with user ID if authenticated
        if (auth()->check()) {
            $cacheKey = self::CACHE_PREFIX . auth()->id();
            Cache::put($cacheKey, $now->toISOString(), self::PERMISSION_REQUEST_COOLDOWN);
        }
    }

    /**
     * Get the last time permission was requested
     */
    public function getLastRequestTime(): ?Carbon
    {
        // First check session
        $sessionTime = Session::get(self::SESSION_KEY);
        if ($sessionTime) {
            return Carbon::parse($sessionTime);
        }

        // Then check cache if user is authenticated
        if (auth()->check()) {
            $cacheKey = self::CACHE_PREFIX . auth()->id();
            $cacheTime = Cache::get($cacheKey);
            if ($cacheTime) {
                return Carbon::parse($cacheTime);
            }
        }

        return null;
    }

    /**
     * Get time remaining until next permission request is allowed
     */
    public function getTimeUntilNextRequest(): ?int
    {
        $lastRequested = $this->getLastRequestTime();
        
        if (!$lastRequested) {
            return 0;
        }

        $cooldownExpires = $lastRequested->addSeconds(self::PERMISSION_REQUEST_COOLDOWN);
        $now = Carbon::now();
        
        if ($now->isAfter($cooldownExpires)) {
            return 0;
        }

        return $cooldownExpires->diffInSeconds($now);
    }

    /**
     * Check if this is the user's first login session (for smart timing)
     */
    public function isFirstLoginSession(): bool
    {
        return !Session::has('user_has_logged_in_before') && auth()->check();
    }

    /**
     * Mark that the user has logged in before
     */
    public function markUserAsLoggedInBefore(): void
    {
        Session::put('user_has_logged_in_before', true);
    }

    /**
     * Check if permission should be requested automatically on login
     */
    public function shouldRequestOnLogin(): bool
    {
        // Only request if:
        // 1. User is authenticated
        // 2. We can request permission (cooldown expired)
        // 3. Browser supports notifications
        // 4. Permission is not already granted
        
        if (!auth()->check()) {
            return false;
        }

        if (!$this->canRequestPermission()) {
            return false;
        }

        // Don't auto-request if user has explicitly denied before
        if ($this->hasUserDeniedBefore()) {
            return false;
        }

        return true;
    }

    /**
     * Check if user has denied permission before in this session
     */
    public function hasUserDeniedBefore(): bool
    {
        return Session::get('notification_permission_denied', false);
    }

    /**
     * Record that user denied permission
     */
    public function recordPermissionDenied(): void
    {
        Session::put('notification_permission_denied', true);
    }

    /**
     * Record that user granted permission
     */
    public function recordPermissionGranted(): void
    {
        Session::forget('notification_permission_denied');
        Session::put('notification_permission_granted', true);
    }

    /**
     * Check if user has granted permission in this session
     */
    public function hasUserGrantedBefore(): bool
    {
        return Session::get('notification_permission_granted', false);
    }

    /**
     * Clear all permission tracking data (useful for testing)
     */
    public function clearTrackingData(): void
    {
        Session::forget([
            self::SESSION_KEY,
            'notification_permission_denied',
            'notification_permission_granted',
            'user_has_logged_in_before'
        ]);

        if (auth()->check()) {
            $cacheKey = self::CACHE_PREFIX . auth()->id();
            Cache::forget($cacheKey);
        }
    }

    /**
     * Get permission status summary for debugging
     */
    public function getPermissionStatus(): array
    {
        return [
            'can_request' => $this->canRequestPermission(),
            'last_requested' => $this->getLastRequestTime()?->toISOString(),
            'time_until_next' => $this->getTimeUntilNextRequest(),
            'is_first_login' => $this->isFirstLoginSession(),
            'should_request_on_login' => $this->shouldRequestOnLogin(),
            'has_denied_before' => $this->hasUserDeniedBefore(),
            'has_granted_before' => $this->hasUserGrantedBefore(),
            'is_authenticated' => auth()->check(),
        ];
    }
}
