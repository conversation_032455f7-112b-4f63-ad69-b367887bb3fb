<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\Notification;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use Kreait\Firebase\Exception\MessagingException;
use Illuminate\Support\Facades\Log;

class FirebaseService
{
    private $messaging;
    private $isConfigured = false;

    public function __construct()
    {
        $this->initializeFirebase();
    }

    /**
     * Initialize Firebase with service account credentials
     */
    private function initializeFirebase()
    {
        try {
            $projectId = Setting::get('firebase_project_id');
            $clientEmail = Setting::get('firebase_client_email');
            $privateKey = Setting::get('firebase_private_key');

            if (empty($projectId) || empty($clientEmail) || empty($privateKey)) {
                Log::warning('Firebase credentials not configured');
                return;
            }

            // Create service account array
            $serviceAccount = [
                'type' => 'service_account',
                'project_id' => $projectId,
                'private_key_id' => '',
                'private_key' => $privateKey,
                'client_email' => $clientEmail,
                'client_id' => '',
                'auth_uri' => 'https://accounts.google.com/o/oauth2/auth',
                'token_uri' => 'https://oauth2.googleapis.com/token',
                'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
                'client_x509_cert_url' => "https://www.googleapis.com/robot/v1/metadata/x509/{$clientEmail}"
            ];

            $factory = (new Factory)->withServiceAccount($serviceAccount);
            $this->messaging = $factory->createMessaging();
            $this->isConfigured = true;

            Log::info('Firebase initialized successfully');
        } catch (\Exception $e) {
            Log::error('Failed to initialize Firebase: ' . $e->getMessage());
            $this->isConfigured = false;
        }
    }

    /**
     * Check if Firebase is properly configured
     */
    public function isConfigured(): bool
    {
        return $this->isConfigured;
    }

    /**
     * Send FCM notification to a single device
     */
    public function sendNotification(Notification $notification): array
    {
        if (!$this->isConfigured) {
            return [
                'success' => false,
                'error' => 'Firebase not configured'
            ];
        }

        $user = $notification->user;
        if (!$user || !$user->fcm_token) {
            return [
                'success' => false,
                'error' => 'No FCM token found for user'
            ];
        }

        try {
            $firebaseNotification = FirebaseNotification::create(
                $notification->title,
                $notification->body
            );

            $data = $notification->data ?? [];
            $data['notification_id'] = (string) $notification->id;
            $data['type'] = $notification->type;
            $data['click_action'] = 'FLUTTER_NOTIFICATION_CLICK';

            $message = CloudMessage::withTarget('token', $user->fcm_token)
                ->withNotification($firebaseNotification)
                ->withData($data);

            $result = $this->messaging->send($message);

            // Update notification as sent
            $notification->update([
                'sent_at' => now(),
                'delivery_status' => 'sent',
                'fcm_message_id' => $result
            ]);

            Log::info('FCM notification sent successfully', [
                'notification_id' => $notification->id,
                'user_id' => $user->id,
                'fcm_message_id' => $result
            ]);

            return [
                'success' => true,
                'message_id' => $result
            ];

        } catch (MessagingException $e) {
            $errorCode = $e->getCode();
            $errorMessage = $e->getMessage();

            // Handle specific FCM errors
            $deliveryStatus = 'failed';
            if (str_contains($errorMessage, 'registration-token-not-registered')) {
                $deliveryStatus = 'token_invalid';
                // Clear invalid FCM token
                $user->update(['fcm_token' => null]);
            }

            $notification->update([
                'delivery_status' => $deliveryStatus,
                'delivery_error' => $errorMessage
            ]);

            Log::error('FCM notification failed', [
                'notification_id' => $notification->id,
                'user_id' => $user->id,
                'error_code' => $errorCode,
                'error_message' => $errorMessage
            ]);

            return [
                'success' => false,
                'error' => $errorMessage,
                'error_code' => $errorCode
            ];

        } catch (\Exception $e) {
            $notification->update([
                'delivery_status' => 'failed',
                'delivery_error' => $e->getMessage()
            ]);

            Log::error('Unexpected error sending FCM notification', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send notifications to multiple devices
     */
    public function sendMulticastNotification(array $notifications): array
    {
        if (!$this->isConfigured) {
            return [
                'success' => false,
                'error' => 'Firebase not configured'
            ];
        }

        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($notifications as $notification) {
            $result = $this->sendNotification($notification);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'total' => count($notifications),
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'results' => $results
        ];
    }

    /**
     * Test Firebase configuration
     */
    public function testConfiguration(): array
    {
        if (!$this->isConfigured) {
            return [
                'success' => false,
                'error' => 'Firebase not configured'
            ];
        }

        try {
            // Try to validate a dummy token to test connection
            $dummyToken = 'dummy_token_for_testing';
            
            // This will fail but confirms Firebase connection works
            $this->messaging->validateRegistrationTokens([$dummyToken]);
            
            return [
                'success' => true,
                'message' => 'Firebase configuration is valid'
            ];
        } catch (\Exception $e) {
            // If it's a token validation error, Firebase is working
            if (str_contains($e->getMessage(), 'registration-token')) {
                return [
                    'success' => true,
                    'message' => 'Firebase configuration is valid'
                ];
            }

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get delivery statistics
     */
    public function getDeliveryStats(): array
    {
        $stats = [
            'total_sent' => Notification::whereNotNull('sent_at')->count(),
            'successful' => Notification::where('delivery_status', 'sent')->count(),
            'failed' => Notification::where('delivery_status', 'failed')->count(),
            'invalid_tokens' => Notification::where('delivery_status', 'token_invalid')->count(),
            'pending' => Notification::whereNull('sent_at')->count(),
        ];

        $stats['success_rate'] = $stats['total_sent'] > 0 
            ? round(($stats['successful'] / $stats['total_sent']) * 100, 2) 
            : 0;

        return $stats;
    }
}
