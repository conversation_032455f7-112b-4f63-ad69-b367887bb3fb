<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserChangeLog;
use Carbon\Carbon;
use Illuminate\Http\Request;

class UserChangeLogService
{
    /**
     * Log changes made to user profile.
     *
     * @param User $user
     * @param array $originalData
     * @param array $newData
     * @param Request $request
     * @return void
     */
    public function logProfileChanges(User $user, array $originalData, array $newData, Request $request): void
    {
        $trackableFields = [
            'name',
            'email',
            'contact_number',
            'gender',
            'date_of_birth',
            'interests',
            'expectation',
            'profile_picture',
            'is_public_profile',
            'show_gallery_images'
        ];

        foreach ($trackableFields as $field) {
            $oldValue = $originalData[$field] ?? null;
            $newValue = $newData[$field] ?? null;

            // Skip if values are the same
            if ($oldValue === $newValue) {
                continue;
            }

            // Handle special cases
            if ($field === 'profile_picture') {
                // For profile picture, we only log if there was actually a change
                if ($request->hasFile('profile_picture')) {
                    $this->createChangeLog($user, $field, $oldValue, 'new_image_uploaded', $request);
                }
                continue;
            }

            // Handle date formatting
            if ($field === 'date_of_birth') {
                $oldValue = $this->formatDateValue($oldValue);
                $newValue = $this->formatDateValue($newValue);
            }

            // Create change log entry
            $this->createChangeLog($user, $field, $oldValue, $newValue, $request);
        }
    }

    /**
     * Create a change log entry.
     *
     * @param User $user
     * @param string $fieldName
     * @param mixed $oldValue
     * @param mixed $newValue
     * @param Request $request
     * @return UserChangeLog
     */
    private function createChangeLog(User $user, string $fieldName, $oldValue, $newValue, Request $request): UserChangeLog
    {
        return UserChangeLog::create([
            'user_id' => $user->id,
            'field_name' => $fieldName,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'action' => 'update',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
    }

    /**
     * Log user creation.
     *
     * @param User $user
     * @param Request $request
     * @return void
     */
    public function logUserCreation(User $user, Request $request): void
    {
        UserChangeLog::create([
            'user_id' => $user->id,
            'field_name' => 'account',
            'old_value' => null,
            'new_value' => 'Account created',
            'action' => 'create',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
    }

    /**
     * Get formatted change logs for a user.
     *
     * @param User $user
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFormattedChangeLogs(User $user, int $limit = 10)
    {
        return $user->changeLogs()
            ->limit($limit)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'field_name' => $log->formatted_field_name,
                    'old_value' => $log->formatted_old_value,
                    'new_value' => $log->formatted_new_value,
                    'action' => ucfirst($log->action),
                    'created_at' => $log->created_at->diffForHumans(),
                    'created_at_formatted' => $log->created_at->format('M d, Y \a\t g:i A'),
                ];
            });
    }

    /**
     * Log subscription-related profile changes.
     *
     * @param User $user
     * @param array $changes
     * @param string $subscriptionType
     * @return void
     */
    public function logSubscriptionProfileChanges(User $user, array $changes, string $subscriptionType = 'time_spending'): void
    {
        foreach ($changes as $field => $change) {
            UserChangeLog::create([
                'user_id' => $user->id,
                'field_name' => $field,
                'old_value' => $change['old'] ? 'true' : 'false',
                'new_value' => $change['new'] ? 'true' : 'false',
                'action' => 'subscription_auto_update',
                'ip_address' => request()->ip() ?? '127.0.0.1',
                'user_agent' => request()->userAgent() ?? 'System',
                'metadata' => json_encode([
                    'subscription_type' => $subscriptionType,
                    'auto_updated' => true,
                    'reason' => 'Time Spending subscription activation'
                ])
            ]);
        }
    }

    /**
     * Format date value for storage.
     *
     * @param mixed $value
     * @return string|null
     */
    private function formatDateValue($value): ?string
    {
        if (empty($value)) {
            return null;
        }

        // If it's already a string, return as is
        if (is_string($value)) {
            return $value;
        }

        // If it's a Carbon instance or DateTime, format it
        if ($value instanceof Carbon || $value instanceof \DateTime) {
            return $value->format('Y-m-d');
        }

        // Try to parse as date and format
        try {
            return Carbon::parse($value)->format('Y-m-d');
        } catch (\Exception $e) {
            // If parsing fails, convert to string
            return (string) $value;
        }
    }
}
