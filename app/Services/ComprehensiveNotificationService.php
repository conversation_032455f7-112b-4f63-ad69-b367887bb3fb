<?php

namespace App\Services;

use App\Enums\NotificationCategory;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;
use App\Models\User;
use App\Models\NotificationPreference;
use App\Models\PushSubscription;
use App\Jobs\SendPushNotificationJob;
use Illuminate\Support\Facades\Notification;
use Illuminate\Notifications\DatabaseNotification;

class ComprehensiveNotificationService
{
    /**
     * Send a notification to a user
     */
    public function sendNotification(
        User $user,
        NotificationType $type,
        string $title,
        string $message,
        array $actionData = [],
        array $additionalData = []
    ): ?DatabaseNotification {
        $category = $type->getCategory();
        $priority = $type->getPriority();

        // Check if user has notifications enabled for this category
        if (!$this->shouldSendNotification($user, $category, $priority)) {
            return null;
        }

        // Create the notification data
        $notificationData = [
            'notification_type' => $type->value,
            'category' => $category->value,
            'priority' => $priority->value,
            'title' => $title,
            'message' => $message,
            'action_data' => $actionData,
            'icon' => $additionalData['icon'] ?? $category->getIcon(),
            'sound' => $this->getNotificationSound($user, $category),
            'scheduled_at' => $additionalData['scheduled_at'] ?? now(),
            'is_push_sent' => false,
            ...$additionalData
        ];

        // Create database notification
        $notification = $user->notifications()->create([
            'id' => \Str::uuid(),
            'type' => 'App\Notifications\CustomNotification',
            'data' => $notificationData,
            'notification_type' => $type->value,
            'category' => $category->value,
            'priority' => $priority->value,
            'title' => $title,
            'message' => $message,
            'action_data' => $actionData,
            'icon' => $notificationData['icon'],
            'sound' => $notificationData['sound'],
            'scheduled_at' => $notificationData['scheduled_at'],
        ]);

        // Send push notification if enabled
        if (NotificationPreference::isPushEnabledForUser($user, $category)) {
            $this->sendPushNotification($user, $notification, $type);
        }

        return $notification;
    }

    /**
     * Send push notification
     */
    private function sendPushNotification(User $user, DatabaseNotification $notification, NotificationType $type): void
    {
        $subscriptions = PushSubscription::getActiveForUser($user);

        if ($subscriptions->isEmpty()) {
            return;
        }

        $delay = $type->getPriority()->getDelay();

        foreach ($subscriptions as $subscription) {
            SendPushNotificationJob::dispatch($subscription, $notification)
                ->delay(now()->addSeconds($delay));
        }
    }

    /**
     * Check if notification should be sent
     */
    private function shouldSendNotification(User $user, NotificationCategory $category, NotificationPriority $priority): bool
    {
        // Always send urgent notifications
        if ($priority->bypassesPreferences()) {
            return true;
        }

        // Check user preferences
        $preference = NotificationPreference::getForUserAndCategory($user, $category);
        
        if (!$preference) {
            return $category->isEnabledByDefault();
        }

        return $preference->in_app_enabled && $preference->isAllowedNow();
    }

    /**
     * Get notification sound for user and category
     */
    private function getNotificationSound(User $user, NotificationCategory $category): ?string
    {
        $preference = NotificationPreference::getForUserAndCategory($user, $category);
        
        if (!$preference || !$preference->sound_enabled) {
            return null;
        }

        return $preference->getSound();
    }

    /**
     * Send notification to multiple users
     */
    public function sendToMultipleUsers(
        array $users,
        NotificationType $type,
        string $title,
        string $message,
        array $actionData = [],
        array $additionalData = []
    ): array {
        $notifications = [];

        foreach ($users as $user) {
            $notification = $this->sendNotification($user, $type, $title, $message, $actionData, $additionalData);
            if ($notification) {
                $notifications[] = $notification;
            }
        }

        return $notifications;
    }

    /**
     * Send broadcast notification to all users
     */
    public function sendBroadcast(
        NotificationType $type,
        string $title,
        string $message,
        array $actionData = [],
        array $additionalData = []
    ): int {
        $users = User::whereHas('notificationPreferences', function ($query) use ($type) {
            $query->where('category', $type->getCategory())
                  ->where('in_app_enabled', true);
        })->get();

        $notifications = $this->sendToMultipleUsers($users, $type, $title, $message, $actionData, $additionalData);

        return count($notifications);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(string $notificationId, User $user): bool
    {
        $notification = $user->notifications()->where('id', $notificationId)->first();
        
        if (!$notification) {
            return false;
        }

        $notification->markAsRead();
        return true;
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead(User $user): int
    {
        return $user->unreadNotifications()->update(['read_at' => now()]);
    }

    /**
     * Get unread notifications count for user
     */
    public function getUnreadCount(User $user): int
    {
        return $user->unreadNotifications()->count();
    }

    /**
     * Get notifications for user with pagination
     */
    public function getNotificationsForUser(User $user, int $perPage = 20): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get notifications by category for user
     */
    public function getNotificationsByCategory(User $user, NotificationCategory $category, int $perPage = 20): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $user->notifications()
            ->where('category', $category->value)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Delete old notifications
     */
    public function cleanupOldNotifications(int $daysOld = 30): int
    {
        return DatabaseNotification::where('created_at', '<', now()->subDays($daysOld))->delete();
    }

    /**
     * Get notification statistics for user
     */
    public function getNotificationStats(User $user): array
    {
        $total = $user->notifications()->count();
        $unread = $user->unreadNotifications()->count();
        $byCategory = $user->notifications()
            ->selectRaw('category, count(*) as count')
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();

        return [
            'total' => $total,
            'unread' => $unread,
            'read' => $total - $unread,
            'by_category' => $byCategory,
        ];
    }

    // Helper methods for specific notification types
    
    /**
     * Send payment notification
     */
    public function sendPaymentNotification(User $user, string $type, array $paymentData): ?DatabaseNotification
    {
        $notificationType = match($type) {
            'confirmed' => NotificationType::PAYMENT_CONFIRMATION,
            'failed' => NotificationType::PAYMENT_FAILED,
            'refund' => NotificationType::REFUND_PROCESSED,
            'wallet_updated' => NotificationType::WALLET_UPDATED,
            default => NotificationType::PAYMENT_CONFIRMATION,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getPaymentMessage($type, $paymentData);

        return $this->sendNotification($user, $notificationType, $title, $message, $paymentData);
    }

    /**
     * Send match notification
     */
    public function sendMatchNotification(User $user, string $type, array $matchData): ?DatabaseNotification
    {
        $notificationType = match($type) {
            'new_match' => NotificationType::NEW_MATCH,
            'mutual_like' => NotificationType::MUTUAL_LIKE,
            'profile_view' => NotificationType::PROFILE_VIEW,
            'like_received' => NotificationType::LIKE_RECEIVED,
            default => NotificationType::NEW_MATCH,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getMatchMessage($type, $matchData);

        return $this->sendNotification($user, $notificationType, $title, $message, $matchData);
    }

    /**
     * Send booking notification
     */
    public function sendBookingNotification(User $user, string $type, array $bookingData): ?DatabaseNotification
    {
        $notificationType = match($type) {
            'request_received' => NotificationType::BOOKING_REQUEST_RECEIVED,
            'confirmed' => NotificationType::BOOKING_CONFIRMED,
            'cancelled' => NotificationType::BOOKING_CANCELLED,
            'modified' => NotificationType::BOOKING_MODIFIED,
            'reminder' => NotificationType::BOOKING_REMINDER,
            default => NotificationType::BOOKING_REQUEST_RECEIVED,
        };

        $title = $notificationType->getDisplayName();
        $message = $this->getBookingMessage($type, $bookingData);

        return $this->sendNotification($user, $notificationType, $title, $message, $bookingData);
    }

    private function getPaymentMessage(string $type, array $data): string
    {
        return match($type) {
            'confirmed' => "Payment of {$data['amount']} has been confirmed.",
            'failed' => "Payment of {$data['amount']} failed. Please try again.",
            'refund' => "Refund of {$data['amount']} has been processed.",
            'wallet_updated' => "Your wallet has been updated with {$data['amount']}.",
            default => "Payment notification",
        };
    }

    private function getMatchMessage(string $type, array $data): string
    {
        return match($type) {
            'new_match' => "You have a new match with {$data['partner_name']}!",
            'mutual_like' => "It's a mutual like with {$data['partner_name']}!",
            'profile_view' => "{$data['viewer_name']} viewed your profile.",
            'like_received' => "{$data['liker_name']} liked your profile!",
            default => "New match notification",
        };
    }

    private function getBookingMessage(string $type, array $data): string
    {
        return match($type) {
            'request_received' => "New booking request from {$data['requester_name']}.",
            'confirmed' => "Your booking with {$data['partner_name']} is confirmed.",
            'cancelled' => "Booking with {$data['partner_name']} has been cancelled.",
            'modified' => "Your booking with {$data['partner_name']} has been modified.",
            'reminder' => "Reminder: Meeting with {$data['partner_name']} in {$data['time_until']}.",
            default => "Booking notification",
        };
    }
}
