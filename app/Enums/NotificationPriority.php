<?php

namespace App\Enums;

enum NotificationPriority: string
{
    case LOW = 'low';
    case MEDIUM = 'medium';
    case HIGH = 'high';
    case URGENT = 'urgent';

    /**
     * Get the display name for this priority
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::LOW => 'Low',
            self::MEDIUM => 'Medium',
            self::HIGH => 'High',
            self::URGENT => 'Urgent',
        };
    }

    /**
     * Get the color for this priority
     */
    public function getColor(): string
    {
        return match($this) {
            self::LOW => 'gray',
            self::MEDIUM => 'blue',
            self::HIGH => 'orange',
            self::URGENT => 'red',
        };
    }

    /**
     * Get the numeric value for sorting
     */
    public function getValue(): int
    {
        return match($this) {
            self::LOW => 1,
            self::MEDIUM => 2,
            self::HIGH => 3,
            self::URGENT => 4,
        };
    }

    /**
     * Check if this priority should bypass user preferences
     */
    public function bypassesPreferences(): bool
    {
        return match($this) {
            self::URGENT => true,
            default => false,
        };
    }

    /**
     * Get the delay in seconds before sending (for rate limiting)
     */
    public function getDelay(): int
    {
        return match($this) {
            self::URGENT => 0,
            self::HIGH => 5,
            self::MEDIUM => 30,
            self::LOW => 300, // 5 minutes
        };
    }
}
