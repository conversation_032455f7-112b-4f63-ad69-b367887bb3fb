<?php

namespace App\Enums;

enum NotificationCategory: string
{
    case GENERAL = 'general';
    case PAYMENT = 'payment';
    case MATCH = 'match';
    case PARTNER_SWAPPING = 'partner_swapping';
    case BOOKING_REQUEST = 'booking_request';
    case MEETING_REMINDER = 'meeting_reminder';
    case SUBSCRIPTION = 'subscription';
    case EMAIL_VERIFICATION = 'email_verification';

    /**
     * Get the display name for this category
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::GENERAL => 'General Notifications',
            self::PAYMENT => 'Payment Notifications',
            self::MATCH => 'Match Notifications',
            self::PARTNER_SWAPPING => 'Partner Swapping',
            self::BOOKING_REQUEST => 'Booking Requests',
            self::MEETING_REMINDER => 'Meeting Reminders',
            self::SUBSCRIPTION => 'Subscription Reminders',
            self::EMAIL_VERIFICATION => 'Email Verification',
        };
    }

    /**
     * Get the description for this category
     */
    public function getDescription(): string
    {
        return match($this) {
            self::GENERAL => 'System announcements, app updates, and feature introductions',
            self::PAYMENT => 'Payment confirmations, failed transactions, refunds, and wallet updates',
            self::MATCH => 'New matches, mutual likes, profile views, and match-related updates',
            self::PARTNER_SWAPPING => 'Activity partner exchange requests, responses, and confirmations',
            self::BOOKING_REQUEST => 'Meeting requests, booking confirmations, and cancellations',
            self::MEETING_REMINDER => 'Upcoming meeting alerts, location reminders, and time confirmations',
            self::SUBSCRIPTION => 'Premium subscription expiring, renewal reminders, and plan changes',
            self::EMAIL_VERIFICATION => 'Account verification required and verification status updates',
        };
    }

    /**
     * Get the icon for this category
     */
    public function getIcon(): string
    {
        return match($this) {
            self::GENERAL => 'bell',
            self::PAYMENT => 'credit-card',
            self::MATCH => 'heart',
            self::PARTNER_SWAPPING => 'users',
            self::BOOKING_REQUEST => 'calendar',
            self::MEETING_REMINDER => 'clock',
            self::SUBSCRIPTION => 'star',
            self::EMAIL_VERIFICATION => 'mail',
        };
    }

    /**
     * Get the color for this category
     */
    public function getColor(): string
    {
        return match($this) {
            self::GENERAL => 'blue',
            self::PAYMENT => 'green',
            self::MATCH => 'pink',
            self::PARTNER_SWAPPING => 'purple',
            self::BOOKING_REQUEST => 'orange',
            self::MEETING_REMINDER => 'yellow',
            self::SUBSCRIPTION => 'indigo',
            self::EMAIL_VERIFICATION => 'red',
        };
    }

    /**
     * Check if this category is enabled by default for new users
     */
    public function isEnabledByDefault(): bool
    {
        return match($this) {
            self::PAYMENT,
            self::EMAIL_VERIFICATION,
            self::BOOKING_REQUEST,
            self::MEETING_REMINDER => true,
            default => false,
        };
    }

    /**
     * Check if this category can be disabled by users
     */
    public function canBeDisabled(): bool
    {
        return match($this) {
            self::EMAIL_VERIFICATION => false, // Always required
            default => true,
        };
    }

    /**
     * Get all categories as an array
     */
    public static function all(): array
    {
        return [
            self::GENERAL,
            self::PAYMENT,
            self::MATCH,
            self::PARTNER_SWAPPING,
            self::BOOKING_REQUEST,
            self::MEETING_REMINDER,
            self::SUBSCRIPTION,
            self::EMAIL_VERIFICATION,
        ];
    }

    /**
     * Get categories that are enabled by default
     */
    public static function defaultEnabled(): array
    {
        return array_filter(self::all(), fn($category) => $category->isEnabledByDefault());
    }

    /**
     * Get categories that can be disabled
     */
    public static function userConfigurable(): array
    {
        return array_filter(self::all(), fn($category) => $category->canBeDisabled());
    }
}
