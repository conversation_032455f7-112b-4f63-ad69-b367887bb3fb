<?php

namespace App\Enums;

enum NotificationType: string
{
    // General notifications
    case GENERAL_ANNOUNCEMENT = 'general_announcement';
    case APP_UPDATE = 'app_update';
    case FEATURE_INTRODUCTION = 'feature_introduction';
    case SYSTEM_MAINTENANCE = 'system_maintenance';
    
    // Payment notifications
    case PAYMENT_CONFIRMATION = 'payment_confirmation';
    case PAYMENT_FAILED = 'payment_failed';
    case REFUND_PROCESSED = 'refund_processed';
    case WALLET_UPDATED = 'wallet_updated';
    case PAYMENT_REMINDER = 'payment_reminder';
    
    // Match notifications
    case NEW_MATCH = 'new_match';
    case MUTUAL_LIKE = 'mutual_like';
    case PROFILE_VIEW = 'profile_view';
    case MATCH_EXPIRED = 'match_expired';
    case LIKE_RECEIVED = 'like_received';
    
    // Partner swapping notifications
    case PARTNER_EXCHANGE_REQUEST = 'partner_exchange_request';
    case PARTNER_EXCHANGE_RESPONSE = 'partner_exchange_response';
    case PARTNER_EXCHANGE_CONFIRMED = 'partner_exchange_confirmed';
    case PARTNER_EXCHANGE_CANCELLED = 'partner_exchange_cancelled';
    
    // Booking request notifications
    case BOOKING_REQUEST_RECEIVED = 'booking_request_received';
    case BOOKING_CONFIRMED = 'booking_confirmed';
    case BOOKING_CANCELLED = 'booking_cancelled';
    case BOOKING_MODIFIED = 'booking_modified';
    case BOOKING_REMINDER = 'booking_reminder';
    
    // Meeting reminders
    case MEETING_UPCOMING = 'meeting_upcoming';
    case MEETING_LOCATION_REMINDER = 'meeting_location_reminder';
    case MEETING_TIME_CONFIRMATION = 'meeting_time_confirmation';
    case MEETING_CANCELLED = 'meeting_cancelled';
    case MEETING_RESCHEDULED = 'meeting_rescheduled';
    
    // Subscription expiry reminders
    case SUBSCRIPTION_EXPIRING = 'subscription_expiring';
    case SUBSCRIPTION_EXPIRED = 'subscription_expired';
    case SUBSCRIPTION_RENEWED = 'subscription_renewed';
    case PLAN_CHANGED = 'plan_changed';
    case RENEWAL_REMINDER = 'renewal_reminder';
    case SUBSCRIPTION_PROFILE_UPDATED = 'subscription_profile_updated';
    
    // Email verification notifications
    case EMAIL_VERIFICATION_REQUIRED = 'email_verification_required';
    case EMAIL_VERIFICATION_SUCCESS = 'email_verification_success';
    case EMAIL_VERIFICATION_FAILED = 'email_verification_failed';
    case EMAIL_CHANGED = 'email_changed';

    /**
     * Get the category for this notification type
     */
    public function getCategory(): NotificationCategory
    {
        return match($this) {
            self::GENERAL_ANNOUNCEMENT,
            self::APP_UPDATE,
            self::FEATURE_INTRODUCTION,
            self::SYSTEM_MAINTENANCE => NotificationCategory::GENERAL,
            
            self::PAYMENT_CONFIRMATION,
            self::PAYMENT_FAILED,
            self::REFUND_PROCESSED,
            self::WALLET_UPDATED,
            self::PAYMENT_REMINDER => NotificationCategory::PAYMENT,
            
            self::NEW_MATCH,
            self::MUTUAL_LIKE,
            self::PROFILE_VIEW,
            self::MATCH_EXPIRED,
            self::LIKE_RECEIVED => NotificationCategory::MATCH,
            
            self::PARTNER_EXCHANGE_REQUEST,
            self::PARTNER_EXCHANGE_RESPONSE,
            self::PARTNER_EXCHANGE_CONFIRMED,
            self::PARTNER_EXCHANGE_CANCELLED => NotificationCategory::PARTNER_SWAPPING,
            
            self::BOOKING_REQUEST_RECEIVED,
            self::BOOKING_CONFIRMED,
            self::BOOKING_CANCELLED,
            self::BOOKING_MODIFIED,
            self::BOOKING_REMINDER => NotificationCategory::BOOKING_REQUEST,
            
            self::MEETING_UPCOMING,
            self::MEETING_LOCATION_REMINDER,
            self::MEETING_TIME_CONFIRMATION,
            self::MEETING_CANCELLED,
            self::MEETING_RESCHEDULED => NotificationCategory::MEETING_REMINDER,
            
            self::SUBSCRIPTION_EXPIRING,
            self::SUBSCRIPTION_EXPIRED,
            self::SUBSCRIPTION_RENEWED,
            self::PLAN_CHANGED,
            self::RENEWAL_REMINDER,
            self::SUBSCRIPTION_PROFILE_UPDATED => NotificationCategory::SUBSCRIPTION,
            
            self::EMAIL_VERIFICATION_REQUIRED,
            self::EMAIL_VERIFICATION_SUCCESS,
            self::EMAIL_VERIFICATION_FAILED,
            self::EMAIL_CHANGED => NotificationCategory::EMAIL_VERIFICATION,
        };
    }

    /**
     * Get the display name for this notification type
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::GENERAL_ANNOUNCEMENT => 'General Announcement',
            self::APP_UPDATE => 'App Update',
            self::FEATURE_INTRODUCTION => 'New Feature',
            self::SYSTEM_MAINTENANCE => 'System Maintenance',
            
            self::PAYMENT_CONFIRMATION => 'Payment Confirmed',
            self::PAYMENT_FAILED => 'Payment Failed',
            self::REFUND_PROCESSED => 'Refund Processed',
            self::WALLET_UPDATED => 'Wallet Updated',
            self::PAYMENT_REMINDER => 'Payment Reminder',
            
            self::NEW_MATCH => 'New Match',
            self::MUTUAL_LIKE => 'Mutual Like',
            self::PROFILE_VIEW => 'Profile View',
            self::MATCH_EXPIRED => 'Match Expired',
            self::LIKE_RECEIVED => 'Like Received',
            
            self::PARTNER_EXCHANGE_REQUEST => 'Partner Exchange Request',
            self::PARTNER_EXCHANGE_RESPONSE => 'Partner Exchange Response',
            self::PARTNER_EXCHANGE_CONFIRMED => 'Partner Exchange Confirmed',
            self::PARTNER_EXCHANGE_CANCELLED => 'Partner Exchange Cancelled',
            
            self::BOOKING_REQUEST_RECEIVED => 'Booking Request',
            self::BOOKING_CONFIRMED => 'Booking Confirmed',
            self::BOOKING_CANCELLED => 'Booking Cancelled',
            self::BOOKING_MODIFIED => 'Booking Modified',
            self::BOOKING_REMINDER => 'Booking Reminder',
            
            self::MEETING_UPCOMING => 'Meeting Upcoming',
            self::MEETING_LOCATION_REMINDER => 'Location Reminder',
            self::MEETING_TIME_CONFIRMATION => 'Time Confirmation',
            self::MEETING_CANCELLED => 'Meeting Cancelled',
            self::MEETING_RESCHEDULED => 'Meeting Rescheduled',
            
            self::SUBSCRIPTION_EXPIRING => 'Subscription Expiring',
            self::SUBSCRIPTION_EXPIRED => 'Subscription Expired',
            self::SUBSCRIPTION_RENEWED => 'Subscription Renewed',
            self::PLAN_CHANGED => 'Plan Changed',
            self::RENEWAL_REMINDER => 'Renewal Reminder',
            self::SUBSCRIPTION_PROFILE_UPDATED => 'Profile Settings Updated',
            
            self::EMAIL_VERIFICATION_REQUIRED => 'Email Verification Required',
            self::EMAIL_VERIFICATION_SUCCESS => 'Email Verified',
            self::EMAIL_VERIFICATION_FAILED => 'Email Verification Failed',
            self::EMAIL_CHANGED => 'Email Changed',
        };
    }

    /**
     * Get the priority level for this notification type
     */
    public function getPriority(): NotificationPriority
    {
        return match($this) {
            self::PAYMENT_FAILED,
            self::EMAIL_VERIFICATION_REQUIRED,
            self::MEETING_CANCELLED,
            self::BOOKING_CANCELLED => NotificationPriority::HIGH,
            
            self::PAYMENT_CONFIRMATION,
            self::NEW_MATCH,
            self::MUTUAL_LIKE,
            self::PARTNER_EXCHANGE_REQUEST,
            self::BOOKING_REQUEST_RECEIVED,
            self::MEETING_UPCOMING,
            self::SUBSCRIPTION_EXPIRING,
            self::SUBSCRIPTION_PROFILE_UPDATED => NotificationPriority::MEDIUM,
            
            default => NotificationPriority::LOW,
        };
    }

    /**
     * Check if this notification type should be sent immediately
     */
    public function isImmediate(): bool
    {
        return match($this) {
            self::PAYMENT_CONFIRMATION,
            self::PAYMENT_FAILED,
            self::NEW_MATCH,
            self::MUTUAL_LIKE,
            self::PARTNER_EXCHANGE_REQUEST,
            self::BOOKING_REQUEST_RECEIVED,
            self::MEETING_UPCOMING,
            self::EMAIL_VERIFICATION_REQUIRED,
            self::SUBSCRIPTION_PROFILE_UPDATED => true,
            default => false,
        };
    }

    /**
     * Get the default sound for this notification type
     */
    public function getSound(): string
    {
        return match($this->getCategory()) {
            NotificationCategory::PAYMENT => 'payment.mp3',
            NotificationCategory::MATCH => 'match.mp3',
            NotificationCategory::PARTNER_SWAPPING => 'partner.mp3',
            NotificationCategory::BOOKING_REQUEST => 'booking.mp3',
            NotificationCategory::MEETING_REMINDER => 'meeting.mp3',
            NotificationCategory::SUBSCRIPTION => 'subscription.mp3',
            NotificationCategory::EMAIL_VERIFICATION => 'email.mp3',
            default => 'default.mp3',
        };
    }
}
