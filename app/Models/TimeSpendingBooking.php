<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class TimeSpendingBooking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'provider_id',
        'booking_date',
        'duration_hours',
        'actual_duration_hours',
        'hourly_rate',
        'platform_fee',
        'base_amount',
        'total_amount',
        'status',
        'payment_status',
        'escrow_status',
        'escrow_held_at',
        'escrow_released_at',
        'auto_release_at',
        'dispute_reason',
        'disputed_at',
        'disputed_by',
        'dispute_type',
        'dispute_status',
        'dispute_evidence',
        'admin_notes',
        'resolved_at',
        'resolved_by',
        'refund_status',
        'refund_amount',
        'refunded_at',
        'payment_method',
        'wallet_amount_used',
        'razorpay_amount_paid',
        'razorpay_payment_id',
        'razorpay_order_id',
        'razorpay_signature',
        'notes',
        'meeting_location',
        'client_location',
        'paid_at',
        'cancelled_at',
        'cancellation_reason',
        'provider_status',
        'rejection_reason',
        'provider_responded_at',
        'commission_percentage',
        'commission_amount',
        'provider_amount',
        'chat_enabled',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'booking_date' => 'datetime',
        'duration_hours' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'base_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'refunded_at' => 'datetime',
        'provider_responded_at' => 'datetime',
        'commission_percentage' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'provider_amount' => 'decimal:2',
        'chat_enabled' => 'boolean',
        'escrow_held_at' => 'datetime',
        'escrow_released_at' => 'datetime',
        'auto_release_at' => 'datetime',
        'disputed_at' => 'datetime',
        'resolved_at' => 'datetime',
        'dispute_evidence' => 'array',
    ];

    /**
     * Get the client (user who made the booking).
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * Get the provider (user being booked).
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the user who raised the dispute.
     */
    public function disputedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'disputed_by');
    }

    /**
     * Get the admin who resolved the dispute.
     */
    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Get the meeting verification for this booking.
     */
    public function meetingVerification(): HasOne
    {
        return $this->hasOne(MeetingVerification::class, 'booking_id');
    }

    /**
     * Get the admin revenue record for this booking.
     */
    public function adminRevenue(): HasOne
    {
        return $this->hasOne(AdminRevenue::class, 'booking_id');
    }

    /**
     * Get the chat messages for this booking.
     */
    public function chatMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'booking_id');
    }

    /**
     * Get the wallet transactions for this booking.
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'booking_id');
    }

    /**
     * Get the rating/reviews for this booking.
     */
    public function ratingReviews(): HasMany
    {
        return $this->hasMany(RatingReview::class, 'booking_id');
    }

    /**
     * Check if a time slot is available for a provider.
     * Only considers ACCEPTED bookings for availability (not pending ones).
     */
    public static function isTimeSlotAvailable($providerId, $bookingDate, $durationHours = 1, $excludeBookingId = null): bool
    {
        $startTime = Carbon::parse($bookingDate)->setTimezone('Asia/Kolkata');
        $endTime = $startTime->copy()->addHours((float) $durationHours);

        // Clean up expired pending bookings first
        self::cleanupExpiredPendingBookings();

        $query = self::where('provider_id', $providerId)
            ->where(function ($q) {
                // Only consider ACCEPTED bookings for availability
                // This allows multiple pending bookings for same slot
                $q->where('provider_status', 'accepted')
                  ->where('payment_status', 'paid');
            })
            ->where(function ($q) use ($startTime, $endTime) {
                // Check for actual overlapping time slots (not just touching)
                // Use actual_duration_hours for time slot calculation, fallback to duration_hours for backward compatibility
                $q->where(function ($subQ) use ($startTime, $endTime) {
                    // New booking starts before existing booking ends AND new booking ends after existing booking starts
                    $subQ->where('booking_date', '<', $endTime);

                    // Database-agnostic way to add duration to booking_date
                    if (\DB::getDriverName() === 'mysql') {
                        $subQ->whereRaw("DATE_ADD(booking_date, INTERVAL CAST(COALESCE(actual_duration_hours, duration_hours) * 60 AS SIGNED) MINUTE) > ?", [$startTime]);
                    } else {
                        // For SQLite and other databases, use PHP calculation
                        $subQ->where(function($phpQ) use ($startTime) {
                            $phpQ->whereRaw("datetime(booking_date, '+' || COALESCE(actual_duration_hours, duration_hours) * 60 || ' minutes') > ?", [$startTime]);
                        });
                    }
                });
            });

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() === 0;
    }

    /**
     * Check if a client already has a booking with the same provider for overlapping time slots.
     * This prevents duplicate bookings by the same client.
     */
    public static function hasClientDuplicateBooking($clientId, $providerId, $bookingDate, $durationHours = 1, $excludeBookingId = null): bool
    {
        $startTime = Carbon::parse($bookingDate)->setTimezone('Asia/Kolkata');
        $endTime = $startTime->copy()->addHours((float) $durationHours);

        // Clean up expired pending bookings first
        self::cleanupExpiredPendingBookings();

        $query = self::where('client_id', $clientId)
            ->where('provider_id', $providerId)
            ->where(function ($q) {
                // Consider pending, confirmed, and accepted bookings
                $q->whereIn('status', ['pending', 'confirmed'])
                  ->whereIn('provider_status', ['pending', 'accepted'])
                  ->where('payment_status', 'paid');
            })
            ->where(function ($q) use ($startTime, $endTime) {
                // Check for actual overlapping time slots (not just touching)
                // Use actual_duration_hours for time slot calculation, fallback to duration_hours for backward compatibility
                $q->where(function ($subQ) use ($startTime, $endTime) {
                    // New booking starts before existing booking ends AND new booking ends after existing booking starts
                    $subQ->where('booking_date', '<', $endTime);

                    // Database-agnostic way to add duration to booking_date
                    if (\DB::getDriverName() === 'mysql') {
                        $subQ->whereRaw("DATE_ADD(booking_date, INTERVAL CAST(COALESCE(actual_duration_hours, duration_hours) * 60 AS SIGNED) MINUTE) > ?", [$startTime]);
                    } else {
                        // For SQLite and other databases, use PHP calculation
                        $subQ->whereRaw("datetime(booking_date, '+' || COALESCE(actual_duration_hours, duration_hours) * 60 || ' minutes') > ?", [$startTime]);
                    }
                });
            });

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() > 0;
    }

    /**
     * Get booked time slots for a provider on a specific date.
     * Only considers ACCEPTED bookings for display (not pending ones).
     */
    public static function getBookedSlotsForDate($providerId, $date): array
    {
        $startOfDay = Carbon::parse($date)->setTimezone('Asia/Kolkata')->startOfDay();
        $endOfDay = Carbon::parse($date)->setTimezone('Asia/Kolkata')->endOfDay();

        // Clean up expired pending bookings first
        self::cleanupExpiredPendingBookings();

        $bookings = self::where('provider_id', $providerId)
            ->where(function ($q) {
                // Only consider ACCEPTED bookings for display
                // This allows multiple pending bookings for same slot
                $q->where('provider_status', 'accepted')
                  ->where('payment_status', 'paid');
            })
            ->whereBetween('booking_date', [$startOfDay, $endOfDay])
            ->orderBy('booking_date')
            ->get();

        $bookedSlots = [];
        foreach ($bookings as $booking) {
            $startTime = Carbon::parse($booking->booking_date);
            $endTime = $startTime->copy()->addHours((float) $booking->duration_hours);

            $bookedSlots[] = [
                'start' => $startTime->format('H:i'),
                'end' => $endTime->format('H:i'),
                'booking_id' => $booking->id,
                'status' => $booking->status,
                'payment_status' => $booking->payment_status
            ];
        }

        return $bookedSlots;
    }

    /**
     * Get available time slots for a provider on a specific date, considering booked slots.
     */
    public static function getAvailableTimeSlots($providerId, $date, $providerStartTime, $providerEndTime): array
    {
        $bookedSlots = self::getBookedSlotsForDate($providerId, $date);

        // Check if the date is today and filter out past times
        $currentDate = Carbon::now('Asia/Kolkata');
        $requestedDate = Carbon::parse($date, 'Asia/Kolkata');
        $isToday = $currentDate->isSameDay($requestedDate);

        // If it's today, adjust the start time to current time if it's later
        if ($isToday) {
            $currentTime = $currentDate->format('H:i');
            if ($currentTime > $providerStartTime) {
                $providerStartTime = $currentTime;
            }
        }

        if (empty($bookedSlots)) {
            // If no booked slots, check if there's still available time after current time
            if ($providerStartTime >= $providerEndTime) {
                return []; // No available time left
            }

            return [
                [
                    'start' => $providerStartTime,
                    'end' => $providerEndTime,
                    'display' => self::formatTimeRange($providerStartTime, $providerEndTime)
                ]
            ];
        }

        $availableSlots = [];
        $currentStart = $providerStartTime;

        // Sort booked slots by start time
        usort($bookedSlots, function($a, $b) {
            return strcmp($a['start'], $b['start']);
        });

        foreach ($bookedSlots as $bookedSlot) {
            // If there's a gap before this booking
            if ($currentStart < $bookedSlot['start']) {
                $gapEnd = self::subtractMinute($bookedSlot['start']);

                // Only add the slot if it's not in the past and has meaningful duration
                if ($gapEnd > $currentStart) {
                    // For today, also check if the gap end time is not in the past
                    if ($isToday) {
                        $currentTime = $currentDate->format('H:i');
                        // Only add if the gap end time is after current time
                        if ($gapEnd > $currentTime) {
                            $availableSlots[] = [
                                'start' => $currentStart,
                                'end' => $gapEnd,
                                'display' => self::formatTimeRange($currentStart, $gapEnd)
                            ];
                        }
                    } else {
                        // For future dates, add all gaps
                        $availableSlots[] = [
                            'start' => $currentStart,
                            'end' => $gapEnd,
                            'display' => self::formatTimeRange($currentStart, $gapEnd)
                        ];
                    }
                }
            }

            // Move current start to after this booking
            $currentStart = self::addMinute($bookedSlot['end']);
        }

        // Check if there's time available after the last booking
        if ($currentStart <= $providerEndTime) {
            $availableSlots[] = [
                'start' => $currentStart,
                'end' => $providerEndTime,
                'display' => self::formatTimeRange($currentStart, $providerEndTime)
            ];
        }

        return $availableSlots;
    }

    /**
     * Format time range for display.
     */
    private static function formatTimeRange($startTime, $endTime): string
    {
        $start = Carbon::createFromFormat('H:i', $startTime);
        $end = Carbon::createFromFormat('H:i', $endTime);

        return $start->format('g:i A') . ' to ' . $end->format('g:i A');
    }

    /**
     * Add one minute to a time string.
     */
    private static function addMinute($time): string
    {
        return Carbon::createFromFormat('H:i', $time)->addMinute()->format('H:i');
    }

    /**
     * Subtract one minute from a time string.
     */
    private static function subtractMinute($time): string
    {
        return Carbon::createFromFormat('H:i', $time)->subMinute()->format('H:i');
    }

    /**
     * Calculate total amount based on hourly rate, duration, and platform fee.
     */
    public function calculateTotalAmount(): float
    {
        $baseAmount = $this->hourly_rate * $this->duration_hours;
        $platformFee = $this->platform_fee ?? 0;
        return $baseAmount + $platformFee;
    }

    /**
     * Calculate base amount (without platform fee).
     */
    public function calculateBaseAmount(): float
    {
        return $this->hourly_rate * $this->duration_hours;
    }

    /**
     * Mark booking as paid.
     */
    public function markAsPaid($paymentDetails = []): void
    {
        $this->update([
            'payment_status' => 'paid',
            'status' => 'confirmed',
            'paid_at' => now(),
            'payment_method' => 'razorpay',
            'razorpay_payment_id' => $paymentDetails['razorpay_payment_id'] ?? null,
            'razorpay_order_id' => $paymentDetails['razorpay_order_id'] ?? null,
            'razorpay_signature' => $paymentDetails['razorpay_signature'] ?? null,
        ]);
    }

    /**
     * Mark booking as failed/cancelled due to payment failure.
     */
    public function markAsPaymentFailed($reason = 'Payment failed'): void
    {
        $this->update([
            'payment_status' => 'failed',
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);
    }

    /**
     * Check if booking payment has expired.
     */
    public function isPaymentExpired(): bool
    {
        if ($this->status !== 'pending' || $this->payment_status !== 'pending') {
            return false;
        }

        $timeoutMinutes = (int) config('booking.payment_timeout', 30);
        return $this->created_at->addMinutes($timeoutMinutes)->isPast();
    }

    /**
     * Clean up expired pending bookings.
     */
    public static function cleanupExpiredPendingBookings(): int
    {
        $timeoutMinutes = (int) config('booking.payment_timeout', 30);
        $expiredBookings = self::where('status', 'pending')
            ->where('payment_status', 'pending')
            ->where('created_at', '<=', now()->subMinutes($timeoutMinutes))
            ->get();

        $count = 0;
        foreach ($expiredBookings as $booking) {
            $booking->markAsPaymentFailed('Payment timeout - booking expired');
            $count++;
        }

        return $count;
    }

    /**
     * Get remaining payment time in minutes.
     */
    public function getRemainingPaymentTime(): int
    {
        if ($this->status !== 'pending' || $this->payment_status !== 'pending') {
            return 0;
        }

        $timeoutMinutes = (int) config('booking.payment_timeout', 30);
        $expiresAt = $this->created_at->addMinutes($timeoutMinutes);

        return max(0, (int) now()->diffInMinutes($expiresAt, false));
    }

    /**
     * Hold payment in escrow when booking is accepted.
     */
    public function holdInEscrow(): void
    {
        $this->update([
            'escrow_status' => 'held',
            'escrow_held_at' => now(),
        ]);
    }

    /**
     * Release payment from escrow to provider.
     */
    public function releaseFromEscrow(): void
    {
        if ($this->escrow_status !== 'held') {
            return;
        }

        // Add money to provider's wallet - use base amount only (excluding platform fee)
        $providerWallet = UserWallet::getOrCreate($this->provider_id);
        $commissionPercentage = $this->commission_percentage;

        // Provider gets base amount minus commission (platform fee goes to admin)
        $providerWallet->addEarnings(
            $this->base_amount,
            $commissionPercentage,
            "Earnings from booking with {$this->client->name}",
            $this->id
        );

        // Record admin revenue (platform fee + commission) only if not already created
        if (!$this->adminRevenue) {
            \App\Models\AdminRevenue::createFromBooking($this);
        }

        $this->update([
            'escrow_status' => 'released',
            'escrow_released_at' => now(),
        ]);

        // Send payment credited notification to provider
        \App\Models\Notification::createPaymentCredited(
            $this->provider_id,
            $this,
            $this->provider_amount,
            $providerWallet->fresh()->balance
        );
    }

    /**
     * Set auto-release timer for 24 hours after meeting completion.
     */
    public function setAutoReleaseTimer(): void
    {
        if ($this->escrow_status === 'held') {
            $this->update([
                'auto_release_at' => now()->addHours(24),
            ]);
        }
    }

    /**
     * Check if payment should be auto-released.
     */
    public function shouldAutoRelease(): bool
    {
        return $this->escrow_status === 'held'
            && $this->auto_release_at
            && now()->gte($this->auto_release_at);
    }

    /**
     * Raise a dispute for this booking.
     */
    public function raiseDispute(int $userId, string $reason, string $type = 'other', array $evidence = []): void
    {
        $this->update([
            'escrow_status' => 'disputed',
            'dispute_reason' => $reason,
            'disputed_at' => now(),
            'disputed_by' => $userId,
            'dispute_type' => $type,
            'dispute_status' => 'pending',
            'dispute_evidence' => $evidence,
        ]);
    }

    /**
     * Check if booking is eligible for no-show dispute.
     */
    public function canRaiseNoShowDispute(): bool
    {
        // Can raise no-show dispute if:
        // 1. Booking is accepted and paid
        // 2. Meeting time has passed (at least 30 minutes)
        // 3. No dispute already raised
        // 4. Meeting not completed (no verification photos)

        if ($this->provider_status !== 'accepted' || $this->payment_status !== 'paid') {
            return false;
        }

        if ($this->escrow_status === 'disputed') {
            return false;
        }

        $meetingEndTime = $this->booking_date->addHours((float) $this->duration_hours);
        if (!$meetingEndTime->addMinutes(30)->isPast()) {
            return false;
        }

        // Check if meeting was completed (has verification photos)
        $verification = \App\Models\MeetingVerification::where('booking_id', $this->id)->first();
        if ($verification && $verification->hasBothEndPhotos()) {
            return false;
        }

        return true;
    }

    /**
     * Check if booking is past meeting time.
     * No grace period - meeting ends exactly at scheduled end time.
     */
    public function isPastMeetingTime(): bool
    {
        $meetingEndTime = Carbon::parse($this->booking_date)->setTimezone('Asia/Kolkata')->addHours((float) $this->duration_hours);
        return now()->setTimezone('Asia/Kolkata')->gt($meetingEndTime);
    }

    /**
     * Get dispute status for display.
     */
    public function getDisputeStatusDisplayAttribute(): string
    {
        if (!$this->dispute_status) {
            return 'No Dispute';
        }

        return match($this->dispute_status) {
            'pending' => 'Dispute Pending',
            'investigating' => 'Under Investigation',
            'resolved' => 'Dispute Resolved',
            'rejected' => 'Dispute Rejected',
            default => ucfirst($this->dispute_status)
        };
    }

    /**
     * Get dispute type for display.
     */
    public function getDisputeTypeDisplayAttribute(): string
    {
        if (!$this->dispute_type) {
            return '';
        }

        return match($this->dispute_type) {
            'no_show' => 'Provider No-Show',
            'service_issue' => 'Service Issue',
            'payment_issue' => 'Payment Issue',
            'other' => 'Other Issue',
            default => ucfirst($this->dispute_type)
        };
    }

    /**
     * Check if meeting is completed (both end photos uploaded).
     */
    public function isMeetingCompleted(): bool
    {
        $verification = $this->meetingVerification;
        return $verification && $verification->hasBothEndPhotos();
    }

    /**
     * Check if meeting should be automatically ended (scheduled end time has passed).
     * No grace period - meeting should be auto-ended exactly at scheduled end time.
     */
    public function shouldBeAutoEnded(): bool
    {
        if ($this->provider_status !== 'accepted' || $this->payment_status !== 'paid') {
            return false;
        }

        $meetingEndTime = Carbon::parse($this->booking_date)->setTimezone('Asia/Kolkata')->addHours((float) $this->duration_hours);
        return now()->setTimezone('Asia/Kolkata')->gt($meetingEndTime);
    }

    /**
     * Get the scheduled meeting end time.
     */
    public function getScheduledEndTime(): Carbon
    {
        return Carbon::parse($this->booking_date)->addHours((float) $this->duration_hours);
    }

    /**
     * Check if meeting has all required photos (both start and end from both users).
     */
    public function hasAllRequiredPhotos(): bool
    {
        $verification = $this->meetingVerification;
        return $verification &&
               $verification->hasBothStartPhotos() &&
               $verification->hasBothEndPhotos();
    }

    /**
     * Check if meeting has any photos uploaded.
     */
    public function hasAnyPhotos(): bool
    {
        $verification = $this->meetingVerification;
        return $verification && (
            !empty($verification->client_start_photo) ||
            !empty($verification->provider_start_photo) ||
            !empty($verification->client_end_photo) ||
            !empty($verification->provider_end_photo)
        );
    }

    /**
     * Get photo verification status for post-meeting actions.
     */
    public function getPhotoVerificationStatus(): array
    {
        $verification = $this->meetingVerification;

        if (!$verification) {
            return [
                'has_all_photos' => false,
                'has_any_photos' => false,
                'missing_photos' => ['client_start', 'provider_start', 'client_end', 'provider_end'],
                'action_type' => 'no_show_or_end'
            ];
        }

        $hasAllPhotos = $verification->hasBothStartPhotos() && $verification->hasBothEndPhotos();
        $hasAnyPhotos = !empty($verification->client_start_photo) ||
                       !empty($verification->provider_start_photo) ||
                       !empty($verification->client_end_photo) ||
                       !empty($verification->provider_end_photo);

        $missingPhotos = [];
        if (empty($verification->client_start_photo)) $missingPhotos[] = 'client_start';
        if (empty($verification->provider_start_photo)) $missingPhotos[] = 'provider_start';
        if (empty($verification->client_end_photo)) $missingPhotos[] = 'client_end';
        if (empty($verification->provider_end_photo)) $missingPhotos[] = 'provider_end';

        return [
            'has_all_photos' => $hasAllPhotos,
            'has_any_photos' => $hasAnyPhotos,
            'missing_photos' => $missingPhotos,
            'action_type' => $hasAllPhotos ? 'rate_experience' : 'no_show_or_end'
        ];
    }

    /**
     * Process automatic meeting end when scheduled time passes.
     */
    public function processAutoEnd(): bool
    {
        if (!$this->shouldBeAutoEnded()) {
            return false;
        }

        try {
            $photoStatus = $this->getPhotoVerificationStatus();

            // Update status based on photo verification
            if ($photoStatus['has_all_photos']) {
                // Meeting completed successfully with all photos
                $this->update([
                    'status' => 'auto_completed',
                    'admin_notes' => 'Meeting automatically completed - all verification photos uploaded',
                ]);

                // Set normal auto-release timer for payment
                $this->setAutoReleaseTimer();

                // Create only 'Rate Your Experience' notifications
                $this->createAutoEndNotifications('rate_experience');

            } elseif ($photoStatus['has_any_photos']) {
                // Meeting partially verified - some photos missing
                $this->update([
                    'status' => 'auto_ended_partial',
                    'admin_notes' => 'Meeting automatically ended - partial verification (some photos missing)',
                ]);

                // Set shorter auto-release timer for incomplete meetings
                if ($this->escrow_status === 'held') {
                    $this->update([
                        'auto_release_at' => now()->addHours(12),
                    ]);
                }

                // Create only 'Rate Your Experience' notifications
                $this->createAutoEndNotifications('rate_experience');

            } else {
                // No photos uploaded - potential no-show
                $this->update([
                    'status' => 'auto_ended_no_photos',
                    'admin_notes' => 'Meeting automatically ended - no verification photos uploaded',
                ]);

                // Hold payment for manual review
                if ($this->escrow_status === 'held') {
                    $this->update([
                        'auto_release_at' => now()->addDays(7), // Longer hold for potential disputes
                    ]);
                }

                // Create only 'Rate Your Experience' notifications
                $this->createAutoEndNotifications('rate_experience');
            }

            return true;

        } catch (\Exception $e) {
            \Log::error('Auto-end processing failed for booking', [
                'booking_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Create notifications for auto-ended meetings.
     */
    private function createAutoEndNotifications(string $actionType): void
    {
        try {
            $notificationData = [
                'booking_id' => $this->id,
                'action_type' => $actionType,
                'meeting_date' => $this->booking_date->format('M d, Y'),
                'meeting_time' => $this->booking_date->format('h:i A'),
                'duration' => $this->duration_hours,
            ];

            // Check if users have already rated (for rate_experience type)
            $clientHasRated = false;
            $providerHasRated = false;

            if ($actionType === 'rate_experience') {
                $clientHasRated = \App\Models\RatingReview::where('booking_id', $this->id)
                    ->where('reviewer_id', $this->client_id)
                    ->exists();

                $providerHasRated = \App\Models\RatingReview::where('booking_id', $this->id)
                    ->where('reviewer_id', $this->provider_id)
                    ->exists();
            }

            // Create notification for client
            if ($actionType !== 'rate_experience' || !$clientHasRated) {
                $clientNotificationData = array_merge($notificationData, [
                    'other_user_name' => $this->provider->name,
                    'other_user_id' => $this->provider_id,
                ]);

                $clientNotification = $this->getNotificationContent($actionType, $this->provider->name);

                Notification::create([
                    'user_id' => $this->client_id,
                    'type' => $clientNotification['type'],
                    'title' => $clientNotification['title'],
                    'message' => $clientNotification['message'],
                    'body' => $clientNotification['message'],
                    'data' => $clientNotificationData,
                ]);
            }

            // Create notification for provider
            if ($actionType !== 'rate_experience' || !$providerHasRated) {
                $providerNotificationData = array_merge($notificationData, [
                    'other_user_name' => $this->client->name,
                    'other_user_id' => $this->client_id,
                ]);

                $providerNotification = $this->getNotificationContent($actionType, $this->client->name);

                Notification::create([
                    'user_id' => $this->provider_id,
                    'type' => $providerNotification['type'],
                    'title' => $providerNotification['title'],
                    'message' => $providerNotification['message'],
                    'body' => $providerNotification['message'],
                    'data' => $providerNotificationData,
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Failed to create auto-end notifications', [
                'booking_id' => $this->id,
                'action_type' => $actionType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get notification content based on action type.
     * Now only creates 'Rate Your Experience' notifications as per user preference.
     */
    private function getNotificationContent(string $actionType, string $otherUserName): array
    {
        // Always return 'Rate Your Experience' notification regardless of action type
        return [
            'type' => 'meeting_completed_rate',
            'title' => 'Rate Your Experience',
            'message' => "Your meeting with {$otherUserName} has ended. Please rate your experience.",
        ];
    }

    /**
     * Process refund for cancelled/rejected booking.
     */
    public function processRefund($reason = 'Booking cancelled'): bool
    {
        // Only process refund if booking was paid
        if ($this->payment_status !== 'paid') {
            return false;
        }

        // Prevent duplicate refunds - check if refund already processed
        if ($this->refund_status === 'processed') {
            \Log::warning('Attempted to process duplicate refund for booking', [
                'booking_id' => $this->id,
                'current_refund_status' => $this->refund_status,
                'refunded_at' => $this->refunded_at,
                'reason' => $reason
            ]);
            return false;
        }

        // Get client's wallet
        $clientWallet = UserWallet::getOrCreate($this->client_id);

        // Add refund to client's wallet
        $clientWallet->addRefund(
            $this->total_amount,
            "Refund for cancelled booking #{$this->id} - {$reason}",
            $this->id,
            [
                'refund_type' => 'booking_cancellation',
                'original_booking_id' => $this->id,
                'refund_reason' => $reason,
                'refund_amount' => $this->total_amount
            ]
        );

        // Update booking status to indicate refund processed
        $this->update([
            'refund_status' => 'processed',
            'refund_amount' => $this->total_amount,
            'refunded_at' => now(),
        ]);

        // Send notification to client about refund
        \App\Models\Notification::create([
            'user_id' => $this->client_id,
            'type' => 'refund_processed',
            'title' => 'Refund Processed',
            'message' => "₹{$this->total_amount} has been refunded to your wallet for cancelled booking on {$this->booking_date->format('M d, Y')}",
            'body' => "Your booking for {$this->booking_date->format('M d, Y')} at {$this->booking_date->format('h:i A')} was cancelled and ₹{$this->total_amount} has been refunded to your wallet. You can use this amount for future bookings.",
            'data' => [
                'booking_id' => $this->id,
                'refund_amount' => $this->total_amount,
                'wallet_balance' => $clientWallet->fresh()->balance,
            ],
        ]);

        return true;
    }

    /**
     * Check if booking should be auto-cancelled.
     * Criteria: paid booking where provider hasn't responded and start time has passed.
     */
    public function shouldBeAutoCancelled(): bool
    {
        return $this->provider_status === 'pending'
            && $this->payment_status === 'paid'
            && $this->status === 'confirmed'
            && now()->gte($this->booking_date);
    }

    /**
     * Process auto-cancellation for this booking.
     */
    public function processAutoCancellation(): bool
    {
        if (!$this->shouldBeAutoCancelled()) {
            return false;
        }

        try {
            // Update booking status
            $this->update([
                'status' => 'auto_cancelled',
                'provider_status' => 'rejected',
                'rejection_reason' => 'Booking auto-cancelled due to provider not responding before meeting time',
                'provider_responded_at' => now(),
                'cancelled_at' => now(),
                'cancellation_reason' => 'Auto-cancelled - Provider did not respond before meeting time',
            ]);

            // Process refund using the centralized method
            $refundProcessed = $this->processRefund('Provider did not respond before meeting time');

            if (!$refundProcessed) {
                \Log::warning('Auto-cancellation refund failed for booking', [
                    'booking_id' => $this->id,
                    'payment_status' => $this->payment_status,
                    'refund_status' => $this->refund_status
                ]);
            }

            return true;

        } catch (\Exception $e) {
            \Log::error('Auto-cancellation failed for booking', [
                'booking_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Auto-reject competing bookings when one booking is accepted.
     */
    public static function autoRejectCompetingBookings($acceptedBookingId): int
    {
        try {
            $acceptedBooking = self::find($acceptedBookingId);
            if (!$acceptedBooking) {
                return 0;
            }

            $startTime = Carbon::parse($acceptedBooking->booking_date);
            $endTime = $startTime->copy()->addHours((float) $acceptedBooking->duration_hours);

            // Find all competing pending bookings for the same provider and overlapping time
            $competingBookings = self::where('provider_id', $acceptedBooking->provider_id)
                ->where('id', '!=', $acceptedBookingId)
                ->where('provider_status', 'pending')
                ->where('payment_status', 'paid')
                ->where(function ($q) use ($startTime, $endTime) {
                    // Check for actual overlapping time slots (not just touching)
                    $q->where(function ($subQ) use ($startTime, $endTime) {
                        // New booking starts before existing booking ends AND new booking ends after existing booking starts
                        $subQ->where('booking_date', '<', $endTime);

                        // Database-agnostic way to add duration to booking_date
                        if (\DB::getDriverName() === 'mysql') {
                            $subQ->whereRaw("DATE_ADD(booking_date, INTERVAL CAST(duration_hours * 60 AS SIGNED) MINUTE) > ?", [$startTime]);
                        } else {
                            // For SQLite and other databases
                            $subQ->whereRaw("datetime(booking_date, '+' || duration_hours * 60 || ' minutes') > ?", [$startTime]);
                        }
                    });
                })
                ->get();

            $rejectedCount = 0;
            foreach ($competingBookings as $booking) {
                try {
                    $booking->update([
                        'provider_status' => 'rejected',
                        'rejection_reason' => 'Provider selected another client for this time slot',
                        'provider_responded_at' => now(),
                        'status' => 'cancelled',
                        'cancellation_reason' => 'Automatically declined - Provider chose another booking for the same time',
                    ]);

                    // Process refund for auto-rejected booking
                    $booking->processRefund('Provider selected another client for this time slot');

                    // Send notification to client about auto-rejection
                    \App\Models\Notification::create([
                        'user_id' => $booking->client_id,
                        'type' => 'booking_auto_rejected',
                        'title' => 'Booking Request Auto-Rejected',
                        'message' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically rejected. The provider accepted another client's request for the same time slot.",
                        'body' => "We're sorry to inform you that your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was declined. The provider received multiple booking requests for the same time slot and chose to accept another client's request. Please try booking a different available time slot. Your payment has been refunded to your wallet.",
                        'data' => [
                            'booking_id' => $booking->id,
                            'provider_id' => $booking->provider_id,
                            'auto_rejected' => true,
                            'reason' => 'time_slot_unavailable'
                        ],
                    ]);

                    $rejectedCount++;
                } catch (\Exception $e) {
                    \Log::error('Failed to auto-reject competing booking:', [
                        'booking_id' => $booking->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // Continue with other bookings even if one fails
                }
            }

            return $rejectedCount;
        } catch (\Exception $e) {
            \Log::error('Auto-reject competing bookings failed:', [
                'accepted_booking_id' => $acceptedBookingId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 0;
        }
    }

}
