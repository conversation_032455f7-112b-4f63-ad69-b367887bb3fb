<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SugarPartnerPartnership extends Model
{
    use HasFactory;

    protected $fillable = [
        'user1_id',
        'user2_id',
        'exchange_id',
        'status',
        'end_reason',
        'ended_by_user_id',
        'chat_enabled',
        'started_at',
        'ended_at',
    ];

    protected $casts = [
        'chat_enabled' => 'boolean',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
    ];

    /**
     * Get the first user in the partnership.
     */
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    /**
     * Get the second user in the partnership.
     */
    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    /**
     * Get the exchange that created this partnership.
     */
    public function exchange(): BelongsTo
    {
        return $this->belongsTo(SugarPartnerExchange::class, 'exchange_id');
    }

    /**
     * Get the user who ended the partnership.
     */
    public function endedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'ended_by_user_id');
    }

    /**
     * Get the other user in the partnership (given one user ID).
     */
    public function getOtherUser(int $userId): ?User
    {
        if ($this->user1_id === $userId) {
            return $this->user2;
        } elseif ($this->user2_id === $userId) {
            return $this->user1;
        }

        return null;
    }

    /**
     * Check if the partnership is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if chat is enabled for this partnership.
     */
    public function isChatEnabled(): bool
    {
        return $this->chat_enabled && $this->isActive();
    }

    /**
     * End the partnership.
     */
    public function endPartnership(int $endedByUserId, ?string $reason = null): void
    {
        $status = 'ended_by_user1';
        if ($endedByUserId === $this->user2_id) {
            $status = 'ended_by_user2';
        }

        $this->update([
            'status' => $status,
            'end_reason' => $reason,
            'ended_by_user_id' => $endedByUserId,
            'ended_at' => now(),
            'chat_enabled' => false,
        ]);
    }

    /**
     * Create a new partnership from a successful exchange.
     */
    public static function createFromExchange(SugarPartnerExchange $exchange): self
    {
        return self::create([
            'user1_id' => $exchange->user1_id,
            'user2_id' => $exchange->user2_id,
            'exchange_id' => $exchange->id,
            'status' => 'active',
            'chat_enabled' => true,
            'started_at' => now(),
        ]);
    }

    /**
     * Get partnership display name for a user.
     */
    public function getPartnerDisplayName(int $userId): string
    {
        $partner = $this->getOtherUser($userId);
        if (!$partner) {
            return 'Unknown Partner';
        }

        return $partner->name;
    }

    /**
     * Get partnership role label for a user.
     */
    public function getPartnerRoleLabel(int $userId): string
    {
        $partner = $this->getOtherUser($userId);
        if (!$partner) {
            return 'Sugar Partner';
        }

        return $partner->getSugarPartnerRoleLabel();
    }
}
