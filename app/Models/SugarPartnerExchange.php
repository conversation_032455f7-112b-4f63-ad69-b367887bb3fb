<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SugarPartnerExchange extends Model
{
    use HasFactory;

    protected $fillable = [
        'user1_id',
        'user2_id',
        'initiated_by_admin_id',
        'exchange_price',
        'custom_user1_amount',
        'custom_user2_amount',
        'currency',
        'status',
        'payment_completed_at',
        'profiles_viewed_at',
        'responses_completed_at',
    ];

    protected $casts = [
        'exchange_price' => 'decimal:2',
        'custom_user1_amount' => 'decimal:2',
        'custom_user2_amount' => 'decimal:2',
        'payment_completed_at' => 'datetime',
        'profiles_viewed_at' => 'datetime',
        'responses_completed_at' => 'datetime',
    ];

    /**
     * Get the first user in the exchange.
     */
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    /**
     * Get the second user in the exchange.
     */
    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    /**
     * Get the admin who initiated the exchange.
     */
    public function initiatedByAdmin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiated_by_admin_id');
    }

    /**
     * Get all payments for this exchange.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(SugarPartnerExchangePayment::class, 'exchange_id');
    }

    /**
     * Get all rejections for this exchange.
     */
    public function rejections(): HasMany
    {
        return $this->hasMany(SugarPartnerRejection::class, 'exchange_id');
    }

    /**
     * Check if both users have paid for the exchange.
     * @deprecated Use anyUserPaid() instead for single-user payment model
     */
    public function bothUsersPaid(): bool
    {
        $paidUsers = $this->payments()
            ->where('status', 'completed')
            ->pluck('user_id')
            ->unique();

        return $paidUsers->contains($this->user1_id) && $paidUsers->contains($this->user2_id);
    }

    /**
     * Check if any user has paid for the exchange (new single-payment model).
     */
    public function anyUserPaid(): bool
    {
        return $this->payments()
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Check if a specific user has paid for the exchange.
     */
    public function userHasPaid(int $userId): bool
    {
        return $this->payments()
            ->where('user_id', $userId)
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Get the other user in the exchange (given one user ID).
     */
    public function getOtherUser(int $userId): ?User
    {
        if ($this->user1_id === $userId) {
            return $this->user2;
        } elseif ($this->user2_id === $userId) {
            return $this->user1;
        }

        return null;
    }

    /**
     * Check if both users have responded to the exchange.
     */
    public function bothUsersResponded(): bool
    {
        $respondedUsers = $this->rejections()
            ->pluck('rejector_id')
            ->unique();

        return $respondedUsers->contains($this->user1_id) && $respondedUsers->contains($this->user2_id);
    }

    /**
     * Get user's response to the exchange.
     */
    public function getUserResponse(int $userId): ?SugarPartnerRejection
    {
        return $this->rejections()
            ->where('rejector_id', $userId)
            ->first();
    }

    /**
     * Check if exchange can be initiated between two users.
     */
    public static function canInitiateExchange(int $user1Id, int $user2Id): array
    {
        // Check if users have hard rejected each other
        // Use consistent ordering to match how hard rejects are stored
        $orderedUser1Id = min($user1Id, $user2Id);
        $orderedUser2Id = max($user1Id, $user2Id);

        $hardReject = SugarPartnerHardReject::where('user1_id', $orderedUser1Id)
            ->where('user2_id', $orderedUser2Id)
            ->first();

        if ($hardReject) {
            return [
                'can_initiate' => false,
                'reason' => 'hard_reject',
                'message' => 'Hard Reject Alert: These users have previously hard rejected each other. Profile exchange is not permitted.',
                'hard_reject' => $hardReject
            ];
        }

        // Check if there's already an active exchange
        // An exchange is considered "active" if:
        // 1. Both users haven't responded yet (status is not 'responses_completed'), OR
        // 2. Both users have accepted (both responses are 'accept')

        // Use consistent ordering to match how exchanges are stored
        $orderedUser1Id = min($user1Id, $user2Id);
        $orderedUser2Id = max($user1Id, $user2Id);

        $existingExchanges = self::where('user1_id', $orderedUser1Id)
            ->where('user2_id', $orderedUser2Id)
            ->whereNotIn('status', ['cancelled'])
            ->get();

        foreach ($existingExchanges as $exchange) {
            // If exchange hasn't reached responses_completed, it's still active
            if ($exchange->status !== 'responses_completed') {
                return [
                    'can_initiate' => false,
                    'reason' => 'active_exchange',
                    'message' => 'An active exchange already exists between these users.',
                    'exchange' => $exchange
                ];
            }

            // If exchange is responses_completed, check if both users accepted
            if ($exchange->status === 'responses_completed') {
                $user1Response = $exchange->getUserResponse($user1Id);
                $user2Response = $exchange->getUserResponse($user2Id);

                // If both users accepted, the exchange is still "active" (they're matched)
                if ($user1Response && $user1Response->rejection_type === 'accept' &&
                    $user2Response && $user2Response->rejection_type === 'accept') {
                    return [
                        'can_initiate' => false,
                        'reason' => 'active_exchange',
                        'message' => 'An active exchange already exists between these users.',
                        'exchange' => $exchange
                    ];
                }
                // If either user rejected, the exchange is completed and new exchanges can be initiated
            }
        }

        return [
            'can_initiate' => true,
            'reason' => null,
            'message' => 'Exchange can be initiated.'
        ];
    }

    /**
     * Get the price for a specific user based on their Sugar Partner type or custom amount.
     */
    public function getPriceForUser(int $userId): float
    {
        // Check for custom amounts first (payment override)
        if ($userId === $this->user1_id && $this->custom_user1_amount !== null) {
            return $this->custom_user1_amount;
        }

        if ($userId === $this->user2_id && $this->custom_user2_amount !== null) {
            return $this->custom_user2_amount;
        }

        // Fall back to default pricing based on user type
        $user = $userId === $this->user1_id ? $this->user1 : $this->user2;
        $userType = $user->getWhatIAm();

        $sugarPartnerFeature = \App\Models\Feature::where('name', 'sugar_partner')->first();
        $pricing = $sugarPartnerFeature?->options['pricing'] ?? [
            'sugar_daddy' => 200.00,
            'sugar_mommy' => 200.00,
            'sugar_companion_female' => 100.00,
            'sugar_companion_male' => 100.00
        ];

        // If user type is null or not found in pricing, use default companion pricing
        if (!$userType || !isset($pricing[$userType])) {
            return $pricing['sugar_companion_male'] ?? 100.00;
        }

        return $pricing[$userType];
    }

    /**
     * Mark exchange as payment completed.
     */
    public function markPaymentCompleted(): void
    {
        if ($this->bothUsersPaid()) {
            $this->update([
                'status' => 'payment_completed',
                'payment_completed_at' => now(),
            ]);
        }
    }

    /**
     * Mark profiles as viewed.
     */
    public function markProfilesViewed(): void
    {
        if ($this->status === 'payment_completed') {
            $this->update([
                'status' => 'profiles_viewed',
                'profiles_viewed_at' => now(),
            ]);
        }
    }

    /**
     * Mark responses as completed.
     */
    public function markResponsesCompleted(): void
    {
        if ($this->bothUsersResponded()) {
            $this->update([
                'status' => 'responses_completed',
                'responses_completed_at' => now(),
            ]);
        }
    }
}
