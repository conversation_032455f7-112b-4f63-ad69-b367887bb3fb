<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserReport extends Model
{
    use HasFactory;

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_DISMISSED = 'dismissed';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reporter_id',
        'reported_user_id',
        'category',
        'description',
        'evidence_file',
        'status',
        'admin_notes',
        'reviewed_by',
        'reviewed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reviewed_at' => 'datetime',
    ];

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_UNDER_REVIEW => 'Under Review',
            self::STATUS_RESOLVED => 'Resolved',
            self::STATUS_DISMISSED => 'Dismissed',
        ];
    }

    /**
     * Get available categories
     */
    public static function getCategories(): array
    {
        return [
            'inappropriate_behavior' => 'Inappropriate Behavior',
            'fake_profile' => 'Fake Profile',
            'harassment' => 'Harassment',
            'spam' => 'Spam',
            'inappropriate_content' => 'Inappropriate Content',
            'scam_fraud' => 'Scam/Fraud',
            'underage_user' => 'Underage User',
            'violence_threats' => 'Violence/Threats',
            'hate_speech' => 'Hate Speech',
            'other' => 'Other',
        ];
    }

    /**
     * Get the user who reported
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reporter_id');
    }

    /**
     * Get the reported user
     */
    public function reportedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reported_user_id');
    }

    /**
     * Get the admin who reviewed the report
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the decision history for this report
     */
    public function decisionHistory(): HasMany
    {
        return $this->hasMany(ReportDecisionHistory::class, 'report_id')->orderBy('created_at', 'desc');
    }

    /**
     * Check if report is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if report is under review
     */
    public function isUnderReview(): bool
    {
        return $this->status === self::STATUS_UNDER_REVIEW;
    }

    /**
     * Check if report is resolved
     */
    public function isResolved(): bool
    {
        return $this->status === self::STATUS_RESOLVED;
    }

    /**
     * Check if report is dismissed
     */
    public function isDismissed(): bool
    {
        return $this->status === self::STATUS_DISMISSED;
    }

    /**
     * Mark report as under review
     */
    public function markAsUnderReview(User $reviewer): void
    {
        $this->update([
            'status' => self::STATUS_UNDER_REVIEW,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);
    }

    /**
     * Mark report as resolved
     */
    public function markAsResolved(User $reviewer, ?string $adminNotes = null): void
    {
        $this->update([
            'status' => self::STATUS_RESOLVED,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'admin_notes' => $adminNotes ?? $this->admin_notes,
        ]);
    }

    /**
     * Mark report as dismissed
     */
    public function markAsDismissed(User $reviewer, ?string $adminNotes = null): void
    {
        $this->update([
            'status' => self::STATUS_DISMISSED,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'admin_notes' => $adminNotes ?? $this->admin_notes,
        ]);
    }

    /**
     * Get category display name
     */
    public function getCategoryDisplayName(): string
    {
        return self::getCategories()[$this->category] ?? $this->category;
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayName(): string
    {
        return self::getStatuses()[$this->status] ?? $this->status;
    }

    /**
     * Get status badge class for Bootstrap
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'bg-warning',
            self::STATUS_UNDER_REVIEW => 'bg-info',
            self::STATUS_RESOLVED => 'bg-success',
            self::STATUS_DISMISSED => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Scope for pending reports
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for resolved reports
     */
    public function scopeResolved($query)
    {
        return $query->where('status', self::STATUS_RESOLVED);
    }

    /**
     * Scope for dismissed reports
     */
    public function scopeDismissed($query)
    {
        return $query->where('status', self::STATUS_DISMISSED);
    }

    /**
     * Scope for reports by category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for reports in date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
