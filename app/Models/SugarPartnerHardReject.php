<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SugarPartnerHardReject extends Model
{
    use HasFactory;

    protected $fillable = [
        'user1_id',
        'user2_id',
        'rejector_id',
        'original_exchange_id',
        'rejection_reason',
        'admin_note',
    ];

    /**
     * Get the first user in the hard reject.
     */
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    /**
     * Get the second user in the hard reject.
     */
    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    /**
     * Get the user who initiated the hard reject.
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejector_id');
    }

    /**
     * Get the original exchange that led to this hard reject.
     */
    public function originalExchange(): BelongsTo
    {
        return $this->belongsTo(SugarPartnerExchange::class, 'original_exchange_id');
    }

    /**
     * Check if two users have hard rejected each other.
     */
    public static function existsBetweenUsers(int $user1Id, int $user2Id): bool
    {
        $minId = min($user1Id, $user2Id);
        $maxId = max($user1Id, $user2Id);

        return self::where('user1_id', $minId)
            ->where('user2_id', $maxId)
            ->exists();
    }

    /**
     * Get hard reject record between two users.
     */
    public static function getBetweenUsers(int $user1Id, int $user2Id): ?self
    {
        $minId = min($user1Id, $user2Id);
        $maxId = max($user1Id, $user2Id);

        return self::where('user1_id', $minId)
            ->where('user2_id', $maxId)
            ->with(['rejector', 'originalExchange'])
            ->first();
    }

    /**
     * Get all users that a specific user has hard rejected or been hard rejected by.
     */
    public static function getBlockedUsersFor(int $userId): array
    {
        $hardRejects = self::where('user1_id', $userId)
            ->orWhere('user2_id', $userId)
            ->with(['user1', 'user2', 'rejector'])
            ->get();

        $blockedUsers = [];
        foreach ($hardRejects as $hardReject) {
            $otherUserId = $hardReject->user1_id === $userId ? $hardReject->user2_id : $hardReject->user1_id;
            $otherUser = $hardReject->user1_id === $userId ? $hardReject->user2 : $hardReject->user1;

            $blockedUsers[] = [
                'user' => $otherUser,
                'rejector_id' => $hardReject->rejector_id,
                'is_rejector' => $hardReject->rejector_id === $userId,
                'rejection_reason' => $hardReject->rejection_reason,
                'created_at' => $hardReject->created_at,
            ];
        }

        return $blockedUsers;
    }

    /**
     * Get only users that a specific user has hard rejected (not users who rejected them).
     */
    public static function getUsersRejectedBy(int $userId): array
    {
        $hardRejects = self::where('rejector_id', $userId)
            ->with(['user1', 'user2', 'rejector'])
            ->get();

        $rejectedUsers = [];
        foreach ($hardRejects as $hardReject) {
            // Get the user who was rejected (not the rejector)
            $rejectedUserId = $hardReject->user1_id === $userId ? $hardReject->user2_id : $hardReject->user1_id;
            $rejectedUser = $hardReject->user1_id === $userId ? $hardReject->user2 : $hardReject->user1;

            $rejectedUsers[] = [
                'user' => $rejectedUser,
                'rejector_id' => $hardReject->rejector_id,
                'rejection_reason' => $hardReject->rejection_reason,
                'created_at' => $hardReject->created_at,
                'hard_reject_id' => $hardReject->id,
            ];
        }

        return $rejectedUsers;
    }

    /**
     * Remove hard reject between two users (admin override).
     */
    public static function removeBlockBetweenUsers(int $user1Id, int $user2Id): bool
    {
        $minId = min($user1Id, $user2Id);
        $maxId = max($user1Id, $user2Id);

        return self::where('user1_id', $minId)
            ->where('user2_id', $maxId)
            ->delete() > 0;
    }

    /**
     * Get formatted rejection details for admin display.
     */
    public function getFormattedDetails(): array
    {
        return [
            'rejector_name' => $this->rejector->name,
            'rejected_user_name' => $this->rejector_id === $this->user1_id ? $this->user2->name : $this->user1->name,
            'rejection_date' => $this->created_at->format('M d, Y'),
            'rejection_reason' => $this->rejection_reason ?: 'No reason provided',
            'admin_note' => $this->admin_note ?: 'No admin note',
            'original_exchange_date' => $this->originalExchange?->created_at?->format('M d, Y') ?: 'Unknown',
        ];
    }
}
