<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserChangeLog extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'field_name',
        'old_value',
        'new_value',
        'action',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the change log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted field name for display.
     */
    public function getFormattedFieldNameAttribute(): string
    {
        return match($this->field_name) {
            'name' => 'Full Name',
            'email' => 'Email Address',
            'contact_number' => 'Contact Number',
            'gender' => 'Gender',
            'date_of_birth' => 'Date of Birth',
            'interests' => 'Interests & Hobbies',
            'expectation' => 'Expectations from Partner',
            'profile_picture' => 'Profile Picture',
            'is_public_profile' => 'Public Profile Visibility',
            'show_gallery_images' => 'Gallery Images Display',
            default => ucfirst(str_replace('_', ' ', $this->field_name))
        };
    }

    /**
     * Get formatted old value for display.
     */
    public function getFormattedOldValueAttribute(): string
    {
        if (empty($this->old_value)) {
            return 'Not set';
        }

        if ($this->field_name === 'profile_picture') {
            return 'Previous image';
        }

        if ($this->field_name === 'date_of_birth') {
            return \Carbon\Carbon::parse($this->old_value)->format('M d, Y');
        }

        return $this->old_value;
    }

    /**
     * Get formatted new value for display.
     */
    public function getFormattedNewValueAttribute(): string
    {
        if (empty($this->new_value)) {
            return 'Removed';
        }

        if ($this->field_name === 'profile_picture') {
            return 'New image uploaded';
        }

        if ($this->field_name === 'date_of_birth') {
            return \Carbon\Carbon::parse($this->new_value)->format('M d, Y');
        }

        return $this->new_value;
    }
}
