<?php

namespace App\Models;

use App\Enums\NotificationCategory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserNotificationPreference extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',

        // Push notification preferences
        'push_notifications_enabled',
        'push_general',
        'push_payment',
        'push_match',
        'push_partner_swapping',
        'push_booking',
        'push_meeting_reminders',
        'push_subscription',

        // Email notification preferences
        'email_notifications_enabled',
        'email_general',
        'email_payment',
        'email_match',
        'email_partner_swapping',
        'email_booking',
        'email_meeting_reminders',
        'email_subscription',

        // In-app notification preferences
        'inapp_notifications_enabled',
        'inapp_general',
        'inapp_payment',
        'inapp_match',
        'inapp_partner_swapping',
        'inapp_booking',
        'inapp_meeting_reminders',
        'inapp_subscription',

        // Quiet hours
        'quiet_hours_enabled',
        'quiet_hours_start',
        'quiet_hours_end',
        'timezone',

        // Browser permission and FCM token
        'browser_permission_status',
        'fcm_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // Push notification preferences
        'push_notifications_enabled' => 'boolean',
        'push_general' => 'boolean',
        'push_payment' => 'boolean',
        'push_match' => 'boolean',
        'push_partner_swapping' => 'boolean',
        'push_booking' => 'boolean',
        'push_meeting_reminders' => 'boolean',
        'push_subscription' => 'boolean',
        
        // Email notification preferences
        'email_notifications_enabled' => 'boolean',
        'email_general' => 'boolean',
        'email_payment' => 'boolean',
        'email_match' => 'boolean',
        'email_partner_swapping' => 'boolean',
        'email_booking' => 'boolean',
        'email_meeting_reminders' => 'boolean',
        'email_subscription' => 'boolean',
        
        // In-app notification preferences
        'inapp_notifications_enabled' => 'boolean',
        'inapp_general' => 'boolean',
        'inapp_payment' => 'boolean',
        'inapp_match' => 'boolean',
        'inapp_partner_swapping' => 'boolean',
        'inapp_booking' => 'boolean',
        'inapp_meeting_reminders' => 'boolean',
        'inapp_subscription' => 'boolean',
        
        // Quiet hours
        'quiet_hours_enabled' => 'boolean',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
    ];

    /**
     * Get the user that owns the notification preferences.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get default notification preferences - ALL DISABLED BY DEFAULT
     */
    public static function getDefaults(): array
    {
        return [
            // Push notification preferences - ALL DISABLED BY DEFAULT
            'push_notifications_enabled' => false,
            'push_general' => false,
            'push_payment' => false,
            'push_match' => false,
            'push_partner_swapping' => false,
            'push_booking' => false,
            'push_meeting_reminders' => false,
            'push_subscription' => false,

            // Email notification preferences - ALL DISABLED BY DEFAULT
            'email_notifications_enabled' => false,
            'email_general' => false,
            'email_payment' => false,
            'email_match' => false,
            'email_partner_swapping' => false,
            'email_booking' => false,
            'email_meeting_reminders' => false,
            'email_subscription' => false,

            // In-app notification preferences - ALL DISABLED BY DEFAULT
            'inapp_notifications_enabled' => false,
            'inapp_general' => false,
            'inapp_payment' => false,
            'inapp_match' => false,
            'inapp_partner_swapping' => false,
            'inapp_booking' => false,
            'inapp_meeting_reminders' => false,
            'inapp_subscription' => false,

            // Quiet hours
            'quiet_hours_enabled' => false,
            'quiet_hours_start' => null,
            'quiet_hours_end' => null,
            'timezone' => 'UTC',
        ];
    }

    /**
     * Check if user should receive a specific type of notification
     */
    public function shouldReceive(string $type, string $channel = 'push'): bool
    {
        // Check if the channel is enabled
        $channelEnabled = $this->{$channel . '_notifications_enabled'} ?? true;
        if (!$channelEnabled) {
            return false;
        }

        // Check if the specific type is enabled
        $typeEnabled = $this->{$channel . '_' . $type} ?? true;
        if (!$typeEnabled) {
            return false;
        }

        // Check quiet hours for push notifications
        if ($channel === 'push' && $this->quiet_hours_enabled) {
            return !$this->isInQuietHours();
        }

        return true;
    }

    /**
     * Check if current time is within quiet hours
     */
    public function isInQuietHours(): bool
    {
        if (!$this->quiet_hours_enabled || !$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $now = now($this->timezone);
        $start = $now->copy()->setTimeFromTimeString($this->quiet_hours_start);
        $end = $now->copy()->setTimeFromTimeString($this->quiet_hours_end);

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($start->gt($end)) {
            return $now->gte($start) || $now->lte($end);
        }

        return $now->between($start, $end);
    }

    /**
     * Get notification types
     */
    public static function getNotificationTypes(): array
    {
        return [
            'general' => 'General Notifications',
            'payment' => 'Payment & Transactions',
            'match' => 'Matches & Connections',
            'partner_swapping' => 'Activity Partner Exchange',
            'booking' => 'Bookings & Appointments',
            'meeting_reminders' => 'Meeting Reminders',
            'subscription' => 'Subscription & Account',
        ];
    }

    /**
     * Get notification channels
     */
    public static function getNotificationChannels(): array
    {
        return [
            'push' => 'Push Notifications',
            'email' => 'Email Notifications',
            'inapp' => 'In-App Notifications',
        ];
    }

    /**
     * Check if browser permission is granted
     */
    public function hasBrowserPermission(): bool
    {
        return $this->browser_permission_status === 'granted';
    }

    /**
     * Update browser permission status
     */
    public function updateBrowserPermission(string $status): void
    {
        $this->update(['browser_permission_status' => $status]);
    }

    /**
     * Update FCM token
     */
    public function updateFcmToken(?string $token): void
    {
        $this->update(['fcm_token' => $token]);
    }

    /**
     * Check if any push notifications are enabled
     */
    public function hasAnyPushNotificationsEnabled(): bool
    {
        return $this->push_notifications_enabled && (
            $this->push_general ||
            $this->push_payment ||
            $this->push_match ||
            $this->push_partner_swapping ||
            $this->push_booking ||
            $this->push_meeting_reminders ||
            $this->push_subscription
        );
    }

    /**
     * Enable all notification types for a specific channel
     */
    public function enableAllForChannel(string $channel): void
    {
        $updates = [
            $channel . '_notifications_enabled' => true,
            $channel . '_general' => true,
            $channel . '_payment' => true,
            $channel . '_match' => true,
            $channel . '_partner_swapping' => true,
            $channel . '_booking' => true,
            $channel . '_meeting_reminders' => true,
            $channel . '_subscription' => true,
        ];

        $this->update($updates);
    }

    /**
     * Disable all notification types for a specific channel
     */
    public function disableAllForChannel(string $channel): void
    {
        $updates = [
            $channel . '_notifications_enabled' => false,
            $channel . '_general' => false,
            $channel . '_payment' => false,
            $channel . '_match' => false,
            $channel . '_partner_swapping' => false,
            $channel . '_booking' => false,
            $channel . '_meeting_reminders' => false,
            $channel . '_subscription' => false,
        ];

        $this->update($updates);
    }
}
