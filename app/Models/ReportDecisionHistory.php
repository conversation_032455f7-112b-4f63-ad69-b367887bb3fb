<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReportDecisionHistory extends Model
{
    use HasFactory;

    // Action type constants
    const ACTION_INITIAL_DECISION = 'initial_decision';
    const ACTION_STATUS_CHANGE = 'status_change';
    const ACTION_SUSPENSION_APPLIED = 'suspension_applied';
    const ACTION_SUSPENSION_LIFTED = 'suspension_lifted';
    const ACTION_WARNING_SENT = 'warning_sent';
    const ACTION_DECISION_REVERSED = 'decision_reversed';
    const ACTION_ADMIN_NOTES_UPDATED = 'admin_notes_updated';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'report_id',
        'admin_id',
        'action_type',
        'previous_status',
        'new_status',
        'reason',
        'admin_notes',
        'suspension_applied',
        'suspension_lifted',
        'suspension_duration',
        'suspension_reason',
        'warning_sent',
        'warning_message',
        'metadata',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'suspension_applied' => 'boolean',
        'suspension_lifted' => 'boolean',
        'warning_sent' => 'boolean',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the report that this decision history belongs to.
     */
    public function report(): BelongsTo
    {
        return $this->belongsTo(UserReport::class, 'report_id');
    }

    /**
     * Get the admin who made this decision.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get all available action types.
     */
    public static function getActionTypes(): array
    {
        return [
            self::ACTION_INITIAL_DECISION => 'Initial Decision',
            self::ACTION_STATUS_CHANGE => 'Status Change',
            self::ACTION_SUSPENSION_APPLIED => 'Suspension Applied',
            self::ACTION_SUSPENSION_LIFTED => 'Suspension Lifted',
            self::ACTION_WARNING_SENT => 'Warning Sent',
            self::ACTION_DECISION_REVERSED => 'Decision Reversed',
            self::ACTION_ADMIN_NOTES_UPDATED => 'Admin Notes Updated',
        ];
    }

    /**
     * Get the display name for the action type.
     */
    public function getActionTypeDisplayName(): string
    {
        return self::getActionTypes()[$this->action_type] ?? $this->action_type;
    }

    /**
     * Create a decision history entry for initial decision.
     */
    public static function logInitialDecision(
        UserReport $report,
        User $admin,
        string $action,
        ?string $reason = null,
        ?array $metadata = null
    ): self {
        return self::create([
            'report_id' => $report->id,
            'admin_id' => $admin->id,
            'action_type' => self::ACTION_INITIAL_DECISION,
            'new_status' => $report->status,
            'reason' => $reason,
            'metadata' => $metadata,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Create a decision history entry for status change.
     */
    public static function logStatusChange(
        UserReport $report,
        User $admin,
        string $previousStatus,
        string $newStatus,
        ?string $reason = null
    ): self {
        return self::create([
            'report_id' => $report->id,
            'admin_id' => $admin->id,
            'action_type' => self::ACTION_STATUS_CHANGE,
            'previous_status' => $previousStatus,
            'new_status' => $newStatus,
            'reason' => $reason,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Create a decision history entry for suspension.
     */
    public static function logSuspension(
        UserReport $report,
        User $admin,
        string $duration,
        string $reason,
        bool $isLifted = false
    ): self {
        return self::create([
            'report_id' => $report->id,
            'admin_id' => $admin->id,
            'action_type' => $isLifted ? self::ACTION_SUSPENSION_LIFTED : self::ACTION_SUSPENSION_APPLIED,
            'suspension_applied' => !$isLifted,
            'suspension_lifted' => $isLifted,
            'suspension_duration' => $duration,
            'suspension_reason' => $reason,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Create a decision history entry for warning.
     */
    public static function logWarning(
        UserReport $report,
        User $admin,
        string $warningMessage
    ): self {
        return self::create([
            'report_id' => $report->id,
            'admin_id' => $admin->id,
            'action_type' => self::ACTION_WARNING_SENT,
            'warning_sent' => true,
            'warning_message' => $warningMessage,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Create a decision history entry for decision reversal.
     */
    public static function logDecisionReversal(
        UserReport $report,
        User $admin,
        string $reason,
        ?array $metadata = null
    ): self {
        return self::create([
            'report_id' => $report->id,
            'admin_id' => $admin->id,
            'action_type' => self::ACTION_DECISION_REVERSED,
            'reason' => $reason,
            'metadata' => $metadata,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
