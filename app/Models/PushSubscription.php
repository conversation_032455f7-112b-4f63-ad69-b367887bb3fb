<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PushSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'endpoint',
        'public_key',
        'auth_token',
        'content_encoding',
        'user_agent',
        'device_type',
        'browser',
        'is_active',
        'last_used_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create or update a push subscription for a user
     */
    public static function createOrUpdateForUser(User $user, array $subscriptionData): self
    {
        return self::updateOrCreate(
            [
                'user_id' => $user->id,
                'endpoint' => $subscriptionData['endpoint'],
            ],
            [
                'public_key' => $subscriptionData['keys']['p256dh'] ?? null,
                'auth_token' => $subscriptionData['keys']['auth'] ?? null,
                'content_encoding' => $subscriptionData['contentEncoding'] ?? 'aesgcm',
                'user_agent' => request()->userAgent(),
                'device_type' => self::detectDeviceType(),
                'browser' => self::detectBrowser(),
                'is_active' => true,
                'last_used_at' => now(),
            ]
        );
    }

    /**
     * Get active subscriptions for a user
     */
    public static function getActiveForUser(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $user->id)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Mark subscription as inactive
     */
    public function markInactive(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Update last used timestamp
     */
    public function updateLastUsed(): void
    {
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Get subscription data for push service
     */
    public function getSubscriptionData(): array
    {
        return [
            'endpoint' => $this->endpoint,
            'keys' => [
                'p256dh' => $this->public_key,
                'auth' => $this->auth_token,
            ],
            'contentEncoding' => $this->content_encoding,
        ];
    }

    /**
     * Detect device type from user agent
     */
    private static function detectDeviceType(): string
    {
        $userAgent = request()->userAgent();
        
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        
        return 'desktop';
    }

    /**
     * Detect browser from user agent
     */
    private static function detectBrowser(): string
    {
        $userAgent = request()->userAgent();
        
        if (preg_match('/Chrome/', $userAgent)) {
            return 'chrome';
        } elseif (preg_match('/Firefox/', $userAgent)) {
            return 'firefox';
        } elseif (preg_match('/Safari/', $userAgent)) {
            return 'safari';
        } elseif (preg_match('/Edge/', $userAgent)) {
            return 'edge';
        }
        
        return 'unknown';
    }

    /**
     * Clean up old inactive subscriptions
     */
    public static function cleanupOldSubscriptions(): int
    {
        return self::where('is_active', false)
            ->where('updated_at', '<', now()->subDays(30))
            ->delete();
    }

    /**
     * Mark subscriptions as inactive if they haven't been used recently
     */
    public static function markStaleSubscriptionsInactive(): int
    {
        return self::where('is_active', true)
            ->where('last_used_at', '<', now()->subDays(7))
            ->update(['is_active' => false]);
    }
}
