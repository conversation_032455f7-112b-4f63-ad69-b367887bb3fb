<?php

namespace App\Models;

use App\Enums\NotificationCategory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationPreference extends Model
{
    use HasFactory;

    protected $table = 'user_notification_preferences';

    protected $fillable = [
        'user_id',
        'category',
        'push_enabled',
        'email_enabled',
        'in_app_enabled',
        'sound_enabled',
        'custom_sound',
        'quiet_hours',
        'days_enabled',
    ];

    protected $casts = [
        'category' => NotificationCategory::class,
        'push_enabled' => 'boolean',
        'email_enabled' => 'boolean',
        'in_app_enabled' => 'boolean',
        'sound_enabled' => 'boolean',
        'quiet_hours' => 'array',
        'days_enabled' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if notifications are allowed at the current time
     */
    public function isAllowedNow(): bool
    {
        // Check if today is enabled
        if ($this->days_enabled && !in_array(now()->dayOfWeek, $this->days_enabled)) {
            return false;
        }

        // Check quiet hours
        if ($this->quiet_hours) {
            $now = now()->format('H:i');
            $start = $this->quiet_hours['start'] ?? null;
            $end = $this->quiet_hours['end'] ?? null;

            if ($start && $end) {
                if ($start <= $end) {
                    // Same day quiet hours (e.g., 22:00 to 23:59)
                    if ($now >= $start && $now <= $end) {
                        return false;
                    }
                } else {
                    // Overnight quiet hours (e.g., 22:00 to 08:00)
                    if ($now >= $start || $now <= $end) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Get the sound to use for this preference
     */
    public function getSound(): ?string
    {
        if (!$this->sound_enabled) {
            return null;
        }

        return $this->custom_sound ?: $this->category->getSound();
    }

    /**
     * Create default preferences for a user
     */
    public static function createDefaultsForUser(User $user): void
    {
        foreach (NotificationCategory::all() as $category) {
            self::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'category' => $category,
                ],
                [
                    'push_enabled' => $category->isEnabledByDefault(),
                    'email_enabled' => false,
                    'in_app_enabled' => true,
                    'sound_enabled' => true,
                ]
            );
        }
    }

    /**
     * Get user's preference for a specific category
     */
    public static function getForUserAndCategory(User $user, NotificationCategory $category): ?self
    {
        return self::where('user_id', $user->id)
            ->where('category', $category)
            ->first();
    }

    /**
     * Check if user has push notifications enabled for a category
     */
    public static function isPushEnabledForUser(User $user, NotificationCategory $category): bool
    {
        $preference = self::getForUserAndCategory($user, $category);
        
        if (!$preference) {
            return $category->isEnabledByDefault();
        }

        return $preference->push_enabled && $preference->isAllowedNow();
    }

    /**
     * Check if user has in-app notifications enabled for a category
     */
    public static function isInAppEnabledForUser(User $user, NotificationCategory $category): bool
    {
        $preference = self::getForUserAndCategory($user, $category);
        
        if (!$preference) {
            return true; // In-app notifications are always enabled by default
        }

        return $preference->in_app_enabled;
    }

    /**
     * Get all preferences for a user
     */
    public static function getAllForUser(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $user->id)->get();
    }

    /**
     * Update or create preference for user and category
     */
    public static function updateForUserAndCategory(
        User $user, 
        NotificationCategory $category, 
        array $preferences
    ): self {
        return self::updateOrCreate(
            [
                'user_id' => $user->id,
                'category' => $category,
            ],
            $preferences
        );
    }
}
