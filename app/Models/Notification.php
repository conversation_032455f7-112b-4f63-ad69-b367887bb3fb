<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'body',
        'message',
        'type',
        'sent_to_all',
        'total_recipients',
        'is_read',
        'fcm_token',
        'data',
        'sent_at',
        'read_at',
        'delivery_status',
        'fcm_message_id',
        'delivery_error',
        'delivery_attempted_at',
        'delivery_attempts',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_read' => 'boolean',
        'sent_to_all' => 'boolean',
        'data' => 'array',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'delivery_attempted_at' => 'datetime',
        'delivery_attempts' => 'integer',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Create a booking request notification.
     */
    public static function createBookingRequest($providerId, $booking): self
    {
        // Check if notification already exists for this booking
        $existingNotification = self::where('user_id', $providerId)
            ->where('type', 'booking_request')
            ->whereJsonContains('data->booking_id', $booking->id)
            ->first();

        if ($existingNotification) {
            return $existingNotification; // Return existing notification instead of creating duplicate
        }

        return self::create([
            'user_id' => $providerId,
            'type' => 'booking_request',
            'title' => 'New Booking Request',
            'message' => "{$booking->client->name} wants to hire you for {$booking->duration_hours} hour" . ($booking->duration_hours > 1 ? 's' : '') . " on {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}",
            'body' => "{$booking->client->name} wants to hire you for {$booking->duration_hours} hour" . ($booking->duration_hours > 1 ? 's' : '') . " on {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}",
            'data' => [
                'booking_id' => $booking->id,
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'amount' => $booking->total_amount,
                'duration_hours' => $booking->duration_hours,
                'date' => $booking->booking_date->toISOString(),
                'meeting_location' => $booking->meeting_location,
                'notes' => $booking->notes,
            ],
        ]);
    }

    /**
     * Create a booking accepted notification.
     */
    public static function createBookingAccepted($clientId, $booking): self
    {
        return self::create([
            'user_id' => $clientId,
            'type' => 'booking_accepted',
            'title' => 'Booking Accepted!',
            'message' => "{$booking->provider->name} has accepted your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}. You can now chat with them!",
            'body' => "{$booking->provider->name} has accepted your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}. You can now chat with them!",
            'data' => [
                'booking_id' => $booking->id,
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'amount' => $booking->total_amount,
                'duration_hours' => $booking->duration_hours,
                'date' => $booking->booking_date->toISOString(),
                'meeting_location' => $booking->meeting_location,
                'notes' => $booking->notes,
            ],
        ]);
    }

    /**
     * Create a booking rejected notification.
     */
    public static function createBookingRejected($clientId, $booking, $reason): self
    {
        return self::create([
            'user_id' => $clientId,
            'type' => 'booking_rejected',
            'title' => 'Booking Rejected',
            'message' => "{$booking->provider->name} has rejected your booking request. Reason: {$reason}",
            'body' => "{$booking->provider->name} has rejected your booking request. Reason: {$reason}",
            'data' => [
                'booking_id' => $booking->id,
                'provider_id' => $booking->provider_id,
                'reason' => $reason,
                'amount' => $booking->total_amount,
            ],
        ]);
    }

    /**
     * Create a payment received notification.
     */
    public static function createPaymentReceived($providerId, $booking, $amount): self
    {
        return self::create([
            'user_id' => $providerId,
            'type' => 'payment_received',
            'title' => 'Payment Received',
            'message' => "You received ₹{$amount} for your booking with {$booking->client->name}",
            'body' => "You received ₹{$amount} for your booking with {$booking->client->name}",
            'data' => [
                'booking_id' => $booking->id,
                'client_id' => $booking->client_id,
                'amount' => $amount,
            ],
        ]);
    }

    /**
     * Create a payment credited notification.
     */
    public static function createPaymentCredited($providerId, $booking, $amount, $walletBalance): self
    {
        return self::create([
            'user_id' => $providerId,
            'type' => 'payment_credited',
            'title' => 'Payment Credited to Wallet',
            'message' => "₹{$amount} has been credited to your wallet for the completed meeting with {$booking->client->name}",
            'body' => "₹{$amount} has been credited to your wallet for the completed meeting with {$booking->client->name}",
            'data' => [
                'booking_id' => $booking->id,
                'client_id' => $booking->client_id,
                'amount' => $amount,
                'wallet_balance' => $walletBalance,
            ],
        ]);
    }

    /**
     * Create a meeting completed with payment pending notification.
     */
    public static function createMeetingCompletedPaymentPending($userId, $booking, $otherUserName, $duration, $amount): self
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'meeting_completed_payment_pending',
            'title' => 'Meeting Completed Successfully',
            'message' => "Meeting with {$otherUserName} has been completed and verified. Duration: {$duration}. Your payment will be credited to your wallet within 24 hours.",
            'body' => "Meeting with {$otherUserName} has been completed and verified. Duration: {$duration}. Your payment will be credited to your wallet within 24 hours.",
            'data' => [
                'booking_id' => $booking->id,
                'duration' => $duration,
                'amount' => $amount,
            ],
        ]);
    }

    // Note: Removed createMeetingStartReminder method as this notification is no longer needed

    /**
     * Create a meeting end reminder notification.
     */
    public static function createMeetingEndReminder($userId, $booking, $otherUserName): self
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'meeting_end_reminder',
            'title' => 'Meeting Ending - Photo Required',
            'message' => "{$otherUserName} has uploaded their meeting end photo. Please upload yours to complete the meeting verification.",
            'body' => "{$otherUserName} has uploaded their meeting end photo. Please upload yours to complete the meeting verification.",
            'data' => [
                'booking_id' => $booking->id,
            ],
        ]);
    }

    /**
     * Create a meeting completed notification.
     */
    public static function createMeetingCompleted($userId, $booking, $otherUserName, $duration): self
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'meeting_completed',
            'title' => 'Meeting Completed',
            'message' => "Meeting with {$otherUserName} has been completed and verified. Duration: {$duration}",
            'body' => "Meeting with {$otherUserName} has been completed and verified. Duration: {$duration}",
            'data' => [
                'booking_id' => $booking->id,
                'duration' => $duration,
            ],
        ]);
    }

    /**
     * Create a review reminder notification.
     */
    public static function createReviewReminder($userId, $booking, $otherUserName): self
    {
        // Check if reminder already exists for this booking
        $existingReminder = self::where('user_id', $userId)
            ->where('type', 'review_reminder')
            ->whereJsonContains('data->booking_id', $booking->id)
            ->first();

        if ($existingReminder) {
            return $existingReminder;
        }

        return self::create([
            'user_id' => $userId,
            'type' => 'review_reminder',
            'title' => 'Rate Your Experience',
            'message' => "How was your meeting with {$otherUserName}? Share your experience by leaving a review.",
            'body' => "How was your meeting with {$otherUserName}? Share your experience by leaving a review.",
            'data' => [
                'booking_id' => $booking->id,
                'other_user_name' => $otherUserName,
                'booking_date' => $booking->booking_date->format('M d, Y'),
                'booking_time' => $booking->booking_date->format('h:i A'),
            ],
        ]);
    }

    /**
     * Create a review received notification.
     */
    public static function createReviewReceived($userId, $booking, $review, $reviewerName): self
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'review_received',
            'title' => 'New Review Received',
            'message' => $review->is_anonymous
                ? "You received a new {$review->rating}-star review from an anonymous user."
                : "You received a new {$review->rating}-star review from {$reviewerName}.",
            'body' => $review->is_anonymous
                ? "You received a new {$review->rating}-star review from an anonymous user."
                : "You received a new {$review->rating}-star review from {$reviewerName}.",
            'data' => [
                'booking_id' => $booking->id,
                'review_id' => $review->id,
                'rating' => $review->rating,
                'reviewer_name' => $review->is_anonymous ? 'Anonymous' : $reviewerName,
                'review_text' => $review->review_text,
            ],
        ]);
    }

    /**
     * Create a dispute raised notification for admin.
     */
    public static function createDisputeRaised($booking, $disputeType): self
    {
        return self::create([
            'user_id' => 1, // Admin user ID
            'type' => 'dispute_raised',
            'title' => 'New Dispute Raised',
            'message' => "A {$disputeType} dispute has been raised for booking #{$booking->id} by {$booking->client->name}",
            'body' => "Client {$booking->client->name} has raised a {$disputeType} dispute for booking #{$booking->id} with provider {$booking->provider->name}. Please review and take appropriate action.",
            'data' => [
                'booking_id' => $booking->id,
                'dispute_type' => $disputeType,
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'disputed_at' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Create a dispute submitted confirmation notification for client.
     */
    public static function createDisputeSubmitted($booking, $disputeType): self
    {
        return self::create([
            'user_id' => $booking->client_id,
            'type' => 'dispute_submitted',
            'title' => 'Dispute Submitted',
            'message' => "Your {$disputeType} dispute for booking #{$booking->id} has been submitted and is under review.",
            'body' => "We have received your dispute regarding the booking with {$booking->provider->name}. Our team will review your case and get back to you within 24-48 hours.",
            'data' => [
                'booking_id' => $booking->id,
                'dispute_type' => $disputeType,
                'provider_name' => $booking->provider->name,
            ],
        ]);
    }

    /**
     * Create a dispute status update notification.
     */
    public static function createDisputeStatusUpdate($userId, $booking, $status, $adminNotes = null): self
    {
        $statusDisplay = match($status) {
            'pending' => 'Pending Review',
            'investigating' => 'Under Investigation',
            'resolved' => 'Resolved',
            'rejected' => 'Rejected',
            default => ucfirst($status)
        };

        return self::create([
            'user_id' => $userId,
            'type' => 'dispute_updated',
            'title' => 'Dispute Status Updated',
            'message' => "Your dispute for booking #{$booking->id} has been updated to: {$statusDisplay}",
            'body' => "Your dispute regarding the booking with {$booking->provider->name} has been updated. Status: {$statusDisplay}" . ($adminNotes ? ". Admin notes: {$adminNotes}" : ""),
            'data' => [
                'booking_id' => $booking->id,
                'dispute_status' => $status,
                'admin_notes' => $adminNotes,
            ],
        ]);
    }

    /**
     * Create a dispute resolved notification.
     */
    public static function createDisputeResolved($userId, $booking, $resolvedInFavorOf, $amount = null): self
    {
        $isClient = $userId === $booking->client_id;
        $favoredUser = $resolvedInFavorOf === 'client' ? 'your favor' : 'the provider\'s favor';

        if ($resolvedInFavorOf === 'client' && $isClient) {
            $message = "Your dispute has been resolved in your favor. ₹{$amount} has been refunded to your wallet.";
            $title = 'Dispute Resolved - Refund Processed';
        } elseif ($resolvedInFavorOf === 'provider' && !$isClient) {
            $message = "The dispute for booking #{$booking->id} has been resolved in your favor. Payment has been released.";
            $title = 'Dispute Resolved - Payment Released';
        } else {
            $message = "The dispute for booking #{$booking->id} has been resolved in {$favoredUser}.";
            $title = 'Dispute Resolution Notice';
        }

        return self::create([
            'user_id' => $userId,
            'type' => 'dispute_resolved',
            'title' => $title,
            'message' => $message,
            'body' => $message,
            'data' => [
                'booking_id' => $booking->id,
                'resolved_in_favor_of' => $resolvedInFavorOf,
                'amount' => $amount,
            ],
        ]);
    }

    /**
     * Create a booking auto-rejected notification.
     */
    public static function createBookingAutoRejected($booking): self
    {
        return self::create([
            'user_id' => $booking->client_id,
            'type' => 'booking_auto_rejected',
            'title' => 'Booking Auto-Rejected',
            'message' => "Your booking request was automatically rejected because the provider did not respond before the meeting start time. You have been refunded ₹{$booking->total_amount}.",
            'body' => "Your booking request was automatically rejected because the provider did not respond before the meeting start time. You have been refunded ₹{$booking->total_amount}.",
            'data' => [
                'booking_id' => $booking->id,
                'provider_id' => $booking->provider_id,
                'provider_name' => $booking->provider->name ?? 'Provider',
                'booking_date' => $booking->booking_date->format('M d, Y'),
                'booking_time' => $booking->booking_date->format('h:i A'),
                'refund_amount' => $booking->total_amount,
                'reason' => 'Provider did not respond before meeting start time',
            ],
        ]);
    }
}
