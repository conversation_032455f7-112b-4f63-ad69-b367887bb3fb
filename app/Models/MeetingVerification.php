<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class MeetingVerification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'booking_id',
        'client_start_photo',
        'provider_start_photo',
        'client_end_photo',
        'provider_end_photo',
        'client_start_time',
        'provider_start_time',
        'client_end_time',
        'provider_end_time',
        'meeting_start_time',
        'meeting_end_time',
        'calculated_duration_minutes',
        'is_verified',
        'notes',
        'client_start_latitude',
        'client_start_longitude',
        'client_start_address',
        'provider_start_latitude',
        'provider_start_longitude',
        'provider_start_address',
        'client_end_latitude',
        'client_end_longitude',
        'client_end_address',
        'provider_end_latitude',
        'provider_end_longitude',
        'provider_end_address',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'client_start_time' => 'datetime',
        'provider_start_time' => 'datetime',
        'client_end_time' => 'datetime',
        'provider_end_time' => 'datetime',
        'meeting_start_time' => 'datetime',
        'meeting_end_time' => 'datetime',
        'is_verified' => 'boolean',
    ];

    /**
     * Get the booking that owns the meeting verification.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(TimeSpendingBooking::class, 'booking_id');
    }

    /**
     * Get the client start photo URL.
     */
    public function getClientStartPhotoUrlAttribute(): ?string
    {
        return $this->client_start_photo ? asset('storage/' . $this->client_start_photo) : null;
    }

    /**
     * Get the provider start photo URL.
     */
    public function getProviderStartPhotoUrlAttribute(): ?string
    {
        return $this->provider_start_photo ? asset('storage/' . $this->provider_start_photo) : null;
    }

    /**
     * Get the client end photo URL.
     */
    public function getClientEndPhotoUrlAttribute(): ?string
    {
        return $this->client_end_photo ? asset('storage/' . $this->client_end_photo) : null;
    }

    /**
     * Get the provider end photo URL.
     */
    public function getProviderEndPhotoUrlAttribute(): ?string
    {
        return $this->provider_end_photo ? asset('storage/' . $this->provider_end_photo) : null;
    }

    /**
     * Check if both start photos are uploaded.
     */
    public function hasBothStartPhotos(): bool
    {
        return !empty($this->client_start_photo) && !empty($this->provider_start_photo);
    }

    /**
     * Check if both end photos are uploaded.
     */
    public function hasBothEndPhotos(): bool
    {
        return !empty($this->client_end_photo) && !empty($this->provider_end_photo);
    }

    /**
     * Check if all required photos are uploaded (both start and end from both users).
     */
    public function hasAllRequiredPhotos(): bool
    {
        return $this->hasBothStartPhotos() && $this->hasBothEndPhotos();
    }

    /**
     * Check if any photos are uploaded.
     */
    public function hasAnyPhotos(): bool
    {
        return !empty($this->client_start_photo) ||
               !empty($this->provider_start_photo) ||
               !empty($this->client_end_photo) ||
               !empty($this->provider_end_photo);
    }

    /**
     * Get detailed photo upload status for a specific user.
     */
    public function getUserPhotoStatus(int $userId): array
    {
        $booking = $this->booking;
        $isClient = $booking->client_id === $userId;

        return [
            'start_photo_uploaded' => $isClient ? !empty($this->client_start_photo) : !empty($this->provider_start_photo),
            'end_photo_uploaded' => $isClient ? !empty($this->client_end_photo) : !empty($this->provider_end_photo),
            'partner_start_photo_uploaded' => $isClient ? !empty($this->provider_start_photo) : !empty($this->client_start_photo),
            'partner_end_photo_uploaded' => $isClient ? !empty($this->provider_end_photo) : !empty($this->client_end_photo),
            'meeting_started' => $this->hasBothStartPhotos(),
            'meeting_ended' => $this->hasBothEndPhotos(),
            'is_verified' => $this->hasAllRequiredPhotos(),
        ];
    }

    /**
     * Get missing photos list.
     */
    public function getMissingPhotos(): array
    {
        $missing = [];

        if (empty($this->client_start_photo)) $missing[] = 'client_start_photo';
        if (empty($this->provider_start_photo)) $missing[] = 'provider_start_photo';
        if (empty($this->client_end_photo)) $missing[] = 'client_end_photo';
        if (empty($this->provider_end_photo)) $missing[] = 'provider_end_photo';

        return $missing;
    }

    /**
     * Get post-meeting action type based on photo verification status.
     */
    public function getPostMeetingActionType(): string
    {
        if ($this->hasAllRequiredPhotos()) {
            return 'rate_experience';
        }

        return 'no_show_or_end';
    }

    /**
     * Calculate and update meeting times and duration.
     */
    public function calculateMeetingDuration(): void
    {
        // Calculate meeting start time (when both start photos are uploaded)
        if ($this->hasBothStartPhotos() && !$this->meeting_start_time) {
            $this->meeting_start_time = max($this->client_start_time, $this->provider_start_time);
        }

        // Calculate meeting end time (when both end photos are uploaded)
        if ($this->hasBothEndPhotos() && !$this->meeting_end_time) {
            $this->meeting_end_time = max($this->client_end_time, $this->provider_end_time);
        }

        // Calculate duration if both start and end times are available
        if ($this->meeting_start_time && $this->meeting_end_time) {
            $startTime = Carbon::parse($this->meeting_start_time);
            $endTime = Carbon::parse($this->meeting_end_time);
            $this->calculated_duration_minutes = $endTime->diffInMinutes($startTime);
            $this->is_verified = true;
        }

        $this->save();
    }

    /**
     * Get formatted duration string.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->calculated_duration_minutes) {
            return 'Not calculated';
        }

        $hours = floor($this->calculated_duration_minutes / 60);
        $minutes = $this->calculated_duration_minutes % 60;

        if ($hours > 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . 
                   ($minutes > 0 ? ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '') : '');
        }

        return $minutes . ' minute' . ($minutes > 1 ? 's' : '');
    }

    /**
     * Get verification status for a user.
     */
    public function getUserVerificationStatus(int $userId): array
    {
        $booking = $this->booking;
        $isClient = $booking->client_id === $userId;
        
        return [
            'start_photo_uploaded' => $isClient ? !empty($this->client_start_photo) : !empty($this->provider_start_photo),
            'end_photo_uploaded' => $isClient ? !empty($this->client_end_photo) : !empty($this->provider_end_photo),
            'partner_start_photo_uploaded' => $isClient ? !empty($this->provider_start_photo) : !empty($this->client_start_photo),
            'partner_end_photo_uploaded' => $isClient ? !empty($this->provider_end_photo) : !empty($this->client_end_photo),
            'meeting_started' => $this->hasBothStartPhotos(),
            'meeting_ended' => $this->hasBothEndPhotos(),
            'is_verified' => $this->is_verified,
            'duration' => $this->formatted_duration,
        ];
    }

    /**
     * Create or get meeting verification for a booking.
     */
    public static function getOrCreateForBooking(int $bookingId): self
    {
        return self::firstOrCreate(['booking_id' => $bookingId]);
    }
}
