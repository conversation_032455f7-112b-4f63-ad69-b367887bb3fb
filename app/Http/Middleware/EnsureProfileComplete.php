<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureProfileComplete
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for authenticated users
        if (Auth::check()) {
            $user = Auth::user();

            // Skip profile completion check for admin users
            if ($user->isAdmin()) {
                return $next($request);
            }

            // Skip if already on profile-related pages to avoid redirect loops
            if ($request->routeIs('profile.*') || $request->routeIs('gallery.*')) {
                return $next($request);
            }

            // Skip notification routes - these should always be accessible to authenticated users
            if ($request->is('notifications*') || $request->is('debug/notifications')) {
                return $next($request);
            }

            // Check if profile is incomplete
            if (!$user->isProfileComplete()) {
                // Handle JSON/AJAX requests
                if ($request->expectsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Complete your profile first to access all features. Your profile is ' . $user->getProfileCompletionPercentage() . '% complete.',
                        'redirect' => route('profile.edit')
                    ], 403);
                }

                return redirect()->route('profile.edit')
                    ->with('warning', 'Complete your profile first to access all features. Your profile is ' . $user->getProfileCompletionPercentage() . '% complete.');
            }
        }

        return $next($request);
    }
}
