<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Content Security Policy - Updated for Tailwind CDN, Bootstrap CDN, DataTables CDN, and Razorpay compatibility
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com https://fonts.googleapis.com https://checkout.razorpay.com https://apis.google.com https://cdn.jsdelivr.net https://www.gstatic.com https://cdn.datatables.net; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdn.tailwindcss.com https://unpkg.com https://cdn.jsdelivr.net https://cdn.datatables.net; " .
               "font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net https://cdn.jsdelivr.net; " .
               "img-src 'self' data: https: blob:; " .
               "connect-src 'self' https://api.razorpay.com https://lumberjack.razorpay.com https://checkout.razorpay.com https://accounts.google.com https://nominatim.openstreetmap.org https://api.bigdatacloud.net https://fcm.googleapis.com https://firebase.googleapis.com; " .
               "frame-src 'self' https://checkout.razorpay.com https://api.razorpay.com https://accounts.google.com; " .
               "media-src 'self' blob:; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self';";

        // Only add upgrade-insecure-requests in production with HTTPS
        if (app()->environment('production') && $request->isSecure()) {
            $csp .= " upgrade-insecure-requests;";
        }

        $response->headers->set('Content-Security-Policy', $csp);
        
        // Security Headers
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        // Permissions Policy with enhanced camera and payment support
        $permissionsPolicy = 'geolocation=*, microphone=*, camera=*, payment=(self "https://checkout.razorpay.com" "https://api.razorpay.com"), display-capture=*, screen-wake-lock=*';
        $response->headers->set('Permissions-Policy', $permissionsPolicy);

        // Remove deprecated Feature-Policy header if it exists
        $response->headers->remove('Feature-Policy');

        // Add cache control headers to prevent caching of security headers
        $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', '0');

        // Add a custom header to force policy refresh
        $response->headers->set('X-Permissions-Policy-Version', '2.0-' . time());
        
        // HSTS (only in production with HTTPS)
        if (app()->environment('production') && $request->isSecure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
        
        // Remove server information
        $response->headers->remove('Server');
        $response->headers->remove('X-Powered-By');
        
        return $response;
    }
}
