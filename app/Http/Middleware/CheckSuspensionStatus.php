<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CheckSuspensionStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for authenticated users
        if (!auth()->check()) {
            return $next($request);
        }

        $user = auth()->user();

        // Skip check for admin users
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check if user is suspended and if suspension has expired
        if ($user->is_suspended && $user->suspension_end_date) {
            $currentTime = Carbon::now();
            
            // If suspension has expired, automatically unsuspend the user
            if ($currentTime->gt($user->suspension_end_date)) {
                $this->autoUnsuspendUser($user, $currentTime);
            }
        }

        return $next($request);
    }

    /**
     * Automatically unsuspend a user whose suspension period has expired.
     */
    private function autoUnsuspendUser($user, Carbon $currentTime): void
    {
        try {
            $originalSuspensionReason = $user->suspension_reason;
            $expiredHours = $currentTime->diffInHours($user->suspension_end_date);

            // Unsuspend the user
            $user->update([
                'is_suspended' => false,
                'suspension_end_date' => null,
                'suspension_reason' => null,
            ]);

            // Send notification to user
            Notification::create([
                'user_id' => $user->id,
                'type' => 'account_unsuspended',
                'title' => 'Account Reactivated',
                'message' => 'Your account suspension has been automatically lifted.',
                'body' => 'Good news! Your account suspension period has ended and your account has been automatically reactivated. You can now use all platform features normally.',
                'data' => [
                    'suspension_reason' => $originalSuspensionReason,
                    'expired_hours' => $expiredHours,
                    'unsuspended_at' => $currentTime->toISOString(),
                    'action_type' => 'realtime_unsuspension',
                    'action_url' => url('/'),
                    'action_text' => 'Continue'
                ],
            ]);

            // Log the automatic unsuspension
            Log::info('User automatically unsuspended via middleware', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'original_suspension_reason' => $originalSuspensionReason,
                'expired_hours' => $expiredHours,
                'unsuspended_at' => $currentTime,
                'action' => 'realtime_unsuspension',
                'request_url' => request()->url(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to auto-unsuspend user via middleware', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
