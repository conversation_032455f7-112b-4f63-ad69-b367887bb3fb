<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\TimeSpendingBooking;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\Response;

class ValidateChatAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for authenticated users
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to access chat.');
        }

        $user = Auth::user();

        // If accessing chat index, check if user has any active bookings
        if ($request->routeIs('chat.index')) {
            $hasActiveBookings = TimeSpendingBooking::with(['client', 'provider'])
                ->where(function($query) use ($user) {
                    $query->where('client_id', $user->id)
                          ->orWhere('provider_id', $user->id);
                })
                ->where('payment_status', 'paid')
                ->where('provider_status', 'accepted')
                ->where('chat_enabled', true)
                ->whereIn('status', ['confirmed', 'accepted'])
                ->get()
                ->filter(function($booking) {
                    // Calculate end time and check if booking hasn't ended yet (IST timezone)
                    $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
                    $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);

                    // Check if meeting is completed (both end photos uploaded) or time has passed
                    $isMeetingCompleted = $booking->isMeetingCompleted();
                    $isTimeExpired = now()->setTimezone('Asia/Kolkata')->gt($bookingEndTime);

                    // Only show as active if meeting is not completed AND time hasn't expired
                    return !$isMeetingCompleted && !$isTimeExpired;
                })
                ->isNotEmpty();

            if (!$hasActiveBookings) {
                return redirect()->route('home')->with('error', 'No active chat sessions available. You need an accepted booking to access chat.');
            }
        }

        // If accessing specific user chat, validate the booking exists and is active
        if ($request->routeIs('chat.user')) {
            $userId = $request->route('user');

            $booking = TimeSpendingBooking::with(['client', 'provider'])
                ->where(function($query) use ($user, $userId) {
                    $query->where(function($q) use ($user, $userId) {
                        $q->where('client_id', $user->id)
                          ->where('provider_id', $userId);
                    })->orWhere(function($q) use ($user, $userId) {
                        $q->where('client_id', $userId)
                          ->where('provider_id', $user->id);
                    });
                })
                ->where('payment_status', 'paid')
                ->where('provider_status', 'accepted')
                ->where('chat_enabled', true)
                ->whereIn('status', ['confirmed', 'accepted'])
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$booking) {
                return redirect()->route('home')->with('error', 'No active booking found with this user. Chat access denied.');
            }

            // Check if booking time hasn't ended yet (IST timezone) or meeting is completed
            $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
            $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);
            $isMeetingCompleted = $booking->isMeetingCompleted();

            if ($bookingEndTime->isPast() || $isMeetingCompleted) {
                return redirect()->route('home')->with('error', 'This booking has ended or been completed. Chat is no longer available.');
            }
        }

        return $next($request);
    }
}
