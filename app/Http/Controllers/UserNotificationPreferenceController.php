<?php

namespace App\Http\Controllers;

use App\Models\UserNotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserNotificationPreferenceController extends Controller
{
    /**
     * Display the user's notification preferences.
     */
    public function show()
    {
        $user = Auth::user();
        $preferences = $user->getNotificationPreferences();

        return view('profile.notifications', compact('preferences'));
    }

    /**
     * Update the user's notification preferences.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        // Define all possible checkbox fields
        $checkboxFields = [
            'push_notifications_enabled', 'push_general', 'push_payment', 'push_match',
            'push_partner_swapping', 'push_booking', 'push_meeting_reminders', 'push_subscription',
            'email_notifications_enabled', 'email_general', 'email_payment', 'email_match',
            'email_partner_swapping', 'email_booking', 'email_meeting_reminders', 'email_subscription',
            'inapp_notifications_enabled', 'inapp_general', 'inapp_payment', 'inapp_match',
            'inapp_partner_swapping', 'inapp_booking', 'inapp_meeting_reminders', 'inapp_subscription',
            'quiet_hours_enabled'
        ];

        // Convert checkbox values: if present = true, if not present = false
        $data = $request->all();
        foreach ($checkboxFields as $field) {
            $data[$field] = $request->has($field) ? true : false;
        }

        $validator = Validator::make($data, [
            // Push notification preferences
            'push_notifications_enabled' => 'boolean',
            'push_general' => 'boolean',
            'push_payment' => 'boolean',
            'push_match' => 'boolean',
            'push_partner_swapping' => 'boolean',
            'push_booking' => 'boolean',
            'push_meeting_reminders' => 'boolean',
            'push_subscription' => 'boolean',

            // Email notification preferences
            'email_notifications_enabled' => 'boolean',
            'email_general' => 'boolean',
            'email_payment' => 'boolean',
            'email_match' => 'boolean',
            'email_partner_swapping' => 'boolean',
            'email_booking' => 'boolean',
            'email_meeting_reminders' => 'boolean',
            'email_subscription' => 'boolean',

            // In-app notification preferences
            'inapp_notifications_enabled' => 'boolean',
            'inapp_general' => 'boolean',
            'inapp_payment' => 'boolean',
            'inapp_match' => 'boolean',
            'inapp_partner_swapping' => 'boolean',
            'inapp_booking' => 'boolean',
            'inapp_meeting_reminders' => 'boolean',
            'inapp_subscription' => 'boolean',

            // Quiet hours
            'quiet_hours_enabled' => 'boolean',
            'quiet_hours_start' => 'nullable|date_format:H:i',
            'quiet_hours_end' => 'nullable|date_format:H:i',
            'timezone' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get or create preferences
        $preferences = $user->getNotificationPreferences();

        // Update preferences with validated data
        $preferences->update($validator->validated());

        return redirect()->back()->with('success', 'Notification preferences updated successfully!');
    }

    /**
     * Update notification preferences via AJAX.
     */
    public function updateAjax(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            // Push notification preferences
            'push_notifications_enabled' => 'boolean',
            'push_general' => 'boolean',
            'push_payment' => 'boolean',
            'push_match' => 'boolean',
            'push_partner_swapping' => 'boolean',
            'push_booking' => 'boolean',
            'push_meeting_reminders' => 'boolean',
            'push_subscription' => 'boolean',

            // Email notification preferences
            'email_notifications_enabled' => 'boolean',
            'email_general' => 'boolean',
            'email_payment' => 'boolean',
            'email_match' => 'boolean',
            'email_partner_swapping' => 'boolean',
            'email_booking' => 'boolean',
            'email_meeting_reminders' => 'boolean',
            'email_subscription' => 'boolean',

            // In-app notification preferences
            'inapp_notifications_enabled' => 'boolean',
            'inapp_general' => 'boolean',
            'inapp_payment' => 'boolean',
            'inapp_match' => 'boolean',
            'inapp_partner_swapping' => 'boolean',
            'inapp_booking' => 'boolean',
            'inapp_meeting_reminders' => 'boolean',
            'inapp_subscription' => 'boolean',

            // Quiet hours
            'quiet_hours_enabled' => 'boolean',
            'quiet_hours_start' => 'nullable|date_format:H:i',
            'quiet_hours_end' => 'nullable|date_format:H:i',
            'timezone' => 'string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Get or create preferences
        $preferences = $user->getNotificationPreferences();

        // Update preferences with validated data
        $preferences->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully!'
        ]);
    }

    /**
     * Reset notification preferences to defaults.
     */
    public function reset()
    {
        $user = Auth::user();
        $preferences = $user->getNotificationPreferences();

        $preferences->update(UserNotificationPreference::getDefaults());

        return redirect()->back()->with('success', 'Notification preferences reset to defaults!');
    }

    /**
     * Reset notification preferences to defaults via AJAX.
     */
    public function resetAjax()
    {
        $user = Auth::user();
        $preferences = $user->getNotificationPreferences();

        $preferences->update(UserNotificationPreference::getDefaults());

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences reset to defaults!'
        ]);
    }
}
