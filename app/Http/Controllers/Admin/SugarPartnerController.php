<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SugarPartnerHardReject;
use App\Models\SugarPartnerRejection;
use App\Models\Feature;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Carbon\Carbon;

class SugarPartnerController extends Controller
{
    /**
     * Display a listing of Sugar Partners.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request): View
    {
        $query = User::query()->where(function($q) {
            $q->where('interested_in_sugar_partner', true)
              ->orWhereNotNull('sugar_partner_types');
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        // Gender filter
        if ($request->filled('gender')) {
            $gender = $request->get('gender');
            $query->where('gender', $gender);
        }

        // Sugar Partner type filter
        if ($request->filled('sugar_partner_type')) {
            $type = $request->get('sugar_partner_type');
            $query->whereJsonContains('sugar_partner_types', $type);
        }

        // Registration date range filter
        if ($request->filled('date_from')) {
            $dateFrom = Carbon::parse($request->get('date_from'))->startOfDay();
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($request->filled('date_to')) {
            $dateTo = Carbon::parse($request->get('date_to'))->endOfDay();
            $query->where('created_at', '<=', $dateTo);
        }

        // Active/inactive status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_suspended', false);
            } elseif ($status === 'inactive') {
                $query->where('is_suspended', true);
            }
        }

        // Sorting functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Validate sort fields
        $allowedSortFields = ['name', 'email', 'created_at', 'updated_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }

        $users = $query->orderBy($sortBy, $sortOrder)->paginate(15);

        // Get sugar partner type options for filter dropdown
        $sugarPartnerTypes = [
            'sugar_daddy' => 'Sugar Daddy',
            'sugar_mommy' => 'Sugar Mommy',
            'sugar_companion_female' => 'Sugar Babe',
            'sugar_companion_male' => 'Sugar Boy'
        ];

        // Get all users for the exchange modal with rejection status
        $allUsers = User::where(function($q) {
            $q->where('interested_in_sugar_partner', true)
              ->orWhereNotNull('sugar_partner_types');
        })->orderBy('name')->get();

        // Add rejection status information to each user
        $usersWithRejectionStatus = $allUsers->map(function ($user) use ($allUsers) {
            $rejectionStatus = [];

            foreach ($allUsers as $otherUser) {
                if ($user->id !== $otherUser->id) {
                    // Check for hard reject
                    $hardReject = SugarPartnerHardReject::existsBetweenUsers($user->id, $otherUser->id);

                    // Check for soft reject (get the most recent soft reject by this user against the other user)
                    $softReject = SugarPartnerRejection::where('rejector_id', $user->id)
                        ->where('rejected_user_id', $otherUser->id)
                        ->where('rejection_type', 'soft_reject')
                        ->latest()
                        ->first();

                    $rejectionStatus[$otherUser->id] = [
                        'hard_reject' => $hardReject,
                        'soft_reject' => $softReject ? true : false,
                        'soft_reject_date' => $softReject ? $softReject->created_at : null
                    ];
                }
            }

            // Assign the complete array at once to avoid Laravel's indirect modification issue
            $user->rejection_status = $rejectionStatus;
            return $user;
        });

        // Get pricing and currency for the exchange modal
        $sugarPartnerFeature = Feature::where('name', 'sugar_partner')->first();
        $pricing = $sugarPartnerFeature?->options['pricing'] ?? [
            'sugar_daddy' => 200.00,
            'sugar_mommy' => 200.00,
            'sugar_companion_female' => 100.00,
            'sugar_companion_male' => 100.00
        ];
        $currency = $sugarPartnerFeature?->options['currency'] ?? 'INR';

        return view('admin.sugar-partners.index', compact('users', 'sugarPartnerTypes', 'usersWithRejectionStatus', 'pricing', 'currency'));
    }

    /**
     * Display the specified Sugar Partner profile.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\View\View
     */
    public function show(User $user): View
    {
        // Ensure the user is a sugar partner
        if (!$user->interested_in_sugar_partner && empty($user->sugar_partner_types)) {
            abort(404, 'User is not a Sugar Partner');
        }

        // Load any additional relationships if needed
        $user->load(['galleryImages', 'sentMessages', 'receivedMessages']);

        return view('admin.sugar-partners.show', compact('user'));
    }

    /**
     * Get Sugar Partners data for AJAX requests.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Request $request)
    {
        $query = User::query()->where(function($q) {
            $q->where('interested_in_sugar_partner', true)
              ->orWhereNotNull('sugar_partner_types');
        });

        // Apply same filters as index method
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        if ($request->filled('gender')) {
            $gender = $request->get('gender');
            $query->where('gender', $gender);
        }

        if ($request->filled('sugar_partner_type')) {
            $type = $request->get('sugar_partner_type');
            $query->whereJsonContains('sugar_partner_types', $type);
        }

        if ($request->filled('date_from')) {
            $dateFrom = Carbon::parse($request->get('date_from'))->startOfDay();
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($request->filled('date_to')) {
            $dateTo = Carbon::parse($request->get('date_to'))->endOfDay();
            $query->where('created_at', '<=', $dateTo);
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_suspended', false);
            } elseif ($status === 'inactive') {
                $query->where('is_suspended', true);
            }
        }

        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSortFields = ['name', 'email', 'created_at', 'updated_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }

        $users = $query->orderBy($sortBy, $sortOrder)->paginate(15);

        return view('admin.sugar-partners.table', compact('users'))->render();
    }
}
