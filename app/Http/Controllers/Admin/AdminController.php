<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MeetingAddress;
use App\Models\Notification;
use App\Models\User;
use App\Models\AdminRevenue;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\TimeSpendingBooking;
use App\Models\EventPayment;
use App\Models\ChatMessage;
use App\Models\CoupleActivityRequest;
use App\Models\ContactSubmission;
use App\Models\UserReport;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Middleware is already applied in routes/web.php
    }

    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        try {
            $regularUsersCount = User::where('role', 'user')->where('is_admin', false)->count();
            $managementUsersCount = User::where(function($q) {
                $q->whereIn('role', ['admin', 'super_admin', 'editor', 'accountant'])
                  ->orWhere('is_admin', true);
            })->count();
            $notificationsCount = Notification::count();
            $meetingEventsCount = MeetingAddress::count();
            $contactSubmissionsCount = ContactSubmission::count();
            $pendingContactsCount = ContactSubmission::where('status', 'pending')->count();

            // Get time filter parameters
            $timeFilter = $request->get('filter', 'month');
            $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
            $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

            // Get comprehensive analytics data
            $analyticsData = $this->getDashboardAnalytics();
            $overviewStats = $this->getOverviewStats($startDate, $endDate);
            $userRegistrationData = $this->getUserRegistrationData($startDate, $endDate, $timeFilter);
            $revenueData = $this->getRevenueData($startDate, $endDate, $timeFilter);
            $activeSubscriptionData = $this->getActiveSubscriptionData();
            $featureUsageData = $this->getFeatureUsageData($startDate, $endDate);

            // Check if this is an AJAX request
            if (request()->ajax()) {
                $html = view('admin.dashboard', compact(
                    'regularUsersCount',
                    'managementUsersCount',
                    'notificationsCount',
                    'meetingEventsCount',
                    'contactSubmissionsCount',
                    'pendingContactsCount',
                    'analyticsData',
                    'overviewStats',
                    'userRegistrationData',
                    'revenueData',
                    'activeSubscriptionData',
                    'featureUsageData',
                    'timeFilter',
                    'startDate',
                    'endDate'
                ))->render();
                return $html;
            }

            return view('admin.dashboard', compact(
                'regularUsersCount',
                'managementUsersCount',
                'notificationsCount',
                'meetingEventsCount',
                'contactSubmissionsCount',
                'pendingContactsCount',
                'analyticsData',
                'overviewStats',
                'userRegistrationData',
                'revenueData',
                'activeSubscriptionData',
                'featureUsageData',
                'timeFilter',
                'startDate',
                'endDate'
            ));
        } catch (\Exception $e) {
            \Log::error('Dashboard error: ' . $e->getMessage());

            // Return with default data on error
            $regularUsersCount = 0;
            $managementUsersCount = 0;
            $notificationsCount = 0;
            $meetingEventsCount = 0;
            $analyticsData = $this->getDefaultAnalyticsData();
            $overviewStats = $this->getDefaultOverviewStats();
            $userRegistrationData = ['labels' => [], 'data' => [], 'total' => 0];
            $revenueData = ['timeline' => ['labels' => [], 'data' => []], 'by_source' => [], 'total' => 0];
            $activeSubscriptionData = ['labels' => [], 'data' => [], 'total' => 0];
            $featureUsageData = [];
            $timeFilter = 'month';
            $startDate = now()->startOfMonth()->toDateString();
            $endDate = now()->endOfMonth()->toDateString();

            return view('admin.dashboard', compact(
                'regularUsersCount',
                'managementUsersCount',
                'notificationsCount',
                'meetingEventsCount',
                'analyticsData',
                'overviewStats',
                'userRegistrationData',
                'revenueData',
                'activeSubscriptionData',
                'featureUsageData',
                'timeFilter',
                'startDate',
                'endDate'
            ))->with('error', 'Unable to load dashboard data. Please try again.');
        }
    }

    /**
     * Get dashboard statistics for AJAX requests.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardStats()
    {
        $stats = [
            'regularUsersCount' => User::where('role', 'user')->where('is_admin', false)->count(),
            'managementUsersCount' => User::where(function($q) {
                $q->whereIn('role', ['admin', 'super_admin', 'editor', 'accountant'])
                  ->orWhere('is_admin', true);
            })->count(),
            'notificationsCount' => Notification::count(),
            'meetingEventsCount' => MeetingAddress::count(),
            'contactSubmissionsCount' => ContactSubmission::count(),
            'pendingContactsCount' => ContactSubmission::where('status', 'pending')->count(),
        ];

        return response()->json(['stats' => $stats]);
    }

    /**
     * Get analytics data via AJAX.
     */
    public function getAnalyticsData(Request $request)
    {
        try {
            $type = $request->get('type', 'overview');
            $timeFilter = $request->get('filter', 'month');
            $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
            $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

            switch ($type) {
                case 'user_registrations':
                    return response()->json($this->getUserRegistrationData($startDate, $endDate, $timeFilter));
                case 'revenue':
                    return response()->json($this->getRevenueData($startDate, $endDate, $timeFilter));
                case 'active_subscriptions':
                    return response()->json($this->getActiveSubscriptionData());
                case 'feature_usage':
                    return response()->json($this->getFeatureUsageData($startDate, $endDate));
                case 'overview':
                    return response()->json($this->getOverviewStats($startDate, $endDate));
                default:
                    return response()->json(['error' => 'Invalid data type'], 400);
            }
        } catch (\Exception $e) {
            \Log::error('Analytics data error: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch analytics data'], 500);
        }
    }

    /**
     * Export analytics data.
     */
    public function exportAnalytics(Request $request)
    {
        try {
            $timeFilter = $request->get('filter', 'month');
            $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
            $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

            $data = [
                'overview' => $this->getOverviewStats($startDate, $endDate),
                'user_registrations' => $this->getUserRegistrationData($startDate, $endDate, $timeFilter),
                'revenue' => $this->getRevenueData($startDate, $endDate, $timeFilter),
                'active_subscriptions' => $this->getActiveSubscriptionData(),
                'feature_usage' => $this->getFeatureUsageData($startDate, $endDate)
            ];

            $filename = 'analytics_' . $startDate . '_to_' . $endDate . '.json';

            return response()->json($data)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
        } catch (\Exception $e) {
            \Log::error('Analytics export error: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to export analytics data'], 500);
        }
    }

    /**
     * Get dashboard analytics data.
     *
     * @return array
     */
    private function getDashboardAnalytics()
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // User analytics
        $newUsersThisMonth = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $newUsersLastMonth = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereBetween('created_at', [
                Carbon::now()->subMonth()->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth()
            ])
            ->count();

        $userGrowthRate = $newUsersLastMonth > 0
            ? round((($newUsersThisMonth - $newUsersLastMonth) / $newUsersLastMonth) * 100, 2)
            : 0;

        // Revenue analytics
        $revenueThisMonth = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_admin_earnings');

        $revenueLastMonth = AdminRevenue::whereBetween('created_at', [
                Carbon::now()->subMonth()->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth()
            ])
            ->sum('total_admin_earnings');

        $revenueGrowthRate = $revenueLastMonth > 0
            ? round((($revenueThisMonth - $revenueLastMonth) / $revenueLastMonth) * 100, 2)
            : 0;

        // Activity metrics
        $activeSubscriptions = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        $totalBookings = TimeSpendingBooking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $eventParticipants = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Quick stats for last 7 days
        $last7Days = Carbon::now()->subDays(7);
        $recentActivity = [
            'new_users' => User::where('role', 'user')
                ->where('is_admin', false)
                ->where('created_at', '>=', $last7Days)
                ->count(),
            'new_bookings' => TimeSpendingBooking::where('payment_status', 'paid')
                ->where('created_at', '>=', $last7Days)
                ->count(),
            'chat_messages' => ChatMessage::where('created_at', '>=', $last7Days)->count(),
            'notifications_sent' => Notification::where('created_at', '>=', $last7Days)->count(),
        ];

        return [
            'user_growth' => [
                'current' => $newUsersThisMonth,
                'previous' => $newUsersLastMonth,
                'growth_rate' => $userGrowthRate
            ],
            'revenue_growth' => [
                'current' => $revenueThisMonth,
                'previous' => $revenueLastMonth,
                'growth_rate' => $revenueGrowthRate
            ],
            'activity_metrics' => [
                'active_subscriptions' => $activeSubscriptions,
                'total_bookings' => $totalBookings,
                'event_participants' => $eventParticipants
            ],
            'recent_activity' => $recentActivity
        ];
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($startDate, $endDate)
    {
        // Total users (not filtered by time period)
        $totalUsers = User::where('role', 'user')->where('is_admin', false)->count();

        // Total events (not filtered by time period)
        $totalEvents = MeetingAddress::where('is_event_enabled', true)->count();

        // Total revenue (filtered by time period)
        $totalRevenue = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_admin_earnings');

        // Platform fees (filtered by time period)
        $totalPlatformFees = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->sum('platform_fee');

        // Active subscriptions (not filtered by time period)
        $activeSubscriptions = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        // Additional metrics for charts
        $totalBookings = TimeSpendingBooking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $eventParticipants = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $chatMessages = ChatMessage::whereBetween('created_at', [$startDate, $endDate])->count();

        $contactSubmissions = ContactSubmission::whereBetween('created_at', [$startDate, $endDate])->count();
        $pendingContacts = ContactSubmission::where('status', 'pending')->count();

        // User reports statistics
        $pendingReports = UserReport::where('status', 'pending')->count();
        $totalReports = UserReport::whereBetween('created_at', [$startDate, $endDate])->count();

        return [
            'total_users' => $totalUsers,
            'total_events' => $totalEvents,
            'total_revenue' => $totalRevenue,
            'total_platform_fees' => $totalPlatformFees,
            'active_subscriptions' => $activeSubscriptions,
            'total_bookings' => $totalBookings,
            'event_participants' => $eventParticipants,
            'chat_messages' => $chatMessages,
            'contact_submissions' => $contactSubmissions,
            'pending_contacts' => $pendingContacts,
            'pending_reports' => $pendingReports,
            'total_reports' => $totalReports,
        ];
    }

    /**
     * Get user registration data for pie chart.
     */
    private function getUserRegistrationData($startDate, $endDate, $timeFilter)
    {
        // Get registrations by gender for pie chart
        $maleCount = User::where('role', 'user')
            ->where('is_admin', false)
            ->where('gender', 'male')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $femaleCount = User::where('role', 'user')
            ->where('is_admin', false)
            ->where('gender', 'female')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $totalRegistrations = $maleCount + $femaleCount;

        // Always show both genders for consistency
        $labels = ['Female', 'Male'];
        $data = [$femaleCount, $maleCount];
        $colors = ['#8B5CF6', '#3B82F6']; // Purple for Female, Blue for Male

        return [
            'labels' => $labels,
            'data' => $data,
            'total' => $totalRegistrations,
            'colors' => $colors
        ];
    }

    /**
     * Get revenue data for charts.
     */
    private function getRevenueData($startDate, $endDate, $timeFilter)
    {
        // Revenue by source
        $revenueBySource = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('revenue_type, SUM(total_admin_earnings) as total')
            ->groupBy('revenue_type')
            ->get();

        // Revenue over time
        $revenueOverTime = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw($this->getSelectRaw($timeFilter) . ' as date, SUM(total_admin_earnings) as total')
            ->groupByRaw($this->getGroupByRaw($timeFilter))
            ->orderBy('date')
            ->get();

        $labels = [];
        $data = [];
        $period = Carbon::parse($startDate);
        $endPeriod = Carbon::parse($endDate);

        while ($period <= $endPeriod) {
            $dateKey = $period->format($this->getCarbonFormat($timeFilter));
            $labels[] = $this->formatLabel($period, $timeFilter);

            $revenue = $revenueOverTime->where('date', $dateKey)->first();
            $data[] = $revenue ? (float) $revenue->total : 0;

            $period = $this->incrementPeriod($period, $timeFilter);
        }

        // Calculate specific revenue amounts with proper data sources

        // 1. Subscription Revenue - from user_subscriptions table
        $subscriptionRevenue = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('amount_paid');

        // 2. Booking Commission - from admin_revenues table for booking commissions
        $bookingCommission = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->where('revenue_type', 'booking_payment')
            ->sum('commission_amount');

        // 3. Platform Fees - from admin_revenues table for platform fees
        $platformFees = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->sum('platform_fee');

        // 4. Event Revenue - from event_payments table
        $eventRevenue = \DB::table('event_payments')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('amount_paid');

        // Always show all 4 categories for consistency
        $sourceLabels = ['Subscription Revenue', 'Booking Commission', 'Platform Fees', 'Event Revenue'];
        $sourceData = [
            (float) $subscriptionRevenue,
            (float) $bookingCommission,
            (float) $platformFees,
            (float) $eventRevenue
        ];
        $sourceColors = ['#8B5CF6', '#F59E0B', '#007bff', '#10B981']; // Purple, Orange, Blue, Green

        return [
            'timeline' => [
                'labels' => $labels,
                'data' => $data
            ],
            'by_source' => [
                'labels' => $sourceLabels,
                'data' => $sourceData,
                'colors' => array_slice($sourceColors, 0, count($sourceLabels)),
                'formatted_data' => [
                    '₹' . number_format($subscriptionRevenue, 2),
                    '₹' . number_format($bookingCommission, 2),
                    '₹' . number_format($platformFees, 2),
                    '₹' . number_format($eventRevenue, 2)
                ],
                'subscription_revenue' => $subscriptionRevenue,
                'booking_commission' => $bookingCommission,
                'platform_fees' => $platformFees,
                'event_revenue' => $eventRevenue
            ],
            'total' => array_sum($sourceData),
            'formatted_total' => '₹' . number_format(array_sum($sourceData), 2)
        ];
    }

    /**
     * Get active subscription distribution data.
     */
    private function getActiveSubscriptionData()
    {
        // Get all active subscription durations dynamically
        $subscriptionCounts = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->join('subscription_plans', 'user_subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->selectRaw('subscription_plans.duration_months, COUNT(*) as count')
            ->groupBy('subscription_plans.duration_months')
            ->orderBy('subscription_plans.duration_months')
            ->get();

        // Prepare data arrays
        $labels = [];
        $data = [];
        $colors = ['#10B981', '#F59E0B', '#8B5CF6', '#EF4444', '#06B6D4']; // Green, Orange, Purple, Red, Cyan
        $colorIndex = 0;
        $finalColors = [];

        // Always include the main durations even if they have 0 subscriptions
        $mainDurations = [1, 3, 6, 12];
        $existingCounts = $subscriptionCounts->pluck('count', 'duration_months')->toArray();

        foreach ($mainDurations as $duration) {
            $count = $existingCounts[$duration] ?? 0;

            if ($duration == 1) {
                $labels[] = '1 Month';
            } else {
                $labels[] = $duration . ' Months';
            }

            $data[] = $count;
            $finalColors[] = $colors[$colorIndex % count($colors)];
            $colorIndex++;
        }

        // Add any other durations that exist but aren't in the main list
        foreach ($subscriptionCounts as $subscription) {
            if (!in_array($subscription->duration_months, $mainDurations)) {
                $duration = $subscription->duration_months;
                $labels[] = $duration == 1 ? '1 Month' : $duration . ' Months';
                $data[] = $subscription->count;
                $finalColors[] = $colors[$colorIndex % count($colors)];
                $colorIndex++;
            }
        }

        $total = array_sum($data);

        return [
            'labels' => $labels,
            'data' => $data,
            'total' => $total,
            'colors' => $finalColors
        ];
    }

    /**
     * Get feature usage data.
     */
    private function getFeatureUsageData($startDate, $endDate)
    {
        $profileCompletion = $this->getProfileCompletionStats();
        $eventStats = $this->getEventStats($startDate, $endDate);
        $subscriptionStats = $this->getSubscriptionStats($startDate, $endDate);
        $chatStats = $this->getChatStats($startDate, $endDate);
        $coupleActivityStats = $this->getCoupleActivityStats($startDate, $endDate);

        return [
            'profile_completion' => $profileCompletion,
            'events' => $eventStats,
            'subscriptions' => $subscriptionStats,
            'chat' => $chatStats,
            'couple_activity' => $coupleActivityStats
        ];
    }

    /**
     * Get profile completion statistics.
     */
    private function getProfileCompletionStats()
    {
        $totalUsers = User::where('role', 'user')->where('is_admin', false)->count();

        $completedProfiles = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereNotNull('name')
            ->whereNotNull('email')
            ->whereNotNull('gender')
            ->whereNotNull('date_of_birth')
            ->whereNotNull('profile_picture')
            ->count();

        $withGallery = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereHas('galleryImages')
            ->count();

        return [
            'total_users' => $totalUsers,
            'completed_profiles' => $completedProfiles,
            'completion_rate' => $totalUsers > 0 ? round(($completedProfiles / $totalUsers) * 100, 2) : 0,
            'with_gallery' => $withGallery,
            'gallery_rate' => $totalUsers > 0 ? round(($withGallery / $totalUsers) * 100, 2) : 0
        ];
    }

    /**
     * Get event statistics.
     */
    private function getEventStats($startDate, $endDate)
    {
        $totalEvents = MeetingAddress::count();
        $activeEvents = MeetingAddress::where('is_event_enabled', true)->count();
        $coupleEvents = MeetingAddress::where('is_couple_event', true)->count();

        $eventPayments = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $eventRevenue = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount_paid');

        return [
            'total_events' => $totalEvents,
            'active_events' => $activeEvents,
            'couple_events' => $coupleEvents,
            'participants' => $eventPayments,
            'revenue' => $eventRevenue
        ];
    }

    /**
     * Get subscription statistics.
     */
    private function getSubscriptionStats($startDate, $endDate)
    {
        $activeSubscriptions = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        $newSubscriptions = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $subscriptionRevenue = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount_paid');

        $renewalRate = $this->calculateRenewalRate($startDate, $endDate);

        return [
            'active_subscriptions' => $activeSubscriptions,
            'new_subscriptions' => $newSubscriptions,
            'revenue' => $subscriptionRevenue,
            'renewal_rate' => $renewalRate
        ];
    }

    /**
     * Get chat statistics.
     */
    private function getChatStats($startDate, $endDate)
    {
        $totalMessages = ChatMessage::whereBetween('created_at', [$startDate, $endDate])->count();

        $activeChats = ChatMessage::whereBetween('created_at', [$startDate, $endDate])
            ->distinct('booking_id')
            ->count();

        $averageMessagesPerChat = $activeChats > 0 ? round($totalMessages / $activeChats, 2) : 0;

        return [
            'total_messages' => $totalMessages,
            'active_chats' => $activeChats,
            'average_messages_per_chat' => $averageMessagesPerChat
        ];
    }

    /**
     * Get couple activity statistics.
     */
    private function getCoupleActivityStats($startDate, $endDate)
    {
        try {
            $totalRequests = CoupleActivityRequest::whereBetween('created_at', [$startDate, $endDate])->count();
            $approvedRequests = CoupleActivityRequest::where('status', 'approved')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            // Simplified active couples count
            $activeCouples = User::where('role', 'user')
                ->where('is_admin', false)
                ->where('is_couple_activity_enabled', true)
                ->count();

            $approvalRate = $totalRequests > 0 ? round(($approvedRequests / $totalRequests) * 100, 2) : 0;

            return [
                'total_requests' => $totalRequests,
                'approved_requests' => $approvedRequests,
                'active_couples' => $activeCouples,
                'approval_rate' => $approvalRate
            ];
        } catch (\Exception $e) {
            \Log::error('Couple activity stats error: ' . $e->getMessage());
            return [
                'total_requests' => 0,
                'approved_requests' => 0,
                'active_couples' => 0,
                'approval_rate' => 0
            ];
        }
    }

    /**
     * Calculate subscription renewal rate.
     */
    private function calculateRenewalRate($startDate, $endDate)
    {
        try {
            $expiredSubscriptions = UserSubscription::whereBetween('expires_at', [$startDate, $endDate])
                ->where('status', 'expired')
                ->count();

            // Simplified renewal calculation - count new subscriptions in the period
            $newSubscriptions = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
                ->count();

            return $expiredSubscriptions > 0 ? round(($newSubscriptions / $expiredSubscriptions) * 100, 2) : 0;
        } catch (\Exception $e) {
            \Log::error('Renewal rate calculation error: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Display a listing of the regular users.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function users(Request $request)
    {
        $query = User::query()->where(function($q) {
            $q->where('role', 'user')->where('is_admin', false);
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role')) {
            $role = $request->get('role');
            if ($role === 'admin') {
                $query->where('is_admin', true);
            } elseif ($role === 'user') {
                $query->where('is_admin', false);
            }
        }

        // Status filter (payment status)
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'paid') {
                $query->whereNotNull('paid_at');
            } elseif ($status === 'not_paid') {
                $query->whereNull('paid_at');
            }
        }

        // Gender filter
        if ($request->filled('gender')) {
            $gender = $request->get('gender');
            $query->where('gender', $gender);
        }

        // Sorting functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Validate sort fields
        $allowedSortFields = ['name', 'email', 'created_at', 'updated_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }

        $query->orderBy($sortBy, $sortOrder);

        // Load change logs relationship for the modal
        $query->with(['changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }]);

        $users = $query->paginate(20)->appends($request->query());

        // Check if this is an AJAX request
        if (request()->ajax()) {
            $html = view('admin.users.index', compact('users'))->render();
            return $html;
        }

        return view('admin.users.index', compact('users'));
    }

    /**
     * Display a listing of the management users.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function managementUsers(Request $request)
    {
        $query = User::query()->where(function($q) {
            $q->whereIn('role', ['admin', 'super_admin', 'editor', 'accountant'])
              ->orWhere('is_admin', true);
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role')) {
            $role = $request->get('role');
            if (in_array($role, ['admin', 'super_admin', 'editor', 'accountant'])) {
                $query->where('role', $role);
            }
        }

        // Status filter (active/inactive)
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->whereNotNull('email_verified_at');
            } elseif ($status === 'inactive') {
                $query->whereNull('email_verified_at');
            }
        }

        // Sorting functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Validate sort fields
        $allowedSortFields = ['name', 'email', 'role', 'created_at', 'updated_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }

        $query->orderBy($sortBy, $sortOrder);

        // Load change logs relationship for the modal
        $query->with(['changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }]);

        $users = $query->paginate(20)->appends($request->query());

        // Check if this is an AJAX request
        if (request()->ajax()) {
            $html = view('admin.management-users.index', compact('users'))->render();
            return $html;
        }

        return view('admin.management-users.index', compact('users'));
    }

    /**
     * Toggle the admin status of a user.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleAdmin(User $user)
    {
        // Don't allow removing admin status from yourself
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot remove your own admin status.');
        }

        $user->is_admin = !$user->is_admin;
        $user->save();

        return redirect()->route('admin.users')
            ->with('success', 'User admin status updated successfully.');
    }

    /**
     * Get users data for AJAX requests.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsersData(Request $request)
    {
        $query = User::where('role', 'user')->where('is_admin', false);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        // Gender filter
        if ($request->filled('gender')) {
            $query->where('gender', $request->get('gender'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'paid') {
                $query->whereHas('payments');
            } elseif ($status === 'not_paid') {
                $query->whereDoesntHave('payments');
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $allowedSortFields = ['name', 'email', 'created_at', 'updated_at'];

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $users = $query->with(['changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }])->paginate(20);

        $html = view('admin.users.table', compact('users'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'total' => $users->total()
            ]
        ]);
    }

    /**
     * Get management users data for AJAX requests.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManagementUsersData(Request $request)
    {
        $query = User::where(function($q) {
            $q->whereIn('role', ['admin', 'super_admin', 'editor', 'accountant'])
              ->orWhere('is_admin', true);
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $allowedSortFields = ['name', 'email', 'role', 'created_at', 'updated_at'];

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $users = $query->with(['changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }])->paginate(20);

        $html = view('admin.management-users.table', compact('users'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'total' => $users->total()
            ]
        ]);
    }

    /**
     * Get user details for AJAX requests.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserDetails(User $user)
    {
        $user->load(['changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }]);

        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'contact_number' => $user->contact_number,
                'gender' => $user->gender,
                'date_of_birth' => $user->date_of_birth,
                'profile_picture' => $user->profile_picture,
                'interests' => $user->interests,
                'expectation' => $user->expectation,
                'email_verified_at' => $user->email_verified_at,
                'is_suspended' => $user->is_suspended ?? false,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'change_logs' => $user->changeLogs->map(function($log) {
                    return [
                        'field' => $log->field,
                        'old_value' => $log->old_value,
                        'new_value' => $log->new_value,
                        'created_at' => $log->created_at->format('M d, Y H:i')
                    ];
                })
            ]
        ]);
    }

    /**
     * Suspend a user via AJAX.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function suspendUser(User $user)
    {
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot suspend yourself.'
            ], 400);
        }

        $user->update(['is_suspended' => true]);

        return response()->json([
            'success' => true,
            'message' => 'User suspended successfully.'
        ]);
    }

    /**
     * Activate a user via AJAX.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function activateUser(User $user)
    {
        $user->update(['is_suspended' => false]);

        return response()->json([
            'success' => true,
            'message' => 'User activated successfully.'
        ]);
    }

    /**
     * Get start date based on filter.
     */
    private function getStartDate($filter, $customStart = null)
    {
        if ($filter === 'custom' && $customStart) {
            return Carbon::parse($customStart)->startOfDay();
        }

        switch ($filter) {
            case 'today':
                return Carbon::today();
            case 'week':
                return Carbon::now()->startOfWeek();
            case 'month':
                return Carbon::now()->startOfMonth();
            case 'year':
                return Carbon::now()->startOfYear();
            default:
                return Carbon::now()->startOfMonth();
        }
    }

    /**
     * Get end date based on filter.
     */
    private function getEndDate($filter, $customEnd = null)
    {
        if ($filter === 'custom' && $customEnd) {
            return Carbon::parse($customEnd)->endOfDay();
        }

        switch ($filter) {
            case 'today':
                return Carbon::today()->endOfDay();
            case 'week':
                return Carbon::now()->endOfWeek();
            case 'month':
                return Carbon::now()->endOfMonth();
            case 'year':
                return Carbon::now()->endOfYear();
            default:
                return Carbon::now()->endOfMonth();
        }
    }

    /**
     * Get database-agnostic SELECT clause for date grouping.
     */
    private function getSelectRaw($filter)
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            switch ($filter) {
                case 'today':
                    return "strftime('%Y-%m-%d %H:00:00', created_at)";
                case 'week':
                case 'month':
                    return "strftime('%Y-%m-%d', created_at)";
                case 'year':
                    return "strftime('%Y-%m', created_at)";
                default:
                    return "strftime('%Y-%m-%d', created_at)";
            }
        } else {
            // MySQL/PostgreSQL
            switch ($filter) {
                case 'today':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')";
                case 'week':
                case 'month':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
                case 'year':
                    return "DATE_FORMAT(created_at, '%Y-%m')";
                default:
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
            }
        }
    }

    /**
     * Get database-agnostic GROUP BY clause.
     */
    private function getGroupByRaw($filter)
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            switch ($filter) {
                case 'today':
                    return "strftime('%Y-%m-%d %H:00:00', created_at)";
                case 'week':
                case 'month':
                    return "strftime('%Y-%m-%d', created_at)";
                case 'year':
                    return "strftime('%Y-%m', created_at)";
                default:
                    return "strftime('%Y-%m-%d', created_at)";
            }
        } else {
            // MySQL/PostgreSQL
            switch ($filter) {
                case 'today':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')";
                case 'week':
                case 'month':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
                case 'year':
                    return "DATE_FORMAT(created_at, '%Y-%m')";
                default:
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
            }
        }
    }

    /**
     * Get Carbon date format.
     */
    private function getCarbonFormat($filter)
    {
        switch ($filter) {
            case 'today':
                return 'Y-m-d H:00:00';
            case 'week':
            case 'month':
                return 'Y-m-d';
            case 'year':
                return 'Y-m';
            default:
                return 'Y-m-d';
        }
    }

    /**
     * Format label for charts.
     */
    private function formatLabel($period, $filter)
    {
        switch ($filter) {
            case 'today':
                return $period->format('H:00');
            case 'week':
            case 'month':
                return $period->format('M d');
            case 'year':
                return $period->format('M Y');
            default:
                return $period->format('M d');
        }
    }

    /**
     * Increment period based on filter.
     */
    private function incrementPeriod($period, $filter)
    {
        switch ($filter) {
            case 'today':
                return $period->addHour();
            case 'week':
            case 'month':
                return $period->addDay();
            case 'year':
                return $period->addMonth();
            default:
                return $period->addDay();
        }
    }

    /**
     * Get database-agnostic age group calculation.
     */
    private function getAgeGroupSelectRaw()
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            return "
                CASE
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 < 20 THEN 'Under 20'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 20 AND 25 THEN '20-25'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 26 AND 30 THEN '26-30'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 31 AND 35 THEN '31-35'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 36 AND 40 THEN '36-40'
                    ELSE 'Over 40'
                END
            ";
        } else {
            // MySQL/PostgreSQL
            return "
                CASE
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 20 THEN 'Under 20'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 20 AND 25 THEN '20-25'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 26 AND 30 THEN '26-30'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 31 AND 35 THEN '31-35'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 36 AND 40 THEN '36-40'
                    ELSE 'Over 40'
                END
            ";
        }
    }

    /**
     * Format revenue type for display.
     */
    private function formatRevenueType($type)
    {
        switch ($type) {
            case 'booking_commission':
                return 'Time Spending Commission';
            case 'subscription_payment':
                return 'Subscription Revenue';
            case 'event_payment':
                return 'Event Revenue';
            case 'couple_event_payment':
                return 'Couple Event Revenue';
            default:
                return ucwords(str_replace('_', ' ', $type));
        }
    }

    /**
     * Get default analytics data for error cases.
     */
    private function getDefaultAnalyticsData()
    {
        return [
            'user_growth' => [
                'current' => 0,
                'previous' => 0,
                'growth_rate' => 0
            ],
            'revenue_growth' => [
                'current' => 0,
                'previous' => 0,
                'growth_rate' => 0
            ],
            'activity_metrics' => [
                'active_subscriptions' => 0,
                'total_bookings' => 0,
                'event_participants' => 0
            ],
            'recent_activity' => [
                'new_users' => 0,
                'new_bookings' => 0,
                'chat_messages' => 0,
                'notifications_sent' => 0,
            ]
        ];
    }

    /**
     * Get default overview stats for error cases.
     */
    private function getDefaultOverviewStats()
    {
        return [
            'total_users' => 0,
            'new_users' => 0,
            'total_revenue' => 0,
            'total_bookings' => 0,
            'total_events' => 0,
            'event_participants' => 0,
            'active_subscriptions' => 0,
            'chat_messages' => 0,
        ];
    }
}
