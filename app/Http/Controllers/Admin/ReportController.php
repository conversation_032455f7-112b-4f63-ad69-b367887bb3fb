<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserReport;
use App\Models\User;
use App\Models\Notification;
use App\Models\ReportDecisionHistory;
use App\Services\FirebaseService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }

    /**
     * Display a listing of user reports
     */
    public function index(Request $request): View
    {
        // Get report statistics
        $stats = [
            'total_reports' => UserReport::count(),
            'pending_reports' => UserReport::where('status', UserReport::STATUS_PENDING)->count(),
            'under_review_reports' => UserReport::where('status', UserReport::STATUS_UNDER_REVIEW)->count(),
            'resolved_reports' => UserReport::where('status', UserReport::STATUS_RESOLVED)->count(),
            'dismissed_reports' => UserReport::where('status', UserReport::STATUS_DISMISSED)->count(),
            'reports_today' => UserReport::whereDate('created_at', today())->count(),
            'reports_this_week' => UserReport::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'reports_this_month' => UserReport::whereMonth('created_at', now()->month)->count(),
        ];

        return view('admin.reports.index', compact('stats'));
    }

    /**
     * Get reports data for DataTables
     */
    public function getData(Request $request): JsonResponse
    {
        try {
            \Log::info('Admin reports data requested', [
                'user_id' => auth()->id(),
                'is_admin' => auth()->user() ? auth()->user()->isAdmin() : false,
                'request_data' => $request->all()
            ]);

            // Get all reports with relationships
            $reports = UserReport::with(['reporter', 'reportedUser', 'reviewer'])->get();

            // Apply filters manually
            if ($request->filled('status')) {
                $reports = $reports->where('status', $request->status);
            }

            if ($request->filled('category')) {
                $reports = $reports->where('category', $request->category);
            }

            if ($request->filled('search')) {
                $search = strtolower($request->search);
                $reports = $reports->filter(function($report) use ($search) {
                    return str_contains(strtolower($report->reporter?->name ?? ''), $search) ||
                           str_contains(strtolower($report->reportedUser?->name ?? ''), $search) ||
                           str_contains(strtolower($report->description ?? ''), $search);
                });
            }

            // Format data for DataTables
            $data = [];
            foreach($reports as $report) {
                $data[] = [
                    'id' => $report->id,
                    'reporter' => $report->reporter ? $report->reporter->name : 'Unknown',
                    'reported_user' => $report->reportedUser ? $report->reportedUser->name : 'Unknown',
                    'category' => $report->getCategoryDisplayName(),
                    'status' => $this->getStatusBadge($report->status),
                    'created_at' => $report->created_at->format('M d, Y H:i'),
                    'actions' => $this->getActionButtons($report)
                ];
            }

            return response()->json([
                'draw' => intval($request->get('draw', 1)),
                'recordsTotal' => UserReport::count(),
                'recordsFiltered' => count($data),
                'data' => $data
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching reports data: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Failed to fetch reports data',
                'message' => $e->getMessage(),
                'draw' => $request->get('draw', 1),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ], 500);
        }
    }

    private function getStatusBadge($status): string
    {
        $badgeClass = 'bg-secondary';

        switch($status) {
            case 'pending':
                $badgeClass = 'bg-warning';
                break;
            case 'under_review':
                $badgeClass = 'bg-info';
                break;
            case 'resolved':
                $badgeClass = 'bg-success';
                break;
            case 'dismissed':
                $badgeClass = 'bg-secondary';
                break;
        }

        $displayStatus = ucfirst(str_replace('_', ' ', $status));
        return '<span class="badge ' . $badgeClass . '">' . $displayStatus . '</span>';
    }

    private function getActionButtons($report): string
    {
        $actions = '<div class="btn-group" role="group">';
        $actions .= '<button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReport(' . $report->id . ')" title="View Details">';
        $actions .= '<i class="bi bi-eye"></i>';
        $actions .= '</button>';

        if ($report->status === 'pending' || $report->status === 'under_review') {
            $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="suspendUser(' . $report->id . ')" title="Suspend User">';
            $actions .= '<i class="bi bi-person-x"></i>';
            $actions .= '</button>';

            $actions .= '<button type="button" class="btn btn-sm btn-outline-warning" onclick="sendWarning(' . $report->id . ')" title="Send Warning">';
            $actions .= '<i class="bi bi-exclamation-triangle"></i>';
            $actions .= '</button>';

            $actions .= '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="dismissReport(' . $report->id . ')" title="Dismiss Report">';
            $actions .= '<i class="bi bi-x-circle"></i>';
            $actions .= '</button>';
        }

        $actions .= '</div>';
        return $actions;
    }

    /**
     * Show the specified report
     */
    public function show(UserReport $report): View
    {
        $report->load(['reporter', 'reportedUser', 'reviewer', 'decisionHistory.admin']);
        return view('admin.reports.show', compact('report'));
    }

    /**
     * Suspend a user
     */
    public function suspendUser(Request $request, UserReport $report): JsonResponse
    {
        \Log::info('Suspend user request received', [
            'report_id' => $report->id,
            'request_data' => $request->all(),
            'user_id' => auth()->id()
        ]);

        $request->validate([
            'duration' => 'required|in:24h,7d,30d,permanent',
            'reason' => 'required|string|max:500',
        ]);

        try {
            // Admin middleware already ensures user is admin
            DB::beginTransaction();

            $user = $report->reportedUser;
            $duration = $request->duration;
            $reason = $request->reason;

            // Calculate suspension end date
            $suspensionEndDate = null;
            if ($duration !== 'permanent') {
                $suspensionEndDate = match($duration) {
                    '24h' => now()->addHours(24),
                    '7d' => now()->addDays(7),
                    '30d' => now()->addDays(30),
                };
            }

            // Update user suspension status
            $user->update([
                'is_suspended' => true,
                'suspension_end_date' => $suspensionEndDate,
                'suspension_reason' => $reason,
            ]);

            // Mark report as resolved
            $report->markAsResolved(Auth::user(), "User suspended for {$duration}. Reason: {$reason}");

            // Log the suspension in decision history
            ReportDecisionHistory::logSuspension(
                $report,
                Auth::user(),
                $duration,
                $reason,
                false // isLifted = false
            );

            // Create notification for user
            $notification = Notification::create([
                'user_id' => $user->id,
                'type' => 'account_suspension',
                'title' => 'Account Suspended',
                'message' => "Your account has been suspended. Reason: {$reason}",
                'body' => "Your account has been suspended. Reason: {$reason}",
                'data' => [
                    'suspension_type' => $duration,
                    'reason' => $reason,
                    'report_id' => $report->id,
                ],
            ]);

            // Send Firebase notification
            $this->firebaseService->sendNotification($notification);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'User has been suspended successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error suspending user: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while suspending the user.'
            ], 500);
        }
    }

    /**
     * Send warning to user
     */
    public function sendWarning(Request $request, UserReport $report): JsonResponse
    {
        $request->validate([
            'warning_message' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $user = $report->reportedUser;
            $warningMessage = $request->warning_message;

            // Mark report as resolved
            $report->markAsResolved(Auth::user(), "Warning sent to user. Message: {$warningMessage}");

            // Log the warning in decision history
            ReportDecisionHistory::logWarning(
                $report,
                Auth::user(),
                $warningMessage
            );

            // Create notification for user
            $notification = Notification::create([
                'user_id' => $user->id,
                'type' => 'account_warning',
                'title' => 'Account Warning',
                'message' => $warningMessage,
                'body' => $warningMessage,
                'data' => [
                    'warning_type' => 'behavior_warning',
                    'report_id' => $report->id,
                ],
            ]);

            // Send Firebase notification
            $this->firebaseService->sendNotification($notification);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Warning has been sent to the user.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error sending warning: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the warning.'
            ], 500);
        }
    }

    /**
     * Dismiss a report
     */
    public function dismissReport(Request $request, UserReport $report): JsonResponse
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $adminNotes = $request->admin_notes ?? 'Report dismissed by admin.';
            $previousStatus = $report->status;
            $report->markAsDismissed(Auth::user(), $adminNotes);

            // Log the dismissal in decision history
            ReportDecisionHistory::logStatusChange(
                $report,
                Auth::user(),
                $previousStatus,
                'dismissed',
                $adminNotes
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Report has been dismissed.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error dismissing report: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while dismissing the report.'
            ], 500);
        }
    }

    /**
     * Update report status
     */
    public function updateStatus(Request $request, UserReport $report): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:' . implode(',', array_keys(UserReport::getStatuses())),
            'admin_notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $previousStatus = $report->status;
            $newStatus = $request->status;
            $adminNotes = $request->admin_notes ?? $report->admin_notes;

            $report->update([
                'status' => $newStatus,
                'admin_notes' => $adminNotes,
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
            ]);

            // Log the status update in decision history if status changed
            if ($previousStatus !== $newStatus) {
                ReportDecisionHistory::logStatusChange(
                    $report,
                    Auth::user(),
                    $previousStatus,
                    $newStatus,
                    $adminNotes
                );
            } elseif ($adminNotes !== $report->getOriginal('admin_notes')) {
                // Log admin notes update if only notes changed
                ReportDecisionHistory::create([
                    'report_id' => $report->id,
                    'admin_id' => Auth::id(),
                    'action_type' => ReportDecisionHistory::ACTION_ADMIN_NOTES_UPDATED,
                    'admin_notes' => $adminNotes,
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Report status has been updated.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating report status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the report status.'
            ], 500);
        }
    }

    /**
     * Change report status with audit logging
     */
    public function changeStatus(Request $request, UserReport $report): JsonResponse
    {
        // Check authorization
        $this->authorize('changeStatus', $report);

        $request->validate([
            'new_status' => 'required|in:' . implode(',', array_keys(UserReport::getStatuses())),
            'reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $previousStatus = $report->status;
            $newStatus = $request->new_status;
            $reason = $request->reason;

            // Update the report status
            $report->update([
                'status' => $newStatus,
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
            ]);

            // Log the status change in decision history
            ReportDecisionHistory::logStatusChange(
                $report,
                Auth::user(),
                $previousStatus,
                $newStatus,
                $reason
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Report status has been changed from {$previousStatus} to {$newStatus}."
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error changing report status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while changing the report status.'
            ], 500);
        }
    }

    /**
     * Lift user suspension
     */
    public function liftSuspension(Request $request, UserReport $report): JsonResponse
    {
        // Check authorization
        $this->authorize('liftSuspension', $report);

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $user = $report->reportedUser;
            if (!$user || !$user->is_suspended) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not currently suspended.'
                ], 400);
            }

            $reason = $request->reason;

            // Lift the suspension
            $user->update([
                'is_suspended' => false,
                'suspension_end_date' => null,
                'suspension_reason' => null,
            ]);

            // Log the suspension lift in decision history
            ReportDecisionHistory::logSuspension(
                $report,
                Auth::user(),
                'lifted',
                $reason,
                true // isLifted = true
            );

            // Send notification to user about suspension lift
            $this->sendSuspensionLiftNotification($user, $reason);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'User suspension has been lifted successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error lifting suspension: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while lifting the suspension.'
            ], 500);
        }
    }

    /**
     * Reverse a decision completely
     */
    public function reverseDecision(Request $request, UserReport $report): JsonResponse
    {
        // Check authorization
        $this->authorize('reverseDecision', $report);

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $reason = $request->reason;
            $user = $report->reportedUser;

            // If user is suspended due to this report, lift the suspension
            if ($user && $user->is_suspended) {
                $user->update([
                    'is_suspended' => false,
                    'suspension_end_date' => null,
                    'suspension_reason' => null,
                ]);

                // Send notification about suspension lift
                $this->sendSuspensionLiftNotification($user, 'Decision reversed - suspension lifted');
            }

            // Change report status to dismissed
            $previousStatus = $report->status;
            $report->update([
                'status' => UserReport::STATUS_DISMISSED,
                'admin_notes' => "Decision reversed: {$reason}",
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
            ]);

            // Log the decision reversal
            ReportDecisionHistory::logDecisionReversal(
                $report,
                Auth::user(),
                $reason,
                [
                    'previous_status' => $previousStatus,
                    'suspension_lifted' => $user && $user->wasChanged('is_suspended'),
                    'action_type' => 'full_reversal'
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Decision has been reversed successfully. Report dismissed and any related actions have been undone.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error reversing decision: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while reversing the decision.'
            ], 500);
        }
    }

    /**
     * Send notification to user about suspension lift
     */
    private function sendSuspensionLiftNotification(User $user, string $reason): void
    {
        try {
            if ($user->fcm_token) {
                $notificationData = [
                    'title' => 'Account Status Update',
                    'body' => 'Your account suspension has been lifted. You can now use the platform normally.',
                    'type' => 'suspension_lifted',
                    'data' => [
                        'reason' => $reason,
                        'lifted_at' => now()->toISOString(),
                    ]
                ];

                // Send Firebase notification
                $this->sendFirebaseNotification($user->fcm_token, $notificationData);
            }
        } catch (\Exception $e) {
            Log::error('Error sending suspension lift notification: ' . $e->getMessage());
        }
    }
}
