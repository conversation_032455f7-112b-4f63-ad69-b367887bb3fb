<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Setting;
use App\Models\User;
use App\Services\ComprehensiveNotificationService;
use App\Services\FirebaseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    private ComprehensiveNotificationService $notificationService;
    private FirebaseService $firebaseService;

    public function __construct(ComprehensiveNotificationService $notificationService, FirebaseService $firebaseService)
    {
        $this->notificationService = $notificationService;
        $this->firebaseService = $firebaseService;
    }


    /**
     * Display a listing of the notifications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $notifications = Notification::with('user')->latest()->paginate(20);
        $users = User::where('role', 'user')->where('is_admin', false)->get();

        // Check if this is an AJAX request
        if (request()->ajax()) {
            $html = view('admin.notifications.index', compact('notifications', 'users'))->render();
            return $html;
        }

        return view('admin.notifications.index', compact('notifications', 'users'));
    }

    /**
     * Store a newly created notification in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|in:general,payment,match,partner_swapping',
            'user_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle sending to all users
        if ($request->user_id === 'all') {
            $users = User::where('role', 'user')->where('is_admin', false)->get();
            $notificationCount = $users->count();

            // Create a master notification record for tracking
            $masterNotification = Notification::create([
                'user_id' => auth()->id(), // Use admin's ID for the master record
                'title' => $request->title,
                'body' => $request->body,
                'type' => $request->type,
                'data' => $request->data ?? null,
                'sent_to_all' => true,
                'total_recipients' => $notificationCount,
                'sent_at' => now(),
            ]);

            // Create individual notifications for each user
            foreach ($users as $user) {
                $notification = Notification::create([
                    'user_id' => $user->id,
                    'title' => $request->title,
                    'body' => $request->body,
                    'type' => $request->type,
                    'data' => $request->data ?? null,
                    'sent_at' => now(),
                ]);

                // Send push notification using the new service
                if ($user->fcm_token) {
                    $this->firebaseService->sendNotification($notification);
                }
            }

            return redirect()->route('admin.notifications.index')
                ->with('success', "Notification sent to {$notificationCount} users successfully.");
        }

        // Validate that user_id exists if not 'all'
        $user = User::find($request->user_id);
        if (!$user) {
            return redirect()->back()
                ->withErrors(['user_id' => 'Selected user not found.'])
                ->withInput();
        }

        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'body' => $request->body,
            'type' => $request->type,
            'data' => $request->data ?? null,
        ]);

        // Send push notification using the new service
        if ($user->fcm_token) {
            $this->firebaseService->sendNotification($notification);
        }

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification sent successfully.');
    }

    /**
     * Remove the specified notification from storage.
     *
     * @param  \App\Models\Notification  $notification
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Notification $notification)
    {
        $notification->delete();

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification deleted successfully.');
    }

    /**
     * Test Firebase configuration
     */
    public function testFirebase(Request $request)
    {
        $result = $this->firebaseService->testConfiguration();

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Firebase configuration is working correctly!'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Firebase configuration error: ' . $result['error']
            ], 400);
        }
    }

    /**
     * Get notification delivery statistics
     */
    public function getStatistics(Request $request)
    {
        $stats = $this->notificationService->getStatistics();

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get notifications data for AJAX requests.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotificationsData(Request $request)
    {
        $query = Notification::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('body', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Type filter
        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $allowedSortFields = ['title', 'type', 'sent_at', 'created_at'];

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $notifications = $query->latest()->paginate(20);

        $html = view('admin.notifications.table', compact('notifications'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'total' => $notifications->total()
            ]
        ]);
    }

    /**
     * Store a notification via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeAjax(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|in:general,payment,match,partner_swapping',
            'user_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Handle sending to all users
        if ($request->user_id === 'all') {
            $users = User::where('role', 'user')->where('is_admin', false)->get();
            $notificationCount = $users->count();

            // Create a master notification record for tracking
            $masterNotification = Notification::create([
                'user_id' => auth()->id(), // Use admin's ID for the master record
                'title' => $request->title,
                'body' => $request->body,
                'type' => $request->type,
                'data' => $request->data ?? null,
                'sent_to_all' => true,
                'total_recipients' => $notificationCount,
                'sent_at' => now(),
            ]);

            // Create individual notifications for each user
            foreach ($users as $user) {
                $notification = Notification::create([
                    'user_id' => $user->id,
                    'title' => $request->title,
                    'body' => $request->body,
                    'type' => $request->type,
                    'data' => $request->data ?? null,
                    'sent_at' => now(),
                ]);

                // Send push notification using the new service
                if ($user->fcm_token) {
                    $this->firebaseService->sendNotification($notification);
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Notification sent to {$notificationCount} users successfully."
            ]);
        }

        // Validate that user_id exists if not 'all'
        $user = User::find($request->user_id);
        if (!$user) {
            return response()->json([
                'success' => false,
                'errors' => ['user_id' => ['Selected user not found.']]
            ], 422);
        }

        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'body' => $request->body,
            'type' => $request->type,
            'data' => $request->data ?? null,
        ]);

        // Send push notification using the new service
        if ($user->fcm_token) {
            $this->firebaseService->sendNotification($notification);
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully.'
        ]);
    }

    /**
     * Delete a notification via AJAX.
     *
     * @param  \App\Models\Notification  $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyAjax(Notification $notification)
    {
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully.'
        ]);
    }

    /**
     * Search users for notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchUsers(Request $request)
    {
        $query = $request->get('q', '');

        $users = User::where('role', 'user')
            ->where('is_admin', false)
            ->where(function ($q) use ($query) {
                if (!empty($query)) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('email', 'like', "%{$query}%");
                }
            })
            ->select('id', 'name', 'email')
            ->limit(20)
            ->get();

        return response()->json([
            'success' => true,
            'users' => $users
        ]);
    }

    /**
     * Get a specific user.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUser(User $user)
    {
        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ]
        ]);
    }
}
