<?php

namespace App\Http\Controllers;

use App\Services\NotificationPermissionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationPermissionController extends Controller
{
    private NotificationPermissionService $permissionService;

    public function __construct(NotificationPermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Check if permission can be requested
     */
    public function canRequest(): JsonResponse
    {
        return response()->json([
            'can_request' => $this->permissionService->canRequestPermission(),
            'should_request_on_login' => $this->permissionService->shouldRequestOnLogin(),
            'time_until_next' => $this->permissionService->getTimeUntilNextRequest(),
            'is_first_login' => $this->permissionService->isFirstLoginSession(),
        ]);
    }

    /**
     * Record that permission was requested
     */
    public function recordRequest(): JsonResponse
    {
        $this->permissionService->recordPermissionRequest();
        
        return response()->json([
            'success' => true,
            'message' => 'Permission request recorded'
        ]);
    }

    /**
     * Record permission result (granted or denied)
     */
    public function recordResult(Request $request): JsonResponse
    {
        $request->validate([
            'granted' => 'required|boolean'
        ]);

        if ($request->granted) {
            $this->permissionService->recordPermissionGranted();
        } else {
            $this->permissionService->recordPermissionDenied();
        }

        return response()->json([
            'success' => true,
            'message' => 'Permission result recorded'
        ]);
    }

    /**
     * Mark user as having logged in before
     */
    public function markLoggedInBefore(): JsonResponse
    {
        $this->permissionService->markUserAsLoggedInBefore();
        
        return response()->json([
            'success' => true,
            'message' => 'User marked as logged in before'
        ]);
    }

    /**
     * Get permission status (for debugging)
     */
    public function getStatus(): JsonResponse
    {
        return response()->json([
            'status' => $this->permissionService->getPermissionStatus()
        ]);
    }

    /**
     * Clear tracking data (for testing)
     */
    public function clearTracking(): JsonResponse
    {
        $this->permissionService->clearTrackingData();
        
        return response()->json([
            'success' => true,
            'message' => 'Tracking data cleared'
        ]);
    }
}
