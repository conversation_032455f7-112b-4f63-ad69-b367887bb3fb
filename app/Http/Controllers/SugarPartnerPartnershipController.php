<?php

namespace App\Http\Controllers;

use App\Models\SugarPartnerPartnership;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SugarPartnerPartnershipController extends Controller
{
    /**
     * Display a listing of user's partnerships.
     */
    public function index()
    {
        $user = Auth::user();
        
        $partnerships = $user->activeSugarPartnerPartnerships()
            ->with(['user1', 'user2', 'exchange'])
            ->orderBy('started_at', 'desc')
            ->get();

        return view('sugar-partner.partnerships.index', compact('partnerships'));
    }

    /**
     * End a partnership.
     */
    public function destroy(SugarPartnerPartnership $partnership, Request $request)
    {
        $user = Auth::user();

        // Check if user is part of this partnership
        if ($partnership->user1_id !== $user->id && $partnership->user2_id !== $user->id) {
            abort(403, 'You are not authorized to end this partnership.');
        }

        // Check if partnership is active
        if (!$partnership->isActive()) {
            return redirect()->back()->with('error', 'This partnership is already ended.');
        }

        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        DB::beginTransaction();
        try {
            $partner = $partnership->getOtherUser($user->id);
            
            // End the partnership
            $partnership->endPartnership($user->id, $request->reason);

            // Check if user has any other active partnerships
            $otherActivePartnerships = $user->activeSugarPartnerPartnerships()
                ->where('id', '!=', $partnership->id)
                ->count();

            // If no other active partnerships, user can set new preferences
            // (We don't automatically restore preferences, user can set them manually)

            // Send notification to the partner
            Notification::create([
                'user_id' => $partner->id,
                'type' => 'sugar_partner_partnership_ended',
                'title' => 'Sugar Partner Partnership Ended',
                'message' => 'Partnership Ended',
                'body' => "{$user->name} has ended your Sugar Partner partnership." . 
                         ($request->reason ? " Reason: {$request->reason}" : ""),
                'data' => [
                    'ended_by_user_id' => $user->id,
                    'ended_by_user_name' => $user->name,
                    'partnership_id' => $partnership->id,
                    'reason' => $request->reason,
                    'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                    'action_text' => 'View Sugar Partner'
                ],
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Partnership ended successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Failed to end partnership. Please try again.');
        }
    }

    /**
     * Show partnership details.
     */
    public function show(SugarPartnerPartnership $partnership)
    {
        $user = Auth::user();

        // Check if user is part of this partnership
        if ($partnership->user1_id !== $user->id && $partnership->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this partnership.');
        }

        $partner = $partnership->getOtherUser($user->id);

        return view('sugar-partner.partnerships.show', compact('partnership', 'partner'));
    }
}
