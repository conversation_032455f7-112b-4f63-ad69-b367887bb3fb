<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use App\Models\Setting;
use App\Models\UserWallet;
use App\Models\BlockedUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ProviderBookingController extends Controller
{
    /**
     * Get provider's booking requests.
     */
    public function getBookingRequests(): JsonResponse
    {
        $provider = Auth::user();

        // First, auto-cancel any expired booking requests
        $this->autoRejectExpiredBookingRequests($provider->id);

        $bookings = TimeSpendingBooking::with(['client'])
            ->where('provider_id', $provider->id)
            ->where('payment_status', 'paid')
            ->where('provider_status', 'pending')
            ->whereIn('status', ['confirmed', 'pending'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->filter(function($booking) {
                // Only show bookings that haven't passed their meeting time
                return !$booking->isPastMeetingTime();
            })
            ->values(); // Reset array keys after filtering

        return response()->json([
            'success' => true,
            'bookings' => $bookings
        ])->header('Cache-Control', 'no-cache, no-store, must-revalidate')
          ->header('Pragma', 'no-cache')
          ->header('Expires', '0');
    }

    /**
     * Accept a booking request.
     */
    public function acceptBooking(Request $request, $booking): JsonResponse
    {
        $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($booking);
        $provider = Auth::user();

        // Check if the booking belongs to the authenticated provider
        if ($booking->provider_id !== $provider->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking is already responded to
        if ($booking->provider_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been responded to.'
            ], 400);
        }

        // Check if booking is paid
        if ($booking->payment_status !== 'paid') {
            return response()->json([
                'success' => false,
                'message' => 'This booking is not paid yet.'
            ], 400);
        }

        // Check if booking is already refunded
        if ($booking->refund_status === 'processed') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been refunded and cannot be accepted.'
            ], 400);
        }

        // Check if meeting start time has passed and auto-cancel if needed
        if ($booking->booking_date <= now()) {
            // Auto-cancel the booking immediately
            if ($booking->shouldBeAutoCancelled()) {
                $booking->processAutoCancellation();
            }

            return response()->json([
                'success' => false,
                'message' => 'Cannot accept booking after the meeting start time has passed. The booking has been automatically cancelled and the client has been refunded.'
            ], 400);
        }

        try {
            // Accept the booking
            $booking->update([
                'provider_status' => 'accepted',
                'provider_responded_at' => now(),
                'chat_enabled' => true,
            ]);

            // Hold payment in escrow instead of immediate release
            $booking->holdInEscrow();

            // Send notification to client about acceptance
            Notification::createBookingAccepted($booking->client_id, $booking);

            // Note: Payment received notification will be sent after meeting completion
            // Payment is held in escrow until meeting is verified and completed

            // Get provider's wallet for balance response
            $providerWallet = UserWallet::getOrCreate($provider->id);

            // Auto-reject competing bookings for the same time slot (with error handling)
            $rejectedCount = 0;
            try {
                $rejectedCount = TimeSpendingBooking::autoRejectCompetingBookings($booking->id);
            } catch (\Exception $e) {
                \Log::error('Auto-reject competing bookings failed (non-critical):', [
                    'booking_id' => $booking->id,
                    'provider_id' => $provider->id,
                    'error' => $e->getMessage()
                ]);
                // Continue execution even if auto-reject fails
            }

            $message = 'Booking accepted successfully! You can now chat with the client.';
            if ($rejectedCount > 0) {
                $message .= " {$rejectedCount} competing booking(s) were automatically rejected.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'booking' => $booking->fresh(),
                'wallet_balance' => $providerWallet->fresh()->balance,
                'rejected_count' => $rejectedCount
            ]);

        } catch (\Exception $e) {
            \Log::error('Booking acceptance failed:', [
                'booking_id' => $booking->id,
                'provider_id' => $provider->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to accept booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Reject a booking request.
     */
    public function rejectBooking(Request $request, $booking): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($booking);
        $provider = Auth::user();

        // Check if the booking belongs to the authenticated provider
        if ($booking->provider_id !== $provider->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking is already responded to
        if ($booking->provider_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been responded to.'
            ], 400);
        }

        // Check if booking is already refunded
        if ($booking->refund_status === 'processed') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been refunded and cannot be rejected.'
            ], 400);
        }

        // Check if meeting start time has passed and auto-cancel if needed
        if ($booking->booking_date <= now()) {
            // Auto-cancel the booking immediately
            if ($booking->shouldBeAutoCancelled()) {
                $booking->processAutoCancellation();
            }

            return response()->json([
                'success' => false,
                'message' => 'Cannot reject booking after the meeting start time has passed. The booking has been automatically cancelled and the client has been refunded.'
            ], 400);
        }

        try {
            // Reject the booking
            $booking->update([
                'provider_status' => 'rejected',
                'rejection_reason' => $request->reason,
                'provider_responded_at' => now(),
                'status' => 'cancelled',
                'cancellation_reason' => 'Rejected by provider: ' . $request->reason,
            ]);

            // Process refund for rejected booking
            $booking->processRefund($request->reason);

            // Send notification to client about rejection
            Notification::createBookingRejected($booking->client_id, $booking, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Booking rejected successfully. The client has been notified.',
                'booking' => $booking->fresh()
            ]);

        } catch (\Exception $e) {
            \Log::error('Booking rejection failed:', [
                'booking_id' => $booking->id,
                'provider_id' => $provider->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reject booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Block a client.
     */
    public function blockClient(Request $request, $booking): JsonResponse
    {
        $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($booking);
        $provider = Auth::user();

        // Check if the booking belongs to the authenticated provider
        if ($booking->provider_id !== $provider->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking is already refunded
        if ($booking->refund_status === 'processed') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been refunded and cannot be blocked.'
            ], 400);
        }

        // Check if meeting start time has passed and auto-cancel if needed
        if ($booking->booking_date <= now()) {
            // Auto-cancel the booking immediately
            if ($booking->shouldBeAutoCancelled()) {
                $booking->processAutoCancellation();
            }

            return response()->json([
                'success' => false,
                'message' => 'Cannot block client after the meeting start time has passed. The booking has been automatically cancelled and the client has been refunded.'
            ], 400);
        }

        try {
            // Reject the booking and block the client
            $booking->update([
                'provider_status' => 'rejected',
                'rejection_reason' => 'Client blocked by provider',
                'provider_responded_at' => now(),
                'status' => 'cancelled',
                'cancellation_reason' => 'Client blocked by provider',
            ]);

            // Block the client
            BlockedUser::blockUser(
                $provider->id,
                $booking->client_id,
                'Blocked from booking interface',
                $booking->id
            );

            // Process refund for blocked booking
            $booking->processRefund('Client blocked by provider');

            // Auto-reject and remove all future bookings from this client
            $rejectedCount = BlockedUser::handleUserBlocked($provider->id, $booking->client_id);

            // Send notification to client about rejection
            Notification::createBookingRejected($booking->client_id, $booking, 'Your booking request was declined.');

            $message = 'Client blocked successfully. They will not be able to book you again.';
            if ($rejectedCount > 0) {
                $message .= " {$rejectedCount} future booking(s) were automatically rejected.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'booking' => $booking->fresh(),
                'rejected_count' => $rejectedCount
            ]);

        } catch (\Exception $e) {
            \Log::error('Client blocking failed:', [
                'booking_id' => $booking->id,
                'provider_id' => $provider->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to block client. Please try again.'
            ], 500);
        }
    }

    /**
     * Unblock a client.
     */
    public function unblockClient(Request $request, $user): JsonResponse
    {
        $provider = Auth::user();

        try {
            // Check if the user is actually blocked
            if (!BlockedUser::isBlocked($provider->id, $user)) {
                return response()->json([
                    'success' => false,
                    'message' => 'This user is not blocked.'
                ], 400);
            }

            // Unblock the user
            $unblocked = BlockedUser::unblockUser($provider->id, $user);

            if ($unblocked) {
                return response()->json([
                    'success' => true,
                    'message' => 'User unblocked successfully. They can now book you again.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to unblock user.'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Client unblocking failed:', [
                'provider_id' => $provider->id,
                'user_id' => $user,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to unblock user. Please try again.'
            ], 500);
        }
    }

    /**
     * Auto-reject expired booking requests for a provider.
     * This handles bookings where the meeting time has completely passed.
     */
    private function autoRejectExpiredBookingRequests($providerId): int
    {
        try {
            // Find booking requests that have passed their meeting end time
            $expiredBookings = TimeSpendingBooking::with(['client'])
                ->where('provider_id', $providerId)
                ->where('payment_status', 'paid')
                ->where('provider_status', 'pending')
                ->where('status', 'confirmed')
                ->get()
                ->filter(function($booking) {
                    // Check if the entire meeting time (start + duration) has passed
                    return $booking->isPastMeetingTime();
                });

            $rejectedCount = 0;

            foreach ($expiredBookings as $booking) {
                try {
                    // Auto-reject the expired booking
                    $booking->update([
                        'provider_status' => 'rejected',
                        'status' => 'auto_cancelled',
                        'provider_responded_at' => now(),
                        'rejection_reason' => 'Auto-rejected: Meeting time has passed',
                        'cancelled_at' => now(),
                        'cancellation_reason' => 'Auto-cancelled - Meeting time has passed without provider response',
                    ]);

                    // Process refund to client
                    $booking->processRefund('Meeting time passed - Auto-cancelled');

                    // Send custom notification to client about auto-rejection due to expired meeting time
                    \App\Models\Notification::create([
                        'user_id' => $booking->client_id,
                        'type' => 'booking_auto_rejected',
                        'title' => 'Booking Auto-Cancelled',
                        'message' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled because the meeting time has passed. You have been refunded ₹" . ($booking->base_amount ?? $booking->total_amount) . ".",
                        'body' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was automatically cancelled because the meeting time has passed. You have been refunded ₹" . ($booking->base_amount ?? $booking->total_amount) . ".",
                        'data' => [
                            'booking_id' => $booking->id,
                            'provider_id' => $booking->provider_id,
                            'provider_name' => $booking->provider->name ?? 'Provider',
                            'booking_date' => $booking->booking_date->format('M d, Y'),
                            'booking_time' => $booking->booking_date->format('h:i A'),
                            'refund_amount' => $booking->base_amount ?? $booking->total_amount,
                            'auto_cancelled' => true,
                            'reason' => 'meeting_time_passed',
                        ],
                    ]);

                    $rejectedCount++;

                    \Log::info('Auto-rejected expired booking request', [
                        'booking_id' => $booking->id,
                        'provider_id' => $providerId,
                        'client_id' => $booking->client_id,
                        'booking_date' => $booking->booking_date,
                        'refund_amount' => $booking->base_amount ?? $booking->total_amount
                    ]);

                } catch (\Exception $e) {
                    \Log::error('Failed to auto-reject expired booking', [
                        'booking_id' => $booking->id,
                        'provider_id' => $providerId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            if ($rejectedCount > 0) {
                \Log::info("Auto-rejected {$rejectedCount} expired booking requests for provider {$providerId}");
            }

            return $rejectedCount;

        } catch (\Exception $e) {
            \Log::error('Auto-reject expired bookings failed', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 0;
        }
    }
}
