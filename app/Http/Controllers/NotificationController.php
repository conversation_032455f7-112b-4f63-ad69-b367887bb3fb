<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\Setting;
use App\Models\User;
use App\Services\FirebaseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    private FirebaseService $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }
    /**
     * Display a listing of the user's notifications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();

        // Auto-cancel any expired bookings before showing notifications
        $this->autoCancelExpiredBookings($user);

        // Mark all notifications as read when user opens the notifications page
        $user->notifications()->where('is_read', false)->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        $notifications = $user->notifications()->latest()->paginate(10);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * Store a newly created notification in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|in:general,payment,match,partner_swapping',
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user = User::find($request->user_id);
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'body' => $request->body,
            'type' => $request->type,
            'data' => $request->data ?? null,
        ]);

        // Send push notification using the new service
        if ($user->fcm_token) {
            $this->firebaseService->sendNotification($notification);
        }

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification sent successfully.');
    }

    /**
     * Mark a notification as read.
     *
     * @param  \App\Models\Notification  $notification
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead(Notification $notification)
    {
        $notification->is_read = true;
        $notification->save();

        return redirect()->back()
            ->with('success', 'Notification marked as read.');
    }

    /**
     * Mark all notifications as read.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAllAsRead()
    {
        Auth::user()->notifications()->where('is_read', false)->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        return redirect()->back()
            ->with('success', 'All notifications marked as read.');
    }

    /**
     * Get unread notification count for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount()
    {
        // Debug logging
        \Log::info('NotificationController::getUnreadCount called', [
            'authenticated' => Auth::check(),
            'user_id' => Auth::id(),
            'request_headers' => [
                'Accept' => request()->header('Accept'),
                'X-Requested-With' => request()->header('X-Requested-With'),
                'X-CSRF-TOKEN' => request()->header('X-CSRF-TOKEN'),
            ],
            'url' => request()->url(),
            'method' => request()->method(),
        ]);

        if (!Auth::check()) {
            \Log::warning('NotificationController::getUnreadCount - User not authenticated');
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
                'count' => 0,
                'formatted_count' => '0',
                'has_unread' => false
            ], 401);
        }

        $user = Auth::user();
        $count = $user->getUnreadNotificationCount();
        $formattedCount = $user->getFormattedNotificationCount();

        \Log::info('NotificationController::getUnreadCount success', [
            'user_id' => $user->id,
            'count' => $count,
            'formatted_count' => $formattedCount,
        ]);

        return response()->json([
            'success' => true,
            'count' => $count,
            'formatted_count' => $formattedCount,
            'has_unread' => $count > 0
        ]);
    }

    /**
     * Auto-cancel expired bookings for the user when they view notifications.
     */
    private function autoCancelExpiredBookings($user): void
    {
        try {
            // Find bookings where the user is involved and time has passed
            $expiredBookings = \App\Models\TimeSpendingBooking::with(['client', 'provider'])
                ->where(function($query) use ($user) {
                    $query->where('client_id', $user->id)
                          ->orWhere('provider_id', $user->id);
                })
                ->where('provider_status', 'pending')
                ->where('payment_status', 'paid')
                ->whereIn('status', ['confirmed', 'pending'])
                ->where('booking_date', '<=', now())
                ->get();

            foreach ($expiredBookings as $booking) {
                try {
                    // Use the existing auto-cancellation logic
                    if ($booking->shouldBeAutoCancelled()) {
                        $booking->processAutoCancellation();
                    }
                } catch (\Exception $e) {
                    \Log::error('Auto-cancellation failed in notification view', [
                        'booking_id' => $booking->id,
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Auto-cancellation process failed in notification view', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
