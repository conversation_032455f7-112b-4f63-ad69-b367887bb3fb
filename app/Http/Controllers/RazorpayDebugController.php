<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Razorpay\Api\Api;

class RazorpayDebugController extends Controller
{
    /**
     * Debug Razorpay configuration and connectivity.
     */
    public function debug(): JsonResponse
    {
        try {
            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            $debugInfo = [
                'environment' => app()->environment(),
                'has_key_id' => !empty($razorpayKey),
                'has_key_secret' => !empty($razorpaySecret),
                'key_id_format' => $razorpayKey ? (str_starts_with($razorpayKey, 'rzp_test_') ? 'test' : (str_starts_with($razorpayKey, 'rzp_live_') ? 'live' : 'unknown')) : 'missing',
                'timestamp' => now()->toISOString(),
            ];

            if (empty($razorpayKey) || empty($razorpaySecret)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Razorpay credentials not configured',
                    'debug_info' => $debugInfo
                ], 400);
            }

            // Test API connectivity
            $api = new Api($razorpayKey, $razorpaySecret);

            // Create a minimal test order
            $orderData = [
                'receipt' => 'debug_test_' . time(),
                'amount' => 100, // ₹1 in paise
                'currency' => 'INR',
                'notes' => [
                    'debug' => 'connectivity_test',
                    'timestamp' => now()->toISOString()
                ]
            ];

            $order = $api->order->create($orderData);

            $debugInfo['api_test'] = 'success';
            $debugInfo['test_order_id'] = $order['id'];
            $debugInfo['test_order_status'] = $order['status'];

            return response()->json([
                'success' => true,
                'message' => 'Razorpay configuration is working correctly',
                'debug_info' => $debugInfo,
                'test_order' => $order
            ]);

        } catch (\Razorpay\Api\Errors\BadRequestError $e) {
            return response()->json([
                'success' => false,
                'message' => 'Razorpay Bad Request Error: ' . $e->getMessage(),
                'error_type' => 'bad_request',
                'debug_info' => $debugInfo ?? [],
                'error_details' => [
                    'code' => $e->getCode(),
                    'description' => $e->getDescription() ?? 'No description available'
                ]
            ], 400);

        } catch (\Razorpay\Api\Errors\Error $e) {
            return response()->json([
                'success' => false,
                'message' => 'Razorpay API Error: ' . $e->getMessage(),
                'error_type' => 'api_error',
                'debug_info' => $debugInfo ?? [],
                'error_details' => [
                    'code' => $e->getCode(),
                    'description' => $e->getDescription() ?? 'No description available'
                ]
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'General Error: ' . $e->getMessage(),
                'error_type' => 'general_error',
                'debug_info' => $debugInfo ?? [],
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => app()->environment('local') ? $e->getTraceAsString() : 'Hidden in production'
                ]
            ], 500);
        }
    }

    /**
     * Test order creation with specific parameters.
     */
    public function testOrderCreation(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'currency' => 'string|in:INR',
            'receipt' => 'string|max:40'
        ]);

        try {
            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            if (empty($razorpayKey) || empty($razorpaySecret)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Razorpay credentials not configured'
                ], 400);
            }

            $api = new Api($razorpayKey, $razorpaySecret);

            $orderData = [
                'receipt' => $request->receipt ?? 'test_order_' . time(),
                'amount' => (int) round($request->amount * 100), // Convert to paise
                'currency' => $request->currency ?? 'INR',
                'notes' => [
                    'test' => 'manual_order_creation',
                    'timestamp' => now()->toISOString(),
                    'user_agent' => $request->userAgent()
                ]
            ];

            $order = $api->order->create($orderData);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'order' => $order,
                'order_data_sent' => $orderData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage(),
                'error_details' => [
                    'type' => get_class($e),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 400);
        }
    }

    /**
     * Validate payment signature.
     */
    public function validateSignature(Request $request): JsonResponse
    {
        $request->validate([
            'razorpay_order_id' => 'required|string',
            'razorpay_payment_id' => 'required|string',
            'razorpay_signature' => 'required|string'
        ]);

        try {
            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            $api = new Api($razorpayKey, $razorpaySecret);

            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature
            ];

            $api->utility->verifyPaymentSignature($attributes);

            // Fetch payment details
            $payment = $api->payment->fetch($request->razorpay_payment_id);

            return response()->json([
                'success' => true,
                'message' => 'Payment signature is valid',
                'payment_details' => $payment
            ]);

        } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid payment signature',
                'error' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Signature validation failed: ' . $e->getMessage(),
                'error_details' => [
                    'type' => get_class($e),
                    'code' => $e->getCode()
                ]
            ], 500);
        }
    }
}
