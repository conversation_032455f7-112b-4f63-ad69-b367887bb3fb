<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ComprehensiveNotificationService;
use App\Models\NotificationPreference;
use App\Models\PushSubscription;
use App\Enums\NotificationCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    public function __construct(
        private ComprehensiveNotificationService $notificationService
    ) {}

    /**
     * Get notifications for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $perPage = $request->get('per_page', 20);
        
        $query = $user->notifications()->orderBy('created_at', 'desc');
        
        // Apply filters
        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }
        
        if ($request->filled('status')) {
            if ($request->get('status') === 'unread') {
                $query->whereNull('read_at');
            } elseif ($request->get('status') === 'read') {
                $query->whereNotNull('read_at');
            }
        }
        
        if ($request->filled('priority')) {
            $query->where('priority', $request->get('priority'));
        }
        
        $notifications = $query->paginate($perPage);
        $stats = $this->notificationService->getNotificationStats($user);
        
        // Add today's count to stats
        $stats['today'] = $user->notifications()
            ->whereDate('created_at', today())
            ->count();
        
        return response()->json([
            'notifications' => $notifications->items(),
            'has_more_pages' => $notifications->hasMorePages(),
            'current_page' => $notifications->currentPage(),
            'total' => $notifications->total(),
            'stats' => $stats,
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function unreadCount(Request $request): JsonResponse
    {
        $count = $this->notificationService->getUnreadCount($request->user());
        
        return response()->json(['count' => $count]);
    }

    /**
     * Mark a notification as read
     */
    public function markAsRead(Request $request, string $notificationId): JsonResponse
    {
        $success = $this->notificationService->markAsRead($notificationId, $request->user());
        
        return response()->json(['success' => $success]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $count = $this->notificationService->markAllAsRead($request->user());
        
        return response()->json(['marked_count' => $count]);
    }

    /**
     * Clear all notifications for user
     */
    public function clearAll(Request $request): JsonResponse
    {
        $user = $request->user();
        $count = $user->notifications()->delete();
        
        return response()->json(['cleared_count' => $count]);
    }

    /**
     * Get notifications by category
     */
    public function byCategory(Request $request, string $category): JsonResponse
    {
        try {
            $notificationCategory = NotificationCategory::from($category);
        } catch (\ValueError $e) {
            return response()->json(['error' => 'Invalid category'], 400);
        }
        
        $user = $request->user();
        $perPage = $request->get('per_page', 20);
        
        $notifications = $this->notificationService->getNotificationsByCategory(
            $user, 
            $notificationCategory, 
            $perPage
        );
        
        return response()->json([
            'notifications' => $notifications->items(),
            'has_more_pages' => $notifications->hasMorePages(),
            'current_page' => $notifications->currentPage(),
            'total' => $notifications->total(),
            'category' => $category,
        ]);
    }

    /**
     * Store push subscription
     */
    public function storePushSubscription(Request $request): JsonResponse
    {
        $request->validate([
            'endpoint' => 'required|string',
            'keys.p256dh' => 'required|string',
            'keys.auth' => 'required|string',
        ]);
        
        $subscription = PushSubscription::createOrUpdateForUser(
            $request->user(),
            $request->all()
        );
        
        return response()->json([
            'success' => true,
            'subscription_id' => $subscription->id,
        ]);
    }

    /**
     * Remove push subscription
     */
    public function removePushSubscription(Request $request): JsonResponse
    {
        $request->validate([
            'endpoint' => 'required|string',
        ]);
        
        $subscription = PushSubscription::where('user_id', $request->user()->id)
            ->where('endpoint', $request->get('endpoint'))
            ->first();
        
        if ($subscription) {
            $subscription->markInactive();
        }
        
        return response()->json(['success' => true]);
    }

    /**
     * Update notification preferences
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $request->validate([
            'categories' => 'required|array',
            'categories.*.push_enabled' => 'boolean',
            'categories.*.in_app_enabled' => 'boolean',
            'categories.*.sound_enabled' => 'boolean',
            'quiet_hours' => 'nullable|array',
            'quiet_hours.start' => 'nullable|string',
            'quiet_hours.end' => 'nullable|string',
            'days_enabled' => 'nullable|array',
            'days_enabled.*' => 'integer|between:1,7',
        ]);
        
        $categories = $request->get('categories', []);
        $quietHours = $request->get('quiet_hours');
        $daysEnabled = $request->get('days_enabled');
        
        foreach ($categories as $categoryKey => $preferences) {
            try {
                $category = NotificationCategory::from($categoryKey);
                
                NotificationPreference::updateForUserAndCategory(
                    $user,
                    $category,
                    [
                        'push_enabled' => $preferences['push_enabled'] ?? false,
                        'in_app_enabled' => $preferences['in_app_enabled'] ?? true,
                        'sound_enabled' => $preferences['sound_enabled'] ?? true,
                        'quiet_hours' => $quietHours,
                        'days_enabled' => $daysEnabled,
                    ]
                );
            } catch (\ValueError $e) {
                // Skip invalid categories
                continue;
            }
        }
        
        return response()->json(['success' => true]);
    }

    /**
     * Get notification preferences
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        $preferences = NotificationPreference::getAllForUser($user);
        
        $formattedPreferences = [];
        foreach ($preferences as $preference) {
            $formattedPreferences[$preference->category->value] = [
                'push_enabled' => $preference->push_enabled,
                'in_app_enabled' => $preference->in_app_enabled,
                'sound_enabled' => $preference->sound_enabled,
                'quiet_hours' => $preference->quiet_hours,
                'days_enabled' => $preference->days_enabled,
            ];
        }
        
        return response()->json(['preferences' => $formattedPreferences]);
    }

    /**
     * Test notification (for development/testing)
     */
    public function testNotification(Request $request): JsonResponse
    {
        if (!app()->environment('local', 'staging')) {
            return response()->json(['error' => 'Not available in production'], 403);
        }
        
        $request->validate([
            'type' => 'required|string',
            'title' => 'required|string',
            'message' => 'required|string',
        ]);
        
        try {
            $type = \App\Enums\NotificationType::from($request->get('type'));
            
            $notification = $this->notificationService->sendNotification(
                $request->user(),
                $type,
                $request->get('title'),
                $request->get('message'),
                $request->get('action_data', [])
            );
            
            return response()->json([
                'success' => true,
                'notification_id' => $notification?->id,
            ]);
        } catch (\ValueError $e) {
            return response()->json(['error' => 'Invalid notification type'], 400);
        }
    }

    /**
     * Get notification statistics
     */
    public function stats(Request $request): JsonResponse
    {
        $stats = $this->notificationService->getNotificationStats($request->user());
        
        return response()->json(['stats' => $stats]);
    }
}
