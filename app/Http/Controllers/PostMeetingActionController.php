<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use App\Models\MeetingVerification;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PostMeetingActionController extends Controller
{
    /**
     * Get post-meeting action status for a booking.
     */
    public function getActionStatus($bookingId): JsonResponse
    {
        try {
            $user = Auth::user();
            $booking = TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
                ->findOrFail($bookingId);

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking.'
                ], 403);
            }

            // Check if meeting should be auto-ended
            $shouldAutoEnd = $booking->shouldBeAutoEnded();
            $photoStatus = $booking->getPhotoVerificationStatus();
            $verification = $booking->meetingVerification;

            return response()->json([
                'success' => true,
                'booking_id' => $booking->id,
                'should_auto_end' => $shouldAutoEnd,
                'scheduled_end_time' => $booking->getScheduledEndTime()->toISOString(),
                'is_past_end_time' => $booking->isPastMeetingTime(),
                'photo_status' => $photoStatus,
                'user_photo_status' => $verification ? $verification->getUserPhotoStatus($user->id) : null,
                'action_type' => $photoStatus['action_type'],
                'can_rate' => $photoStatus['has_all_photos'],
                'can_report_no_show' => !$photoStatus['has_any_photos'] && $shouldAutoEnd,
                'can_end_meeting' => $photoStatus['has_any_photos'] && !$photoStatus['has_all_photos'] && $shouldAutoEnd,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get action status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Report no-show for a meeting.
     */
    public function reportNoShow(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_id' => 'required|exists:time_spending_bookings,id',
                'reason' => 'required|string|max:500',
                'no_show_type' => 'required|in:client_no_show,provider_no_show,both_no_show',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid input data.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($request->booking_id);

            // Check authorization
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking.'
                ], 403);
            }

            // Check if meeting can be reported as no-show
            if (!$booking->shouldBeAutoEnded() || $booking->hasAnyPhotos()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot report no-show for this meeting.'
                ], 400);
            }

            // Raise dispute for no-show
            $booking->raiseDispute(
                $user->id,
                $request->reason,
                'no_show',
                [
                    'no_show_type' => $request->no_show_type,
                    'reported_by' => $user->id,
                    'reported_at' => now()->toISOString(),
                ]
            );

            // Create notification for the other user
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;
            Notification::create([
                'user_id' => $otherUser->id,
                'type' => 'no_show_reported',
                'title' => 'No-Show Reported',
                'message' => "{$user->name} has reported a no-show for your meeting. The dispute is under review.",
                'body' => "{$user->name} has reported a no-show for your meeting. The dispute is under review.",
                'data' => [
                    'booking_id' => $booking->id,
                    'reported_by' => $user->id,
                    'no_show_type' => $request->no_show_type,
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'No-show report submitted successfully. The dispute is under review.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to report no-show: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manually end a meeting without full verification.
     */
    public function endMeeting(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_id' => 'required|exists:time_spending_bookings,id',
                'reason' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid input data.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $booking = TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
                ->findOrFail($request->booking_id);

            // Check authorization
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking.'
                ], 403);
            }

            // Check if meeting can be manually ended
            $photoStatus = $booking->getPhotoVerificationStatus();
            if (!$booking->shouldBeAutoEnded() || $photoStatus['has_all_photos']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot manually end this meeting.'
                ], 400);
            }

            // Update booking status to manually ended
            $booking->update([
                'status' => 'manually_ended',
                'admin_notes' => "Meeting manually ended by {$user->name}. Reason: " . ($request->reason ?: 'No reason provided'),
            ]);

            // Set auto-release timer for payment (reduced time since meeting was incomplete)
            if ($booking->escrow_status === 'held') {
                $booking->update([
                    'auto_release_at' => now()->addHours(12), // Shorter release time for incomplete meetings
                ]);
            }

            // Create notification for the other user
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;
            Notification::create([
                'user_id' => $otherUser->id,
                'type' => 'meeting_manually_ended',
                'title' => 'Meeting Ended',
                'message' => "{$user->name} has ended the meeting. Payment will be processed based on verification status.",
                'body' => "{$user->name} has ended the meeting. Payment will be processed based on verification status.",
                'data' => [
                    'booking_id' => $booking->id,
                    'ended_by' => $user->id,
                    'reason' => $request->reason,
                ],
            ]);

            // Send rating reminder if there were some photos uploaded
            if ($photoStatus['has_any_photos']) {
                Notification::createReviewReminder(
                    $booking->client_id,
                    $booking,
                    $booking->provider->name
                );

                Notification::createReviewReminder(
                    $booking->provider_id,
                    $booking,
                    $booking->client->name
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Meeting ended successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to end meeting: ' . $e->getMessage()
            ], 500);
        }
    }
}
