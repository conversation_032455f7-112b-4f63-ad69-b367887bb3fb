<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use Illuminate\Http\Request;
use Illuminate\View\View;

class FindPersonController extends Controller
{
    /**
     * Display the find person page with users who have Time Spending enabled.
     */
    public function index(Request $request): View
    {
        // Check if Time Spending feature is enabled by admin
        if (!Feature::isEnabled('time_spending')) {
            abort(404, 'Time Spending feature is not available.');
        }

        // Validate and get filter parameters
        $request->validate([
            'gender' => 'nullable|in:all,male,female',
            'min_price' => 'nullable|numeric|min:0|max:999999',
            'max_price' => 'nullable|numeric|min:0|max:999999',
            'location' => 'nullable|string|max:255',
            'availability_date' => 'nullable|date|after_or_equal:today',
            'availability_time' => 'nullable|date_format:H:i',
        ]);

        $gender = $request->get('gender', 'all');
        $minPrice = $request->get('min_price');
        $maxPrice = $request->get('max_price');
        $location = $request->get('location');
        $availabilityDate = $request->get('availability_date');
        $availabilityTime = $request->get('availability_time');

        // Ensure min_price is not greater than max_price
        if ($minPrice && $maxPrice && $minPrice > $maxPrice) {
            $temp = $minPrice;
            $minPrice = $maxPrice;
            $maxPrice = $temp;
        }

        // Build query for users with Time Spending enabled
        $query = User::where('is_time_spending_enabled', true)
            ->where('is_public_profile', true)
            ->where('show_gallery_images', true)
            ->whereNotNull('hourly_rate')
            ->where('hourly_rate', '>', 0)
            ->whereNotNull('service_location')
            ->with(['galleryImages' => function ($query) {
                $query->active()->ordered()->limit(1);
            }]);

        // Apply subscription model filtering
        if (Feature::isSubscriptionModelEnabled()) {
            $query->where('has_active_time_spending_subscription', true)
                  ->where('time_spending_subscription_expires_at', '>', now());
        }

        // Add is_suspended filter only if the column exists
        if (\Schema::hasColumn('users', 'is_suspended')) {
            $query->where('is_suspended', false);
        }

        // Apply gender filter
        if ($gender !== 'all') {
            $query->where('gender', $gender);
        }

        // Apply price range filters
        if ($minPrice !== null && is_numeric($minPrice)) {
            $query->where('hourly_rate', '>=', $minPrice);
        }

        if ($maxPrice !== null && is_numeric($maxPrice)) {
            $query->where('hourly_rate', '<=', $maxPrice);
        }

        // Apply location filter
        if ($location) {
            $query->where('service_location', 'LIKE', '%' . $location . '%');
        }

        // Apply availability filter if date and time are provided
        if ($availabilityDate && $availabilityTime) {
            try {
                $dateTimeString = $availabilityDate . ' ' . $availabilityTime;
                $dateTime = new \DateTime($dateTimeString);

                // Get all users first, then filter by availability and bookings
                $allUsers = $query->get();
                $availableUserIds = [];

                foreach ($allUsers as $user) {
                    try {
                        // First check if user has availability schedule for this time
                        if ($user->isAvailableAt($dateTime)) {
                            // Then check if they don't have conflicting bookings
                            $isTimeSlotAvailable = TimeSpendingBooking::isTimeSlotAvailable(
                                $user->id,
                                $dateTimeString,
                                1 // Check for 1 hour duration
                            );

                            if ($isTimeSlotAvailable) {
                                $availableUserIds[] = $user->id;
                            }
                        }
                    } catch (\Exception $e) {
                        // Skip this user if there's an error checking availability
                        \Log::warning('Error checking availability for user ' . $user->id . ': ' . $e->getMessage());
                        continue;
                    }
                }

                // Filter query to only include available users
                if (!empty($availableUserIds)) {
                    $query->whereIn('id', $availableUserIds);
                } else {
                    // No users available at this time, return empty result
                    $query->whereRaw('1 = 0'); // This will return no results
                }
            } catch (\Exception $e) {
                // If there's an error with date/time parsing, log it and continue without filtering
                \Log::warning('Error parsing availability date/time: ' . $e->getMessage());
            }
        }

        // Get users ordered by hourly rate (ascending)
        $users = $query->orderBy('hourly_rate', 'asc')
            ->paginate(12)
            ->withQueryString();

        // Get available locations for filter dropdown
        $availableLocationsQuery = User::where('is_time_spending_enabled', true)
            ->where('is_public_profile', true)
            ->where('show_gallery_images', true)
            ->whereNotNull('hourly_rate')
            ->where('hourly_rate', '>', 0)
            ->whereNotNull('service_location');

        // Apply subscription model filtering
        if (Feature::isSubscriptionModelEnabled()) {
            $availableLocationsQuery->where('has_active_time_spending_subscription', true)
                                   ->where('time_spending_subscription_expires_at', '>', now());
        }

        $availableLocations = $availableLocationsQuery->distinct()
            ->pluck('service_location')
            ->sort()
            ->values();

        // Get price range for filter options
        $priceRangeQuery = User::where('is_time_spending_enabled', true)
            ->where('is_public_profile', true)
            ->where('show_gallery_images', true)
            ->whereNotNull('hourly_rate')
            ->where('hourly_rate', '>', 0)
            ->whereNotNull('service_location');

        // Apply subscription model filtering
        if (Feature::isSubscriptionModelEnabled()) {
            $priceRangeQuery->where('has_active_time_spending_subscription', true)
                           ->where('time_spending_subscription_expires_at', '>', now());
        }

        // Add is_suspended filter only if the column exists
        if (\Schema::hasColumn('users', 'is_suspended')) {
            $priceRangeQuery->where('is_suspended', false);
        }

        $priceRange = $priceRangeQuery->selectRaw('MIN(hourly_rate) as min_price, MAX(hourly_rate) as max_price')
            ->first();

        // Get total count for display
        $totalCountQuery = User::where('is_time_spending_enabled', true)
            ->where('is_public_profile', true)
            ->where('show_gallery_images', true)
            ->whereNotNull('hourly_rate')
            ->where('hourly_rate', '>', 0)
            ->whereNotNull('service_location');

        // Apply subscription model filtering
        if (Feature::isSubscriptionModelEnabled()) {
            $totalCountQuery->where('has_active_time_spending_subscription', true)
                           ->where('time_spending_subscription_expires_at', '>', now());
        }

        // Add is_suspended filter only if the column exists
        if (\Schema::hasColumn('users', 'is_suspended')) {
            $totalCountQuery->where('is_suspended', false);
        }

        $totalCount = $totalCountQuery->count();

        return view('find-person', compact('users', 'gender', 'minPrice', 'maxPrice', 'location', 'availabilityDate', 'availabilityTime', 'availableLocations', 'priceRange', 'totalCount'));
    }

    /**
     * Display the user detail page.
     */
    public function show(User $user): View
    {
        // Check if Time Spending feature is enabled by admin
        if (!Feature::isEnabled('time_spending')) {
            abort(404, 'Time Spending feature is not available.');
        }

        // Check if the user has time spending enabled and is visible
        if (!$user->is_time_spending_enabled ||
            !$user->is_public_profile ||
            !$user->show_gallery_images ||
            !$user->hourly_rate ||
            $user->hourly_rate <= 0) {
            abort(404, 'User profile not found or not available.');
        }

        // Check subscription model requirements
        if (Feature::isSubscriptionModelEnabled() && !$user->hasActiveTimeSpendingSubscription()) {
            abort(404, 'User profile not found or not available.');
        }

        // Check if user is suspended (if column exists)
        if (\Schema::hasColumn('users', 'is_suspended') && $user->is_suspended) {
            abort(404, 'User profile not found or not available.');
        }

        // Validate booking update request if update_booking parameter is present
        if (request()->has('update_booking')) {
            $this->validateBookingUpdateRequest(request()->get('update_booking'), $user);
        }

        // Load user with gallery images
        $user->load(['galleryImages' => function ($query) {
            $query->active()->ordered();
        }]);

        // Get similar users based on interests (4 users)
        $similarUsers = collect();
        if ($user->interests) {
            $userInterests = array_map('trim', explode(',', $user->interests));

            $similarUsersQuery = User::where('is_time_spending_enabled', true)
                ->where('is_public_profile', true)
                ->where('show_gallery_images', true)
                ->whereNotNull('hourly_rate')
                ->where('hourly_rate', '>', 0)
                ->where('id', '!=', $user->id) // Exclude current user
                ->whereNotNull('interests')
                ->with(['galleryImages' => function ($query) {
                    $query->active()->ordered()->limit(1);
                }]);

            // Apply subscription model filtering
            if (Feature::isSubscriptionModelEnabled()) {
                $similarUsersQuery->where('has_active_time_spending_subscription', true)
                                 ->where('time_spending_subscription_expires_at', '>', now());
            }

            $similarUsers = $similarUsersQuery
                ->get()
                ->filter(function ($similarUser) use ($userInterests) {
                    if (empty($similarUser->interests)) {
                        return false;
                    }
                    $similarUserInterests = array_map('trim', explode(',', $similarUser->interests));
                    // Check if there's at least one common interest
                    return count(array_intersect($userInterests, $similarUserInterests)) > 0;
                })
                ->take(4);
        }

        // If we don't have enough similar users, fill with random users
        if ($similarUsers->count() < 4) {
            $excludeIds = $similarUsers->pluck('id')->push($user->id)->toArray();

            $randomUsersQuery = User::where('is_time_spending_enabled', true)
                ->where('is_public_profile', true)
                ->where('show_gallery_images', true)
                ->whereNotNull('hourly_rate')
                ->where('hourly_rate', '>', 0)
                ->whereNotIn('id', $excludeIds)
                ->with(['galleryImages' => function ($query) {
                    $query->active()->ordered()->limit(1);
                }]);

            // Apply subscription model filtering
            if (Feature::isSubscriptionModelEnabled()) {
                $randomUsersQuery->where('has_active_time_spending_subscription', true)
                                ->where('time_spending_subscription_expires_at', '>', now());
            }

            $randomUsers = $randomUsersQuery
                ->inRandomOrder()
                ->take(4 - $similarUsers->count())
                ->get();

            $similarUsers = $similarUsers->merge($randomUsers);
        }

        // Add is_suspended filter for similar users if column exists
        if (\Schema::hasColumn('users', 'is_suspended')) {
            $similarUsers = $similarUsers->filter(function ($similarUser) {
                return !$similarUser->is_suspended;
            });
        }

        // Check mutual rejection status (for Sugar Partner context)
        $mutualRejectionStatus = null;
        if (auth()->check() && \App\Models\Feature::isEnabled('sugar_partner')) {
            $mutualRejectionStatus = auth()->user()->getMutualRejectionStatus($user->id);
        }

        return view('find-person-detail', compact('user', 'similarUsers', 'mutualRejectionStatus'));
    }

    /**
     * Validate booking update request and redirect if invalid.
     */
    private function validateBookingUpdateRequest($bookingId, $provider)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            abort(401, 'Authentication required to update bookings.');
        }

        $currentUser = auth()->user();

        // Validate booking ID format
        if (!is_numeric($bookingId) || $bookingId <= 0) {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Invalid booking ID provided.');
        }

        // Find the booking
        $booking = \App\Models\TimeSpendingBooking::find($bookingId);

        if (!$booking) {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Booking not found. It may have been cancelled or deleted.');
        }

        // Check if booking belongs to current user
        if ($booking->client_id !== $currentUser->id) {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'You can only update your own bookings.');
        }

        // Check if booking belongs to the correct provider
        if ($booking->provider_id !== $provider->id) {
            return redirect()->route('find-person.show', $booking->provider)
                ->with('error', 'Booking does not belong to this provider.');
        }

        // Check if booking can be updated
        if ($booking->status === 'cancelled') {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Cancelled bookings cannot be updated. You can make a new booking instead.');
        }

        if ($booking->provider_status === 'accepted') {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Accepted bookings cannot be updated. You can make a new booking if needed.');
        }

        if ($booking->provider_status === 'rejected') {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Rejected bookings cannot be updated. You can make a new booking with different details.');
        }

        // Check if meeting time has started (most important check)
        if ($booking->booking_date <= now()) {
            return redirect()->route('find-person.show', $provider)
                ->with('error', 'Cannot update booking after the meeting start time has passed. You can make a new booking instead.');
        }

        // If we reach here, the booking update request is valid
        // The JavaScript will handle the rest
    }

    /**
     * Display the user detail page in the context of a hire request.
     * This bypasses privacy settings for users who have sent hire requests.
     */
    public function showFromHireRequest(User $user, Request $request): View
    {
        $currentUser = auth()->user();

        if (!$currentUser) {
            abort(403, 'You must be logged in to view this profile.');
        }

        // Check if Time Spending feature is enabled by admin
        if (!Feature::isEnabled('time_spending')) {
            abort(404, 'Time Spending feature is not available.');
        }



        // Verify that the current user has received a hire request from this user
        $hasHireRequest = \App\Models\TimeSpendingBooking::where('client_id', $user->id)
            ->where('provider_id', $currentUser->id)
            ->where('payment_status', 'paid')
            ->exists();

        if (!$hasHireRequest) {
            // If no hire request exists, fall back to normal privacy checks
            return $this->show($user);
        }

        // For hire request context, we bypass ALL privacy and profile setup checks
        // The user should be able to view the profile regardless of the target user's settings

        // Load gallery images regardless of privacy settings for hire request context
        $user->load([
            'galleryImages' => function ($query) {
                $query->active()->ordered();
            }
        ]);

        // For hire request context, we'll show an empty similar users collection
        // since the focus should be on the specific user's profile
        $similarUsers = collect();

        // Check mutual rejection status (for Sugar Partner context)
        $mutualRejectionStatus = null;
        if (\App\Models\Feature::isEnabled('sugar_partner')) {
            $mutualRejectionStatus = $currentUser->getMutualRejectionStatus($user->id);
        }

        return view('find-person-detail', compact('user', 'similarUsers', 'mutualRejectionStatus'))->with('fromHireRequest', true);
    }
}
