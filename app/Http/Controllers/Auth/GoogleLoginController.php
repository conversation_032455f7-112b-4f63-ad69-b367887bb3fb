<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash; // Or Str::random() for password
use Laravel\Socialite\Facades\Socialite;

class GoogleLoginController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes(['email', 'profile'])
                ->redirect();
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Unable to connect to Google. Please try again.');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');

            $googleUser = Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->user();

            \Log::info('Google user data received: ' . $googleUser->getEmail());

            // Check if user already exists
            $existingUser = User::where('google_id', $googleUser->getId())->first();
            $isNewUser = !$existingUser;

            // Find user by google_id or create new one
            $user = User::updateOrCreate([
                'google_id' => $googleUser->getId(),
            ], [
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => Hash::make(uniqid()) // Password is required but not used
            ]);

            // For new users only, enable Time Spending Service by default
            if ($isNewUser) {
                $user->update([
                    'is_time_spending_enabled' => true
                ]);
                \Log::info('Time Spending Service enabled by default for new user: ' . $user->email);
            }

            Auth::login($user, true); // Log the user in

            \Log::info('User logged in successfully: ' . $user->email);

            // Redirect to home page - the root route will handle profile completion check
            return redirect('/')->with('success', 'Welcome back! You have successfully logged in.');

        } catch (\Exception $e) {
            \Log::error('Google OAuth callback failed: ' . $e->getMessage());
            return redirect('/login')->with('error', 'Login with Google failed. Please try again.');
        }
    }
}
