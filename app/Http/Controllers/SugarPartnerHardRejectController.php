<?php

namespace App\Http\Controllers;

use App\Models\SugarPartnerHardReject;
use App\Models\SugarPartnerRejection;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SugarPartnerHardRejectController extends Controller
{
    /**
     * Display users that the current user has rejected (both hard and soft).
     */
    public function index()
    {
        $user = Auth::user();

        // Get all rejections (both hard and soft) made by the current user
        $rejections = SugarPartnerRejection::where('rejector_id', $user->id)
            ->whereIn('rejection_type', ['hard_reject', 'soft_reject'])
            ->with(['rejectedUser', 'exchange'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy('rejected_user_id')
            ->map(function ($userRejections) {
                // Get the most recent rejection for each user
                $latestRejection = $userRejections->first();
                return [
                    'user' => $latestRejection->rejectedUser,
                    'rejection_type' => $latestRejection->rejection_type,
                    'rejection_reason' => $latestRejection->rejection_reason,
                    'rejected_at' => $latestRejection->created_at,
                    'exchange' => $latestRejection->exchange,
                ];
            })
            ->values();

        return view('sugar-partner.hard-rejects.index', compact('rejections'));
    }

    /**
     * Convert soft reject to hard reject.
     */
    public function convertToHardReject(Request $request)
    {
        $request->validate([
            'rejected_user_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $rejectedUserId = (int) $request->rejected_user_id;

        // Find the most recent soft reject by this user against the target user
        $softReject = SugarPartnerRejection::where('rejector_id', $user->id)
            ->where('rejected_user_id', $rejectedUserId)
            ->where('rejection_type', 'soft_reject')
            ->latest()
            ->first();

        if (!$softReject) {
            return redirect()->back()->with('error', 'No soft reject found to convert.');
        }

        DB::beginTransaction();
        try {
            // Update the rejection type to hard reject
            $softReject->update([
                'rejection_type' => 'hard_reject',
                'rejection_reason' => $request->reason ?: $softReject->rejection_reason,
                'admin_note' => ($softReject->admin_note ?: '') . "\nConverted from soft reject to hard reject by user on " . now()->format('Y-m-d H:i:s'),
            ]);

            // Create hard reject record for permanent blocking
            $softReject->createHardRejectRecord();

            // Send notification to the other user
            $rejectedUser = User::find($rejectedUserId);
            if ($rejectedUser && !$rejectedUser->hide_sugar_partner_notifications) {
                \App\Models\Notification::create([
                    'user_id' => $rejectedUserId,
                    'type' => 'sugar_partner_rejection_updated',
                    'title' => 'Rejection Status Changed',
                    'message' => 'Sugar Partner Status Update',
                    'body' => "{$user->name} has changed their rejection to hard reject. Future exchanges are no longer possible.",
                    'data' => [
                        'rejector_id' => $user->id,
                        'rejector_name' => $user->name,
                        'new_status' => 'hard_reject',
                        'reason' => $request->reason,
                        'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                        'action_text' => 'View Sugar Partner'
                    ],
                ]);
            }

            DB::commit();
            return redirect()->back()->with('success', 'Successfully converted soft reject to hard reject.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Failed to convert rejection. Please try again.');
        }
    }

    /**
     * Convert hard reject to soft reject.
     */
    public function convertToSoftReject(Request $request)
    {
        $request->validate([
            'rejected_user_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $rejectedUserId = (int) $request->rejected_user_id;

        // Verify user is the one who made the hard reject
        $hardReject = SugarPartnerHardReject::where('rejector_id', $user->id)
            ->where(function($query) use ($user, $rejectedUserId) {
                $query->where(function($q) use ($user, $rejectedUserId) {
                    $q->where('user1_id', min($user->id, $rejectedUserId))
                      ->where('user2_id', max($user->id, $rejectedUserId));
                });
            })
            ->first();

        if (!$hardReject) {
            return redirect()->back()->with('error', 'You can only modify your own rejections.');
        }

        DB::beginTransaction();
        try {
            // Create a new soft reject record to replace the hard reject
            $originalExchange = $hardReject->originalExchange;

            if ($originalExchange) {
                SugarPartnerRejection::create([
                    'exchange_id' => $originalExchange->id,
                    'rejector_id' => $user->id,
                    'rejected_user_id' => $rejectedUserId,
                    'rejection_type' => 'soft_reject',
                    'rejection_reason' => $request->reason ?: 'Converted from hard reject',
                    'admin_note' => "Converted from hard reject by user on " . now()->format('Y-m-d H:i:s'),
                    'notification_sent' => true, // Skip notification since this is a conversion
                    'notification_sent_at' => now(),
                ]);
            }

            // Remove the hard reject record
            $hardReject->delete();

            // Send notification to the other user
            $rejectedUser = User::find($rejectedUserId);

            if ($rejectedUser && !$rejectedUser->hide_sugar_partner_notifications) {
                \App\Models\Notification::create([
                    'user_id' => $rejectedUserId,
                    'type' => 'sugar_partner_hard_reject_converted',
                    'title' => 'Hard Reject Status Changed',
                    'message' => 'Sugar Partner Status Update',
                    'body' => "{$user->name} has changed their hard reject to soft reject. Future exchanges are now possible.",
                    'data' => [
                        'rejector_id' => $user->id,
                        'rejector_name' => $user->name,
                        'new_status' => 'soft_reject',
                        'reason' => $request->reason,
                        'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                        'action_text' => 'View Sugar Partner'
                    ],
                ]);
            }

            DB::commit();

            return redirect()->back()->with('success', 'Hard reject has been converted to soft reject. Future exchanges may be possible.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to convert hard reject: ' . $e->getMessage());
        }
    }

    /**
     * Remove rejection completely (neutral state).
     */
    public function removeCompletely(Request $request)
    {
        $request->validate([
            'rejected_user_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $rejectedUserId = (int) $request->rejected_user_id;

        DB::beginTransaction();
        try {
            // Remove all rejection records (both hard and soft) between these users where current user was the rejector
            $deletedRejections = SugarPartnerRejection::where('rejector_id', $user->id)
                ->where('rejected_user_id', $rejectedUserId)
                ->delete();

            // Remove hard reject record if exists
            $hardReject = SugarPartnerHardReject::where('rejector_id', $user->id)
                ->where(function($query) use ($user, $rejectedUserId) {
                    $query->where(function($q) use ($user, $rejectedUserId) {
                        $q->where('user1_id', min($user->id, $rejectedUserId))
                          ->where('user2_id', max($user->id, $rejectedUserId));
                    });
                })
                ->first();

            if ($hardReject) {
                $hardReject->delete();
            }

            if ($deletedRejections === 0 && !$hardReject) {
                DB::rollback();
                return redirect()->back()->with('error', 'No rejections found to remove.');
            }

            // Note: Notification for rejection history cleared has been removed per user request

            DB::commit();

            return redirect()->back()->with('success', 'Your rejection has been completely removed. You now have a neutral relationship status.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to clear rejection history: ' . $e->getMessage());
        }
    }

    /**
     * Get hard reject details for AJAX.
     */
    public function getDetails(Request $request)
    {
        $request->validate([
            'user1_id' => 'required|exists:users,id',
            'user2_id' => 'required|exists:users,id',
        ]);

        $user = Auth::user();
        $user1Id = (int) $request->user1_id;
        $user2Id = (int) $request->user2_id;

        // Verify user is involved in this hard reject
        if ($user->id !== $user1Id && $user->id !== $user2Id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $hardReject = SugarPartnerHardReject::getBetweenUsers($user1Id, $user2Id);
        
        if (!$hardReject) {
            return response()->json(['error' => 'Hard reject not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $hardReject->getFormattedDetails()
        ]);
    }
}
