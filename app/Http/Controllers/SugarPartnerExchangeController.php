<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerExchangePayment;
use App\Models\SugarPartnerPartnership;
use App\Models\SugarPartnerRejection;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SugarPartnerExchangeController extends Controller
{
    /**
     * Show exchange payment page for a user.
     */
    public function showPayment(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'You have already paid for this exchange.');
        }

        // Check if exchange is in correct status
        if ($exchange->status !== 'pending_payment') {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'This exchange is no longer accepting payments.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $razorpayKey = Setting::get('razorpay_key_id');

        return view('sugar-partner.exchange.payment', compact(
            'exchange',
            'user',
            'otherUser',
            'razorpayKey'
        ));
    }

    /**
     * Create Razorpay order for the exchange.
     */
    public function createOrder(Request $request, SugarPartnerExchange $exchange): JsonResponse
    {
        $user = Auth::user();

        // Validate that user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to make payments for this exchange.'
            ], 403);
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            return response()->json([
                'success' => false,
                'message' => 'You have already paid for this exchange.'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'wallet_amount_used' => 'sometimes|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $amount = (float) $request->input('amount');
            $walletAmountUsed = (float) $request->input('wallet_amount_used', 0);

            // Get Razorpay configuration
            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            if (empty($razorpayKey) || empty($razorpaySecret)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment gateway not configured. Please contact support.'
                ], 500);
            }

            // Create Razorpay order
            $api = new \Razorpay\Api\Api($razorpayKey, $razorpaySecret);

            $orderData = [
                'receipt' => 'sugar_exchange_' . $exchange->id . '_' . $user->id . '_' . time(),
                'amount' => (int) round($amount * 100), // Amount in paise (must be integer)
                'currency' => $exchange->currency,
                'notes' => [
                    'exchange_id' => $exchange->id,
                    'user_id' => $user->id,
                    'wallet_amount_used' => $walletAmountUsed,
                    'type' => 'sugar_partner_exchange'
                ]
            ];

            $order = $api->order->create($orderData);

            return response()->json([
                'success' => true,
                'order' => [
                    'id' => $order['id'],
                    'amount' => $order['amount'],
                    'currency' => $order['currency'],
                    'razorpay_key' => $razorpayKey
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Razorpay order creation failed', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment order. Please try again.'
            ], 500);
        }
    }

    /**
     * Process payment for exchange.
     */
    public function processPayment(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to pay for this exchange.'
                ], 403);
            }
            abort(403, 'You are not authorized to pay for this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already paid for this exchange.'
                ], 400);
            }
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already paid for this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:razorpay,wallet,wallet_partial,hybrid',
            'amount' => 'sometimes|numeric|min:0',
            'is_partial' => 'sometimes|boolean',
            'wallet_amount_used' => 'sometimes|numeric|min:0',
            'razorpay_payment_id' => 'sometimes|string', // Made optional for demo
            'razorpay_order_id' => 'sometimes|string',   // Made optional for demo
            'razorpay_signature' => 'sometimes|string',  // Made optional for demo
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            $paymentAmount = $request->input('amount');
            $isPartial = $request->input('is_partial', false);
            $walletAmountUsed = $request->input('wallet_amount_used', 0);

            \Log::info('Processing payment', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method,
                'amount' => $paymentAmount,
                'is_partial' => $isPartial,
                'wallet_amount_used' => $walletAmountUsed,
                'expects_json' => $request->expectsJson()
            ]);

            if ($request->payment_method === 'wallet' || $request->payment_method === 'wallet_partial') {
                $this->processWalletPayment($exchange, $user, $paymentAmount, $isPartial);
            } else {
                $this->processRazorpayPayment($exchange, $user, $request, $paymentAmount, $walletAmountUsed);
            }

            DB::commit();

            \Log::info('Payment completed successfully', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully!'
                ]);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', 'Payment completed successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Payment processing failed', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Payment failed: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show exchange status page.
     */
    public function showStatus(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $userHasPaid = $exchange->userHasPaid($user->id);
        $otherUserHasPaid = $exchange->userHasPaid($otherUser->id);
        $anyUserPaid = $exchange->anyUserPaid();
        $bothUsersPaid = $exchange->bothUsersPaid();
        // Keep bothPaid for backward compatibility in views (now represents anyUserPaid for profile access)
        $bothPaid = $anyUserPaid;

        // Load user's payment if exists
        $userPayment = $exchange->payments()
            ->where('user_id', $user->id)
            ->first();

        // Load user's response if exists
        $userResponse = $exchange->getUserResponse($user->id);

        // Get other user's response if exists
        $otherUserResponse = $exchange->getUserResponse($otherUser->id);

        return view('sugar-partner.exchange.status', compact(
            'exchange',
            'user',
            'otherUser',
            'userHasPaid',
            'otherUserHasPaid',
            'anyUserPaid',
            'bothUsersPaid',
            'bothPaid', // For backward compatibility
            'userPayment',
            'userResponse',
            'otherUserResponse'
        ));
    }

    /**
     * Show other user's profile after payment.
     */
    public function showProfile(Request $request, SugarPartnerExchange $exchange, User $user)
    {
        $currentUser = Auth::user();



        // Verify user is part of this exchange
        if ($exchange->user1_id !== $currentUser->id && $exchange->user2_id !== $currentUser->id) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to view this exchange.'
                ], 403);
            }
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Verify the profile user is the other user in the exchange
        $otherUser = $exchange->getOtherUser($currentUser->id);

        if ($user->id !== $otherUser->id) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid profile access.'
                ], 403);
            }
            abort(403, 'Invalid profile access.');
        }

        // Verify at least one user has paid (new single-payment model)
        $anyUserPaid = $exchange->anyUserPaid();


        if (!$anyUserPaid) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment must be completed before viewing profiles.'
                ], 403);
            }
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'Payment must be completed before viewing profiles.');
        }

        // Mark profiles as viewed if this is the first time
        if ($exchange->status === 'payment_completed') {
            $exchange->markProfilesViewed();
        }

        // Load profile user with all necessary data (bypass privacy settings)
        $user->load([
            'galleryImages' => function ($query) {
                $query->active()->ordered();
            }
        ]);

        if ($request->expectsJson()) {
            // Check if current user has already responded
            $currentUserResponse = $exchange->getUserResponse($currentUser->id);

            // Return profile data for AJAX requests
            return response()->json([
                'success' => true,
                'profile' => [
                    'name' => $user->name,
                    'age' => $user->getAge(),
                    'gender' => $user->gender,
                    'interests' => $user->interests,
                    'expectation' => $user->expectation,
                    'profile_picture' => $user->profile_picture ? asset('storage/' . $user->profile_picture) : asset('images/default-avatar.png'),
                    'gallery_images' => $user->galleryImages->map(function($image) {
                        return asset('storage/' . $image->image_path);
                    }),
                    'sugar_partner_bio' => $user->sugar_partner_bio,
                    'sugar_partner_expectations' => $user->sugar_partner_expectations,
                ],
                'user_response' => $currentUserResponse ? [
                    'type' => $currentUserResponse->rejection_type,
                    'display_name' => $currentUserResponse->getRejectionTypeDisplayName(),
                    'reason' => $currentUserResponse->rejection_reason
                ] : null
            ]);
        }

        // Check if the profile user has previously soft rejected the current user
        $profileUserHasSoftRejected = $user->hasSoftRejected($currentUser->id);

        // Get mutual rejection status between users
        $mutualRejectionStatus = $currentUser->getMutualRejectionStatus($user->id);

        return view('sugar-partner.exchange.profile', compact(
            'exchange',
            'currentUser',
            'user',
            'profileUserHasSoftRejected',
            'mutualRejectionStatus'
        ))->with('profileUser', $user);
    }

    /**
     * Submit response to exchange.
     */
    public function submitResponse(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to respond to this exchange.');
        }

        // Verify current user has paid
        if (!$exchange->userHasPaid($user->id)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You must complete payment before responding.'
                ], 403);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You must complete payment before responding.');
        }

        // Check if user has already responded
        if ($exchange->getUserResponse($user->id)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already responded to this exchange.'
                ], 400);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already responded to this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'rejection_type' => 'required|in:accept,soft_reject,hard_reject',
            'rejection_reason' => 'nullable|string|max:1000',
            'admin_note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            $rejection = SugarPartnerRejection::createRejection(
                $exchange,
                $user->id,
                $request->rejection_type,
                $request->rejection_reason,
                $request->admin_note
            );

            // Handle different response types
            $this->handleResponseType($exchange, $user, $request->rejection_type, $rejection);

            DB::commit();

            $responseType = match($request->rejection_type) {
                'accept' => 'acceptance',
                'soft_reject' => 'soft rejection',
                'hard_reject' => 'hard rejection',
            };

            // Determine notification message based on response type
            $notificationMessage = $this->getResponseMessage($request->rejection_type, $exchange);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $notificationMessage
                ]);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', $notificationMessage);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to submit response: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to submit response: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Process wallet payment.
     */
    protected function processWalletPayment(SugarPartnerExchange $exchange, User $user, $paymentAmount = null, $isPartial = false): void
    {
        $wallet = $user->getWallet();
        $userPrice = $exchange->getPriceForUser($user->id);
        $amountToDeduct = $paymentAmount ?? $userPrice;

        // For partial payments, we allow any amount up to wallet balance
        // For full payments, we need the full amount
        if (!$isPartial && $wallet->balance < $userPrice) {
            throw new \Exception('Insufficient wallet balance.');
        }

        if ($isPartial && $wallet->balance < $amountToDeduct) {
            throw new \Exception('Insufficient wallet balance for partial payment.');
        }

        if ($amountToDeduct > $userPrice) {
            throw new \Exception('Payment amount cannot exceed required amount.');
        }

        // Deduct from wallet
        $wallet->deductMoney(
            $amountToDeduct,
            "Sugar Partner Exchange Payment - Exchange #{$exchange->id}" . ($isPartial ? ' (Partial)' : '')
        );

        // Create payment record
        $payment = SugarPartnerExchangePayment::createForExchange($exchange, $user->id, [
            'payment_method' => 'wallet',
            'amount' => $amountToDeduct,
            'payment_details' => [
                'wallet_balance_before' => $wallet->balance + $amountToDeduct,
                'wallet_balance_after' => $wallet->balance,
                'is_partial' => $isPartial,
                'amount_paid' => $amountToDeduct,
                'total_required' => $userPrice,
            ]
        ]);

        // Only mark as completed if this is a full payment
        if (!$isPartial) {
            $payment->markAsCompleted([
                'payment_source' => 'wallet',
                'transaction_id' => 'wallet_' . time() . '_' . $user->id,
            ]);
        } else {
            // For partial payments, just mark the payment as processed but not completed
            $payment->update([
                'status' => 'partial_completed',
                'payment_details' => array_merge($payment->payment_details ?? [], [
                    'partial_payment_id' => 'wallet_partial_' . time() . '_' . $user->id,
                    'processed_at' => now(),
                ])
            ]);
        }
    }

    /**
     * Handle different response types and their consequences.
     */
    protected function handleResponseType(SugarPartnerExchange $exchange, User $user, string $responseType, SugarPartnerRejection $rejection): void
    {
        $otherUser = $exchange->getOtherUser($user->id);

        switch ($responseType) {
            case 'accept':
                // For acceptance, wait for other user's response
                $this->handleAcceptance($exchange, $user, $otherUser);
                break;

            case 'soft_reject':
                // For soft reject, remove from current exchange but allow future exchanges
                $this->handleSoftReject($exchange, $user, $otherUser, $rejection);
                break;

            case 'hard_reject':
                // For hard reject, permanently block future exchanges
                $this->handleHardReject($exchange, $user, $otherUser, $rejection);
                break;
        }

        // Mark exchange as responses completed if both users have responded
        $exchange->markResponsesCompleted();
    }

    /**
     * Handle acceptance response.
     */
    protected function handleAcceptance(SugarPartnerExchange $exchange, User $user, User $otherUser): void
    {
        // Check if other user has also responded
        $otherUserResponse = $exchange->getUserResponse($otherUser->id);

        if ($otherUserResponse) {
            // Both users have responded
            if ($otherUserResponse->rejection_type === 'accept') {
                // Both accepted - create partnership and send success notifications
                $this->createPartnership($exchange, $user, $otherUser);
                $this->sendMatchSuccessNotifications($exchange, $user, $otherUser);
            } else {
                // Other user rejected - send mismatch notification
                $this->sendMismatchNotification($user, $otherUser, $otherUserResponse->rejection_type);
            }

            // Mark exchange as completed since both users have responded
            $exchange->update(['status' => 'responses_completed']);
        } else {
            // Wait for other user's response - only send notification if both users have paid
            if ($exchange->bothUsersPaid()) {
                $this->sendWaitingForResponseNotification($otherUser, $user);
            }
            // If both users haven't paid yet, the notification will be sent when the second user pays
        }
    }

    /**
     * Handle soft reject response.
     */
    protected function handleSoftReject(SugarPartnerExchange $exchange, User $user, User $otherUser, SugarPartnerRejection $rejection): void
    {
        // Check if other user has already responded
        $otherUserResponse = $exchange->getUserResponse($otherUser->id);

        if ($otherUserResponse) {
            // Both users have responded - send final notifications and mark as completed
            if ($otherUserResponse->rejection_type === 'accept') {
                // Other user accepted but this user rejected - send mismatch notification
                $this->sendMismatchNotification($otherUser, $user, 'soft_reject');
            }
            // If other user also rejected, no additional notifications needed

            // Mark exchange as completed since both users have responded
            $exchange->update(['status' => 'responses_completed']);
        } else {
            // Other user hasn't responded yet - don't mark as completed
            // Only send immediate notification if both users have paid
            if ($exchange->bothUsersPaid()) {
                $this->sendRejectionNotification($otherUser, $user, 'soft_reject', $rejection->rejection_reason);
            }
            // If both users haven't paid yet, the notification will be sent when the second user pays
        }
    }

    /**
     * Handle hard reject response.
     */
    protected function handleHardReject(SugarPartnerExchange $exchange, User $user, User $otherUser, SugarPartnerRejection $rejection): void
    {
        // Check if other user has already responded
        $otherUserResponse = $exchange->getUserResponse($otherUser->id);

        if ($otherUserResponse) {
            // Both users have responded - send final notifications and mark as completed
            if ($otherUserResponse->rejection_type === 'accept') {
                // Other user accepted but this user rejected - send mismatch notification
                $this->sendMismatchNotification($otherUser, $user, 'hard_reject');
            }
            // If other user also rejected, no additional notifications needed

            // Mark exchange as completed since both users have responded
            $exchange->update(['status' => 'responses_completed']);
        } else {
            // Other user hasn't responded yet - don't mark as completed
            // Only send immediate notification if both users have paid
            if ($exchange->bothUsersPaid()) {
                $this->sendRejectionNotification($otherUser, $user, 'hard_reject', $rejection->rejection_reason);
            }
            // If both users haven't paid yet, the notification will be sent when the second user pays
        }
    }

    /**
     * Get appropriate response message based on response type.
     */
    protected function getResponseMessage(string $responseType, SugarPartnerExchange $exchange): string
    {
        switch ($responseType) {
            case 'accept':
                $otherUserResponse = $exchange->getUserResponse($exchange->getOtherUser(Auth::id())->id);
                if ($otherUserResponse) {
                    if ($otherUserResponse->rejection_type === 'accept') {
                        return 'Congratulations! Both of you accepted the match. You can now connect with each other.';
                    } else {
                        return 'You accepted the match, but the other user has declined. Better luck next time!';
                    }
                } else {
                    return 'You accepted the match! Waiting for the other user to respond.';
                }

            case 'soft_reject':
                return 'You chose "Maybe Later" for this match. The other user has been notified and this exchange is now closed.';

            case 'hard_reject':
                return 'You chose "Not Interested" for this match. The other user has been notified and future exchanges with this user are blocked.';

            default:
                return 'Your response has been recorded.';
        }
    }

    /**
     * Create partnership when both users accept.
     */
    protected function createPartnership(SugarPartnerExchange $exchange, User $user1, User $user2): void
    {
        // Check if partnership already exists
        $existingPartnership = SugarPartnerPartnership::where(function($query) use ($user1, $user2) {
            $query->where(function($q) use ($user1, $user2) {
                $q->where('user1_id', $user1->id)->where('user2_id', $user2->id);
            })->orWhere(function($q) use ($user1, $user2) {
                $q->where('user1_id', $user2->id)->where('user2_id', $user1->id);
            });
        })->where('status', 'active')->first();

        if (!$existingPartnership) {
            $partnership = SugarPartnerPartnership::createFromExchange($exchange);

            // Clear "What I Want" preferences for both users when partnership is created
            $this->clearWhatIWantPreferences($user1, $user2);
        }
    }

    /**
     * Clear "What I Want" preferences for both users when partnership is created.
     */
    protected function clearWhatIWantPreferences(User $user1, User $user2): void
    {
        // Clear what_i_want for user1
        if ($user1->sugar_partner_want) {
            $user1->update(['sugar_partner_want' => null]);
        }

        // Clear what_i_want for user2
        if ($user2->sugar_partner_want) {
            $user2->update(['sugar_partner_want' => null]);
        }
    }

    /**
     * Send match success notifications to both users.
     */
    protected function sendMatchSuccessNotifications(SugarPartnerExchange $exchange, User $user1, User $user2): void
    {
        foreach ([$user1, $user2] as $user) {
            $otherUser = $user->id === $user1->id ? $user2 : $user1;

            if (!$user->hide_sugar_partner_notifications) {
                // Check if the other user has previously soft rejected this user
                $otherUserLabel = $otherUser->getSugarPartnerRoleLabel();
                if ($user->hasSoftRejected($otherUser->id)) {
                    $otherUserLabel .= " (Soft Rejected)";
                }

                \App\Models\Notification::create([
                    'user_id' => $user->id,
                    'type' => 'sugar_partner_match_success',
                    'title' => 'Sugar Partner Match Success!',
                    'message' => 'Sugar Partner Match',
                    'body' => "Congratulations! Both you and {$otherUserLabel} accepted the match. You can now connect with each other.",
                    'data' => [
                        'exchange_id' => $exchange->id,
                        'match_user_id' => $otherUser->id,
                        'match_user_name' => $otherUser->name,
                        'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                        'action_text' => 'View Match'
                    ],
                ]);
            }
        }
    }

    /**
     * Send mismatch notification when responses don't align.
     */
    protected function sendMismatchNotification(User $acceptingUser, User $rejectingUser, string $rejectionType): void
    {
        if (!$acceptingUser->hide_sugar_partner_notifications) {
            $rejectionText = $rejectionType === 'soft_reject' ? 'said "Maybe Later"' : 'is not interested';

            // Check if the rejecting user has previously soft rejected the accepting user
            $rejectingUserLabel = $rejectingUser->getSugarPartnerRoleLabel();
            if ($acceptingUser->hasSoftRejected($rejectingUser->id)) {
                $rejectingUserLabel .= " (Soft Rejected)";
            }

            \App\Models\Notification::create([
                'user_id' => $acceptingUser->id,
                'type' => 'sugar_partner_mismatch',
                'title' => 'Sugar Partner Response Received',
                'message' => 'Sugar Partner Exchange',
                'body' => "You accepted the match with {$rejectingUserLabel}, but they {$rejectionText}. Better luck next time!",
                'data' => [
                    'other_user_id' => $rejectingUser->id,
                    'other_user_name' => $rejectingUser->name,
                    'rejection_type' => $rejectionType,
                    'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                    'action_text' => 'View Exchanges'
                ],
            ]);
        }
    }

    /**
     * Send waiting for response notification.
     */
    protected function sendWaitingForResponseNotification(User $waitingUser, User $respondingUser): void
    {
        if (!$waitingUser->hide_sugar_partner_notifications) {
            // Check if the responding user has previously soft rejected the waiting user
            $respondingUserLabel = $respondingUser->getSugarPartnerRoleLabel();
            if ($waitingUser->hasSoftRejected($respondingUser->id)) {
                $respondingUserLabel .= " (Soft Rejected)";
            }

            \App\Models\Notification::create([
                'user_id' => $waitingUser->id,
                'type' => 'sugar_partner_response_pending',
                'title' => 'Sugar Partner Response Received',
                'message' => 'Sugar Partner Exchange',
                'body' => "{$respondingUserLabel} has accepted your profile! Please review their profile and respond.",
                'data' => [
                    'other_user_id' => $respondingUser->id,
                    'other_user_name' => $respondingUser->name,
                    'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                    'action_text' => 'Respond Now'
                ],
            ]);
        }
    }

    /**
     * Send rejection notification.
     */
    protected function sendRejectionNotification(User $rejectedUser, User $rejector, string $rejectionType, ?string $reason = null): void
    {
        if (!$rejectedUser->hide_sugar_partner_notifications) {
            $rejectionText = match($rejectionType) {
                'soft_reject' => 'said "Maybe Later"',
                'hard_reject' => 'is not interested',
                default => 'has responded'
            };

            // Check if the rejector has previously soft rejected the rejected user
            $rejectorLabel = $rejector->getSugarPartnerRoleLabel();
            if ($rejectedUser->hasSoftRejected($rejector->id)) {
                $rejectorLabel .= " (Soft Rejected)";
            }

            $body = "{$rejectorLabel} {$rejectionText} to your Sugar Partner profile exchange.";
            if ($reason) {
                $body .= " Reason: {$reason}";
            }

            \App\Models\Notification::create([
                'user_id' => $rejectedUser->id,
                'type' => 'sugar_partner_response_received',
                'title' => 'Sugar Partner Response Received',
                'message' => 'Sugar Partner Exchange',
                'body' => $body,
                'data' => [
                    'rejector_id' => $rejector->id,
                    'rejector_name' => $rejector->name,
                    'rejection_type' => $rejectionType,
                    'rejection_reason' => $reason,
                    'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                    'action_text' => 'View Exchange Details'
                ],
            ]);
        }
    }

    /**
     * Process Razorpay payment.
     */
    protected function processRazorpayPayment(SugarPartnerExchange $exchange, User $user, Request $request, $paymentAmount = null, $walletAmountUsed = 0): void
    {
        try {
            $userPrice = $exchange->getPriceForUser($user->id);
            $razorpayAmount = $paymentAmount ?? $userPrice;
            $isHybridPayment = $walletAmountUsed > 0;

            // For automatic payment processing, we'll simulate successful payment
            // In a real implementation, you would integrate with Razorpay API here

            $paymentMethod = $isHybridPayment ? 'hybrid' : 'razorpay';

            $paymentId = $request->input('razorpay_payment_id', 'auto_' . time() . '_' . $user->id);

            // Create payment record with proper amount handling
            $payment = new SugarPartnerExchangePayment([
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'amount' => $razorpayAmount, // Use the actual payment amount, not the user's default price
                'currency' => $exchange->currency,
                'payment_method' => $paymentMethod,
                'payment_id' => $paymentId,
                'order_id' => $request->input('razorpay_order_id'),
                'signature' => $request->input('razorpay_signature'),
                'payment_details' => [
                    'razorpay_payment_id' => $request->input('razorpay_payment_id'),
                    'razorpay_order_id' => $request->input('razorpay_order_id'),
                    'razorpay_signature' => $request->input('razorpay_signature'),
                    'razorpay_amount' => $razorpayAmount,
                    'wallet_amount_used' => $walletAmountUsed,
                    'total_amount' => $razorpayAmount + $walletAmountUsed,
                    'is_hybrid' => $isHybridPayment,
                    'auto_processed' => true,
                    'processed_at' => now(),
                ]
            ]);

            $payment->save();

            $payment->markAsCompleted([
                'payment_source' => $isHybridPayment ? 'hybrid_auto' : 'razorpay_auto',
                'verified' => true,
                'auto_payment' => true,
            ]);

        } catch (\Exception $e) {
            \Log::error('Razorpay payment processing failed', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \Exception('Payment processing failed: ' . $e->getMessage());
        }
    }
}
