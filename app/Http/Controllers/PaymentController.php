<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{
    /**
     * Show the payment page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's payment history for events (money spent)
        $eventPayments = $user->eventPayments()
            ->with('meetingAddress')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get user's Time Spending booking payments (money spent as client)
        $timeSpendingPayments = $user->timeSpendingBookingsAsClient()
            ->with('provider')
            ->where('payment_status', 'paid')
            ->orderBy('paid_at', 'desc')
            ->get();

        // Get user's earnings from Time Spending (money earned as provider)
        $timeSpendingEarnings = $user->providerBookings()
            ->with('client')
            ->where('payment_status', 'paid')
            ->where('provider_status', 'accepted')
            ->orderBy('paid_at', 'desc')
            ->get();

        // Get user's wallet transactions
        $walletTransactions = $user->walletTransactions()
            ->with(['booking.client', 'booking.provider'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get subscription payments
        $subscriptionPayments = $user->subscriptions()
            ->with('subscriptionPlan')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get wallet balance
        $wallet = $user->getWallet();

        return view('payment.index', compact(
            'user',
            'eventPayments',
            'timeSpendingPayments',
            'timeSpendingEarnings',
            'walletTransactions',
            'subscriptionPayments',
            'wallet'
        ));
    }

    /**
     * Process the payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function process(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'razorpay_payment_id' => 'required|string',
            'razorpay_order_id' => 'required|string',
            'razorpay_signature' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Verify the payment signature
        $razorpayKeyId = Setting::get('razorpay_key_id');
        $razorpayKeySecret = Setting::get('razorpay_key_secret');

        // In a real implementation, you would verify the signature here
        // For now, we'll just mark the user as paid

        $user = Auth::user();
        $user->paid_at = now();
        $user->save();

        return redirect()->route('event.address')
            ->with('status', 'payment-successful');
    }

    /**
     * Show payment page for a specific meeting event.
     *
     * @param  int  $eventId
     * @return \Illuminate\View\View
     */
    public function meetingEventPayment($eventId)
    {
        $user = Auth::user();

        // Check if user's profile is 100% complete
        if (!$user->isProfileComplete()) {
            $redirectRoute = \App\Models\MeetingAddress::hasAvailableEvents() ? 'event.address' : 'home';
            return redirect()->route($redirectRoute)
                ->with('error', 'Please complete your profile 100% before joining any events. Complete all sections: Basic Info, Profile Picture, and Interests.');
        }

        $event = \App\Models\MeetingAddress::where('id', $eventId)
            ->where('is_event_enabled', true)
            ->firstOrFail();

        // Check if user has already paid for this event
        $hasPaidForEvent = $user->hasPaidForEvent($event->id);

        // Check if this is a couple event and user is eligible
        if ($event->is_couple_event) {
            if (!$user->is_couple_activity_enabled || !$user->hasOwnPartner()) {
                return redirect()->route('couple-activity.select-partner')
                    ->with('error', 'To register for this couple event, you need to enable couple activity and add a partner first.');
            }
        }

        // Calculate payment amount based on event type and user's gender
        if ($event->is_couple_event) {
            $paymentAmount = $event->payment_amount_couple;
        } else {
            $paymentAmount = $user->gender === 'female'
                ? $event->payment_amount_girls
                : $event->payment_amount_boys;
        }

        $razorpayKey = Setting::get('razorpay_key_id');

        return view('payment.meeting_event', compact('user', 'event', 'paymentAmount', 'razorpayKey', 'hasPaidForEvent'));
    }

    /**
     * Process payment for a specific meeting event.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processMeetingEventPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:meeting_addresses,id',
            'razorpay_payment_id' => 'required|string',
            'razorpay_order_id' => 'required|string',
            'razorpay_signature' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user = Auth::user();

        // Check if user's profile is 100% complete
        if (!$user->isProfileComplete()) {
            $redirectRoute = \App\Models\MeetingAddress::hasAvailableEvents() ? 'event.address' : 'home';
            return redirect()->route($redirectRoute)
                ->with('error', 'Please complete your profile 100% before joining any events. Complete all sections: Basic Info, Profile Picture, and Interests.');
        }
        $event = \App\Models\MeetingAddress::findOrFail($request->event_id);

        // Check if user has already paid for this event
        if ($user->hasPaidForEvent($event->id)) {
            return redirect()->route('event.address.show', $event->id)
                ->with('info', 'You have already paid for this event.');
        }

        // Calculate payment amount based on event type and user's gender
        if ($event->is_couple_event) {
            $paymentAmount = $event->payment_amount_couple;
        } else {
            $paymentAmount = $user->gender === 'female'
                ? $event->payment_amount_girls
                : $event->payment_amount_boys;
        }

        // Verify the payment signature
        $razorpayKeyId = Setting::get('razorpay_key_id');
        $razorpayKeySecret = Setting::get('razorpay_key_secret');

        // In a real implementation, you would verify the signature here
        // For now, we'll just record the event-specific payment

        \App\Models\EventPayment::create([
            'user_id' => $user->id,
            'meeting_address_id' => $event->id,
            'amount_paid' => $paymentAmount,
            'payment_method' => 'razorpay',
            'razorpay_payment_id' => $request->razorpay_payment_id,
            'razorpay_order_id' => $request->razorpay_order_id,
            'razorpay_signature' => $request->razorpay_signature,
            'status' => 'completed',
        ]);

        return redirect()->route('event.address.show', $request->event_id)
            ->with('status', 'payment-successful');
    }

    /**
     * Simulate a payment for a specific event (for testing).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function simulateEventPayment(Request $request)
    {
        $user = Auth::user();

        // Check if user's profile is 100% complete
        if (!$user->isProfileComplete()) {
            return redirect()->route('event.address')
                ->with('error', 'Please complete your profile 100% before joining any events. Complete all sections: Basic Info, Profile Picture, and Interests.');
        }

        $eventId = $request->input('event_id');

        if (!$eventId) {
            return redirect()->back()->with('error', 'Event ID is required.');
        }

        $event = \App\Models\MeetingAddress::findOrFail($eventId);

        // Check if user has already paid for this event
        if ($user->hasPaidForEvent($event->id)) {
            return redirect()->route('event.address.show', $event->id)
                ->with('info', 'You have already paid for this event.');
        }

        // Calculate payment amount based on event type and user's gender
        if ($event->is_couple_event) {
            $paymentAmount = $event->payment_amount_couple;
        } else {
            $paymentAmount = $user->gender === 'female'
                ? $event->payment_amount_girls
                : $event->payment_amount_boys;
        }

        // Create event-specific payment record
        \App\Models\EventPayment::create([
            'user_id' => $user->id,
            'meeting_address_id' => $event->id,
            'amount_paid' => $paymentAmount,
            'payment_method' => 'simulate',
            'status' => 'completed',
        ]);

        return redirect()->route('event.address.show', $event->id)
            ->with('status', 'payment-successful');
    }

    /**
     * Simulate a payment (for testing) - Legacy method for general payment.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function simulatePayment()
    {
        $user = Auth::user();

        if ($user) {
            $user->paid_at = now(); // Set paid_at to current timestamp
            $user->save();

            return Redirect::route('event.address')->with('status', 'payment-successful');
        }

        return Redirect::route('home')->with('error', 'Could not simulate payment. User not found.');
    }
}
