<?php

namespace App\Http\Controllers;

use App\Models\UserReport;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Store a new user report
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You must be logged in to submit a report.'
                ], 401);
            }

            $reporterId = Auth::id();
            $reportedUserId = $request->reported_user_id;

            // Validation rules
            $validator = Validator::make($request->all(), [
                'reported_user_id' => 'required|exists:users,id',
                'category' => 'required|in:inappropriate_behavior,fake_profile,harassment,spam,inappropriate_content,scam_fraud,underage_user,violence_threats,hate_speech,other',
                'description' => 'nullable|string|max:1000',
                'evidence_file' => 'nullable|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Prevent self-reporting
            if ($reporterId == $reportedUserId) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot report yourself.'
                ], 422);
            }

            // Check if user has already reported this user in the last 24 hours for the same category
            $existingReport = UserReport::where('reporter_id', $reporterId)
                ->where('reported_user_id', $reportedUserId)
                ->where('category', $request->category)
                ->where('created_at', '>=', Carbon::now()->subHours(24))
                ->first();

            if ($existingReport) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reported this user for this category in the last 24 hours.'
                ], 422);
            }

            // Check for spam reporting (more than 5 reports in the last hour)
            $recentReportsCount = UserReport::where('reporter_id', $reporterId)
                ->where('created_at', '>=', Carbon::now()->subHour())
                ->count();

            if ($recentReportsCount >= 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have submitted too many reports recently. Please wait before submitting another report.'
                ], 429);
            }

            // Handle file upload if provided
            $evidenceFilePath = null;
            if ($request->hasFile('evidence_file')) {
                $file = $request->file('evidence_file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $evidenceFilePath = $file->storeAs('reports/evidence', $fileName, 'public');
            }

            // Create the report
            $report = UserReport::create([
                'reporter_id' => $reporterId,
                'reported_user_id' => $reportedUserId,
                'category' => $request->category,
                'description' => $request->description,
                'evidence_file' => $evidenceFilePath,
                'status' => UserReport::STATUS_PENDING,
            ]);

            // Log the report for admin notification
            Log::info('New user report submitted', [
                'report_id' => $report->id,
                'reporter_id' => $reporterId,
                'reported_user_id' => $reportedUserId,
                'category' => $request->category
            ]);

            // Create notification for admins
            $this->notifyAdminsOfNewReport($report);

            return response()->json([
                'success' => true,
                'message' => 'Report submitted successfully. Our team will review it shortly.',
                'report_id' => $report->id
            ]);

        } catch (\Exception $e) {
            Log::error('Error submitting user report: ' . $e->getMessage(), [
                'reporter_id' => Auth::id(),
                'reported_user_id' => $request->reported_user_id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while submitting the report. Please try again.'
            ], 500);
        }
    }

    /**
     * Notify admins of new report
     */
    private function notifyAdminsOfNewReport(UserReport $report): void
    {
        try {
            // Get all admin users
            $admins = User::where('is_admin', true)
                ->orWhereIn('role', ['admin', 'super_admin'])
                ->get();

            foreach ($admins as $admin) {
                Notification::create([
                    'user_id' => $admin->id,
                    'type' => 'new_user_report',
                    'title' => 'New User Report Submitted',
                    'message' => "A new report has been submitted for review. Category: {$report->getCategoryDisplayName()}",
                    'body' => "A new report has been submitted for review. Category: {$report->getCategoryDisplayName()}",
                    'data' => [
                        'report_id' => $report->id,
                        'category' => $report->category,
                        'reported_user_id' => $report->reported_user_id,
                        'reporter_id' => $report->reporter_id,
                    ],
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error notifying admins of new report: ' . $e->getMessage());
        }
    }
}
