<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Razorpay Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Razorpay payment gateway integration
    |
    */

    'key_id' => env('RAZORPAY_KEY_ID'),
    'key_secret' => env('RAZORPAY_KEY_SECRET'),
    
    /*
    |--------------------------------------------------------------------------
    | Razorpay Environment
    |--------------------------------------------------------------------------
    |
    | This determines if we're using test or live credentials
    |
    */
    'environment' => env('RAZORPAY_ENVIRONMENT', 'test'), // 'test' or 'live'
    
    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Razorpay webhooks
    |
    */
    'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET'),
    
    /*
    |--------------------------------------------------------------------------
    | Payment Options
    |--------------------------------------------------------------------------
    |
    | Default options for Razorpay checkout
    |
    */
    'options' => [
        'currency' => 'INR',
        'theme' => [
            'color' => '#4F46E5'
        ],
        'modal' => [
            'backdropclose' => false,
            'escape' => false,
            'handleback' => false,
            'confirm_close' => true,
            'ondismiss' => 'function(){console.log("Payment cancelled by user");}'
        ],
        'retry' => [
            'enabled' => true,
            'max_count' => 3
        ],
        'timeout' => 300, // 5 minutes
        'remember_customer' => false,
        'readonly' => [
            'email' => false,
            'contact' => false,
            'name' => false
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security related configurations
    |
    */
    'verify_signature' => env('RAZORPAY_VERIFY_SIGNATURE', true),
    'log_payments' => env('RAZORPAY_LOG_PAYMENTS', true),
    
    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | API related configurations
    |
    */
    'api_timeout' => 30, // seconds
    'max_retries' => 3,
    
    /*
    |--------------------------------------------------------------------------
    | Allowed Payment Methods
    |--------------------------------------------------------------------------
    |
    | Configure which payment methods to allow
    |
    */
    'payment_methods' => [
        'card' => true,
        'netbanking' => true,
        'wallet' => true,
        'emi' => true,
        'upi' => true,
        'cardless_emi' => false,
        'paylater' => true,
        'bank_transfer' => false
    ]
];
