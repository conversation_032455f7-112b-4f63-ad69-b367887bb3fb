@props(['userId' => null, 'userName' => null])

<!-- Report User Modal -->
<div x-data="{
    showModal: false,
    loading: false,
    showSuccess: false,
    showError: false,
    successMessage: '',
    errorMessage: '',
    errors: {},

    async submitReport() {
        this.loading = true;
        this.showSuccess = false;
        this.showError = false;
        this.errors = {};

        try {
            const formData = new FormData(document.getElementById('reportUserForm'));

            const response = await fetch('{{ route("report.store") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.successMessage = data.message;
                this.showSuccess = true;

                // Close modal after 2 seconds
                setTimeout(() => {
                    this.showModal = false;
                }, 2000);
            } else {
                if (data.errors) {
                    this.errors = data.errors;
                } else {
                    this.errorMessage = data.message || 'An error occurred while submitting the report.';
                    this.showError = true;
                }
            }

        } catch (error) {
            console.error('Error:', error);
            this.errorMessage = 'Network error. Please check your connection and try again.';
            this.showError = true;
        } finally {
            this.loading = false;
        }
    }
}"
x-show="showModal"
x-cloak
class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
style="display: none;"
@keydown.escape.window="showModal = false">

    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white max-w-md">
        <!-- Modal Header -->
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Report User</h3>
            <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
                    @click="showModal = false">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="py-4">
            <!-- Success Message -->
            <div x-show="showSuccess"
                 x-transition
                 class="mb-4 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="successMessage"></span>
                </div>
            </div>

            <!-- Error Message -->
            <div x-show="showError"
                 x-transition
                 class="mb-4 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="errorMessage"></span>
                </div>
            </div>

            <p class="text-gray-600 mb-4">
                You are reporting <strong id="reportedUserName">{{ $userName ?? 'this user' }}</strong>.
                Please provide details about why you're reporting this user.
            </p>

            <form id="reportUserForm" @submit.prevent="submitReport">
                @csrf
                <input type="hidden" id="reportedUserId" name="reported_user_id" value="{{ $userId }}">

                <!-- Category Selection -->
                <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Report Category <span class="text-red-500">*</span>
                    </label>
                    <select id="category"
                            name="category"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            :class="{ 'border-red-500': errors.category }">
                        <option value="">Select a category</option>
                        <option value="inappropriate_behavior">Inappropriate Behavior</option>
                        <option value="fake_profile">Fake Profile</option>
                        <option value="harassment">Harassment</option>
                        <option value="spam">Spam</option>
                        <option value="inappropriate_content">Inappropriate Content</option>
                        <option value="scam_fraud">Scam/Fraud</option>
                        <option value="underage_user">Underage User</option>
                        <option value="violence_threats">Violence/Threats</option>
                        <option value="hate_speech">Hate Speech</option>
                        <option value="other">Other</option>
                    </select>
                    <p x-show="errors.category" x-text="errors.category?.[0]" class="mt-1 text-sm text-red-600"></p>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description"
                              name="description"
                              rows="4"
                              placeholder="Please provide additional details about this report..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              :class="{ 'border-red-500': errors.description }"></textarea>
                    <p x-show="errors.description" x-text="errors.description?.[0]" class="mt-1 text-sm text-red-600"></p>
                </div>

                <!-- Evidence File Upload -->
                <div class="mb-4">
                    <label for="evidenceFile" class="block text-sm font-medium text-gray-700 mb-2">Evidence (Optional)</label>
                    <input type="file"
                           id="evidenceFile"
                           name="evidence_file"
                           accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                           :class="{ 'border-red-500': errors.evidence_file }">
                    <p class="mt-1 text-sm text-gray-500">Upload screenshots or documents as evidence (Max: 10MB)</p>
                    <p x-show="errors.evidence_file" x-text="errors.evidence_file?.[0]" class="mt-1 text-sm text-red-600"></p>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
            <button type="button"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    @click="showModal = false">
                Cancel
            </button>
            <button type="button"
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    :disabled="loading"
                    @click="submitReport">
                <span x-show="!loading">Submit Report</span>
                <span x-show="loading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                </span>
            </button>
        </div>
    </div>
</div>

<script>
// Global function to open report modal (called from report buttons)
function openReportModal(userId, userName) {
    // Find the modal component and trigger it
    const modalElement = document.querySelector('[x-data*="showModal"]');
    if (modalElement) {
        // Set the user data
        document.getElementById('reportedUserId').value = userId;
        document.getElementById('reportedUserName').textContent = userName || 'this user';

        // Reset form
        document.getElementById('reportUserForm').reset();

        // Trigger Alpine.js to show modal
        modalElement._x_dataStack[0].showModal = true;
        modalElement._x_dataStack[0].showSuccess = false;
        modalElement._x_dataStack[0].showError = false;
        modalElement._x_dataStack[0].errors = {};
    }
}
</script>
