@props([
    'title' => 'Journey to Meaningful Connections',
    'subtitle' => 'From feeling alone to finding your perfect activity partner',
    'description' => 'Discover how SettingWala helps you connect with like-minded people through shared activities and interests.',
    'showSteps' => true,
    'customClass' => '',
    'backgroundGradient' => 'linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%)'
])

<!-- Animated SVG Hero Section -->
<div class="relative overflow-hidden py-16 lg:py-24 {{ $customClass }}" style="background: {{ $backgroundGradient }}">
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header Content -->
        <div class="text-center mb-16 animate-slide-up">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-gray-800">
                {!! $title !!}
            </h1>
            @if(!empty($subtitle))
                <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light mb-4">
                    {!! $subtitle !!}
                </p>
            @endif
            @if(!empty($description))
                <p class="text-lg text-gray-500 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>

        @if($showSteps)
            <!-- Animated Journey Steps -->
            <div class="relative max-w-6xl mx-auto">
                <!-- SVG Container -->
                <div class="relative">
                    <!-- Step Cards Container -->
                    <div class="grid md:grid-cols-3 gap-8 md:gap-12 relative z-10">

                        <!-- Step 01: Feeling Alone -->
                        <div class="step-card-container animate-slide-up" style="animation-delay: 0.2s;">
                            <div class="relative">
                                <!-- Step Number Badge -->
                                <div class="absolute -top-4 -left-4 z-20">
                                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center shadow-lg animate-pulse-soft">
                                        <span class="text-white font-bold text-lg">01</span>
                                    </div>
                                </div>

                                <!-- Card -->
                                <div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                                    <!-- Icon Container -->
                                    <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <!-- Lonely Person SVG -->
                                        <svg class="w-12 h-12 text-gray-600" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <!-- Person with subtle animation -->
                                            <g style="animation: svg-character-bounce 3s ease-in-out infinite;">
                                                <circle cx="50" cy="30" r="12" fill="currentColor" opacity="0.8"/>
                                                <path d="M30 70 C30 55, 38 50, 50 50 C62 50, 70 55, 70 70" fill="currentColor" opacity="0.8"/>
                                                <!-- Sad expression -->
                                                <path d="M45 32 Q50 36 55 32" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.6"/>
                                                <circle cx="46" cy="28" r="1.5" fill="currentColor" opacity="0.6"/>
                                                <circle cx="54" cy="28" r="1.5" fill="currentColor" opacity="0.6"/>
                                            </g>
                                            <!-- Question mark with pulse -->
                                            <g style="animation: badge-pulse 2s ease-in-out infinite;" opacity="0.6">
                                                <circle cx="75" cy="25" r="8" fill="none" stroke="currentColor" stroke-width="2"/>
                                                <path d="M72 22 C72 20, 73 19, 75 19 C77 19, 78 20, 78 22 C78 23, 77 24, 75 24" fill="none" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="75" cy="28" r="1" fill="currentColor"/>
                                            </g>
                                        </svg>
                                    </div>

                                    <h3 class="text-xl font-bold text-gray-800 mb-3 text-center">Bas Kar Pagle, Rulayega Kya?</h3>
                                    <p class="text-gray-600 text-center leading-relaxed">
                                        Akele akele ghoom rahe ho, koi interesting person nahi mil raha? "Koi toh mile yaar" wala feeling aa raha hai na?
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Step 02: Discover SettingWala -->
                        <div class="step-card-container animate-slide-up" style="animation-delay: 0.4s;">
                            <div class="relative">
                                <!-- Step Number Badge -->
                                <div class="absolute -top-4 -left-4 z-20">
                                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center shadow-lg animate-pulse-soft" style="animation-delay: 0.5s;">
                                        <span class="text-white font-bold text-lg">02</span>
                                    </div>
                                </div>

                                <!-- Card -->
                                <div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                                    <!-- Icon Container -->
                                    <div class="w-24 h-24 mx-auto mb-6 bg-indigo-100 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <!-- Mobile with Profiles SVG -->
                                        <svg class="w-12 h-12 text-indigo-600" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <!-- Phone with glow effect -->
                                            <g style="animation: svg-phone-glow 2s ease-in-out infinite;">
                                                <rect x="35" y="15" width="30" height="50" rx="5" fill="currentColor" opacity="0.9"/>
                                                <rect x="37" y="20" width="26" height="35" rx="2" fill="white"/>
                                                <circle cx="50" cy="60" r="2" fill="white"/>
                                            </g>

                                            <!-- Profile Cards with staggered animation -->
                                            <g style="animation: fade-in-up 1s ease-out infinite alternate;">
                                                <rect x="40" y="25" width="8" height="6" rx="1" fill="currentColor" opacity="0.7"/>
                                                <rect x="52" y="25" width="8" height="6" rx="1" fill="currentColor" opacity="0.7"/>
                                            </g>
                                            <g style="animation: fade-in-up 1s ease-out infinite alternate; animation-delay: 0.5s;">
                                                <rect x="40" y="35" width="8" height="6" rx="1" fill="currentColor" opacity="0.7"/>
                                                <rect x="52" y="35" width="8" height="6" rx="1" fill="currentColor" opacity="0.7"/>
                                            </g>

                                            <!-- Chat bubbles with bounce -->
                                            <g style="animation: svg-character-bounce 2s ease-in-out infinite;">
                                                <circle cx="70" cy="30" r="6" fill="currentColor" opacity="0.6"/>
                                                <circle cx="25" cy="40" r="5" fill="currentColor" opacity="0.6"/>
                                            </g>
                                        </svg>
                                    </div>

                                    <h3 class="text-xl font-bold text-gray-800 mb-3 text-center">SettingWala Mein Aa Jao!</h3>
                                    <p class="text-gray-600 text-center leading-relaxed">
                                        Yahan sab cool log hain! Verified profiles dekho aur "Ye toh meri type ka hai" wala feeling lo. Guaranteed maza!
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Step 03: Activity Partner Found -->
                        <div class="step-card-container animate-slide-up" style="animation-delay: 0.6s;">
                            <div class="relative">
                                <!-- Step Number Badge -->
                                <div class="absolute -top-4 -left-4 z-20">
                                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center shadow-lg animate-pulse-soft" style="animation-delay: 1s;">
                                        <span class="text-white font-bold text-lg">03</span>
                                    </div>
                                </div>

                                <!-- Card -->
                                <div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                                    <!-- Icon Container -->
                                    <div class="w-24 h-24 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <!-- Two People Activity SVG -->
                                        <svg class="w-12 h-12 text-green-600" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <!-- Person 1 with bounce -->
                                            <g style="animation: svg-character-bounce 2s ease-in-out infinite;">
                                                <circle cx="35" cy="25" r="8" fill="currentColor" opacity="0.8"/>
                                                <path d="M22 55 C22 45, 27 40, 35 40 C43 40, 48 45, 48 55" fill="currentColor" opacity="0.8"/>
                                                <!-- Happy expression -->
                                                <path d="M30 30 Q35 25 40 30" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.6"/>
                                                <circle cx="32" cy="23" r="1" fill="currentColor" opacity="0.6"/>
                                                <circle cx="38" cy="23" r="1" fill="currentColor" opacity="0.6"/>
                                            </g>

                                            <!-- Person 2 with bounce (delayed) -->
                                            <g style="animation: svg-character-bounce 2s ease-in-out infinite; animation-delay: 0.3s;">
                                                <circle cx="65" cy="25" r="8" fill="currentColor" opacity="0.8"/>
                                                <path d="M52 55 C52 45, 57 40, 65 40 C73 40, 78 45, 78 55" fill="currentColor" opacity="0.8"/>
                                                <!-- Happy expression -->
                                                <path d="M60 30 Q65 25 70 30" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.6"/>
                                                <circle cx="62" cy="23" r="1" fill="currentColor" opacity="0.6"/>
                                                <circle cx="68" cy="23" r="1" fill="currentColor" opacity="0.6"/>
                                            </g>

                                            <!-- Activity elements (badminton rackets) with wiggle -->
                                            <g style="animation: svg-activity-wiggle 1.5s ease-in-out infinite;">
                                                <ellipse cx="30" cy="65" rx="3" ry="8" fill="currentColor" opacity="0.6" transform="rotate(-20 30 65)"/>
                                                <ellipse cx="70" cy="65" rx="3" ry="8" fill="currentColor" opacity="0.6" transform="rotate(20 70 65)"/>
                                                <circle cx="50" cy="70" r="2" fill="currentColor" opacity="0.6"/>
                                            </g>

                                            <!-- Celebration sparkles -->
                                            <g style="animation: pulse-soft 1s ease-in-out infinite;" opacity="0.4">
                                                <circle cx="20" cy="20" r="1" fill="currentColor"/>
                                                <circle cx="80" cy="15" r="1" fill="currentColor"/>
                                                <circle cx="85" cy="35" r="1" fill="currentColor"/>
                                                <circle cx="15" cy="40" r="1" fill="currentColor"/>
                                            </g>
                                        </svg>
                                    </div>

                                    <h3 class="text-xl font-bold text-gray-800 mb-3 text-center">Setting Ho Gayi! 🔥</h3>
                                    <p class="text-gray-600 text-center leading-relaxed">
                                        "Ye bhi theek hai" se "Ye toh mast hai" tak ka safar! Real mein mil ke activities karo, setting complete!
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connecting Arrows (Hidden on mobile) -->
                    <div class="hidden md:block absolute top-1/2 left-0 right-0 transform -translate-y-1/2 z-0">
                        <!-- Arrow 1 to 2 -->
                        <svg class="absolute left-1/4 transform -translate-x-1/2" width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 30 Q60 10 110 30" stroke="#FF4B2B" stroke-width="2" stroke-dasharray="5,5" fill="none" opacity="0.6">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
                            </path>
                            <path d="M105 25 L110 30 L105 35" stroke="#FF4B2B" stroke-width="2" fill="none" opacity="0.6"/>
                        </svg>

                        <!-- Arrow 2 to 3 -->
                        <svg class="absolute right-1/4 transform translate-x-1/2" width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 30 Q60 10 110 30" stroke="#FF4B2B" stroke-width="2" stroke-dasharray="5,5" fill="none" opacity="0.6">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite" begin="0.5s"/>
                            </path>
                            <path d="M105 25 L110 30 L105 35" stroke="#FF4B2B" stroke-width="2" fill="none" opacity="0.6"/>
                        </svg>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
