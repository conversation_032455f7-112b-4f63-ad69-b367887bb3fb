@section('title', 'Contact Us')
@section('description', 'Get in touch with the SettingWala team. We are here to help with any questions, concerns, or feedback about our friendship platform.')

<x-app-layout>
    <!-- Modern Hero Section -->
    <section class="bg-white relative overflow-hidden">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20 py-20">
            <div class="text-center max-w-4xl mx-auto">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-8">
                    <i data-lucide="mail" class="w-10 h-10 text-white"></i>
                </div>
                <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                    Contact Us
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    We're here to help! Reach out to our friendly support team with any questions, concerns, or feedback about your SettingWala experience.
                </p>
            </div>
        </div>
    </section>

    <div class="min-h-screen bg-gray-50">
        <!-- Contact Methods -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="mb-20">
                <!-- Email Support -->
                <div class="bg-white rounded-3xl p-12 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="relative inline-block">
                        <div class="w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                            <i data-lucide="mail" class="w-12 h-12 text-white"></i>
                        </div>
                        <!-- Floating notification dot -->
                        <div class="absolute -top-2 -right-2 w-5 h-5 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">Email Support</h3>
                    <p class="text-gray-600 mb-8 leading-relaxed text-lg">Send us an email and we'll get back to you as soon as possible</p>
                    <a href="mailto:<EMAIL>" class="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-semibold text-xl transition-colors duration-300">
                        <i data-lucide="send" class="w-6 h-6 mr-3"></i>
                        <EMAIL>
                    </a>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="bg-white rounded-3xl p-12 shadow-lg border border-gray-100">
                <div class="text-center mb-12">
                    <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="message-circle" class="w-8 h-8 text-white"></i>
                    </div>
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">Send us a Message</h2>
                    <p class="text-gray-600 text-lg">Fill out the form below and we'll get back to you within 24 hours</p>
                </div>

                <form id="contactForm" class="space-y-8">
                    @csrf
                    <div id="form-messages" class="hidden"></div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label for="first_name" class="block text-sm font-bold text-gray-900 mb-3">First Name</label>
                            <input type="text" id="first_name" name="first_name" class="w-full px-6 py-4 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 text-lg" required>
                            <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-bold text-gray-900 mb-3">Last Name</label>
                            <input type="text" id="last_name" name="last_name" class="w-full px-6 py-4 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 text-lg" required>
                            <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-bold text-gray-900 mb-3">Email Address</label>
                        <input type="email" id="email" name="email" class="w-full px-6 py-4 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 text-lg" required>
                        <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-bold text-gray-900 mb-3">Subject</label>
                        <select id="subject" name="subject" class="w-full px-6 py-4 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 text-lg" required>
                            <option value="">Select a subject</option>
                            <option value="account">Account Issues</option>
                            <option value="payment">Payment & Billing</option>
                            <option value="events">Events & Meetings</option>
                            <option value="safety">Safety Concerns</option>
                            <option value="technical">Technical Support</option>
                            <option value="feedback">Feedback & Suggestions</option>
                            <option value="other">Other</option>
                        </select>
                        <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-bold text-gray-900 mb-3">Message</label>
                        <textarea id="message" name="message" rows="6" class="w-full px-6 py-4 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 resize-none text-lg" placeholder="Please describe your question or concern in detail..." required></textarea>
                        <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                        <div class="text-sm text-gray-600 mt-2">
                            <span id="message-count">0</span>/2000 characters
                        </div>
                    </div>

                    <div class="flex items-start">
                        <input type="checkbox" id="privacy" name="privacy" class="mt-1 mr-4 rounded border-gray-300 text-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                        <label for="privacy" class="text-sm text-gray-700">
                            I agree to the <a href="{{ route('static.privacy-policy') }}" class="text-indigo-600 hover:text-indigo-700 underline font-semibold">Privacy Policy</a> and consent to the processing of my personal data.
                        </label>
                        <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                    </div>

                    <button type="submit" id="submitBtn" class="w-full px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-bold text-lg rounded-2xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 shadow-lg hover:shadow-xl">
                        <span class="btn-text flex items-center justify-center">
                            Send Message
                            <i data-lucide="send" class="w-5 h-5 ml-2"></i>
                        </span>
                        <span class="btn-loading hidden flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Sending...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Emergency Contact -->
            <div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-3xl p-12 mt-20">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-8">
                        <i data-lucide="alert-triangle" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-red-800 mb-6">Emergency or Safety Concerns?</h3>
                    <p class="text-red-700 mb-8 text-lg leading-relaxed max-w-2xl mx-auto">
                        If you're experiencing harassment, threats, or any safety issues, please contact us immediately or reach out to local authorities.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-6 justify-center">
                        <a href="mailto:<EMAIL>" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-red-700 text-white font-bold text-lg rounded-2xl hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <i data-lucide="shield-alert" class="w-5 h-5 mr-2"></i>
                            Report Safety Issue
                        </a>
                        <a href="tel:100" class="inline-flex items-center px-8 py-4 border-2 border-red-300 text-red-700 font-bold text-lg rounded-2xl hover:bg-red-50 transition-all duration-300">
                            <i data-lucide="phone" class="w-5 h-5 mr-2"></i>
                            Emergency: 100
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('contactForm');
            const submitBtn = document.getElementById('submitBtn');
            const messageTextarea = document.getElementById('message');
            const messageCount = document.getElementById('message-count');

            // Character counter for message
            messageTextarea.addEventListener('input', function() {
                const count = this.value.length;
                messageCount.textContent = count;

                if (count > 2000) {
                    messageCount.parentElement.classList.add('text-red-500');
                    messageCount.parentElement.classList.remove('text-warm-600');
                } else {
                    messageCount.parentElement.classList.remove('text-red-500');
                    messageCount.parentElement.classList.add('text-warm-600');
                }
            });

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous errors
                clearErrors();

                // Show loading state
                setLoadingState(true);

                // Prepare form data
                const formData = new FormData(form);

                // Submit form
                fetch('{{ route("contact.store") }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    setLoadingState(false);

                    if (data.success) {
                        showSuccessMessage(data.message);
                        form.reset();
                        messageCount.textContent = '0';
                    } else {
                        if (data.errors) {
                            showFieldErrors(data.errors);
                        } else {
                            showErrorMessage(data.message || 'An error occurred. Please try again.');
                        }
                    }
                })
                .catch(error => {
                    setLoadingState(false);
                    showErrorMessage('Network error. Please check your connection and try again.');
                });
            });

            function setLoadingState(loading) {
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');
                const btnIcon = submitBtn.querySelector('.btn-icon');

                if (loading) {
                    submitBtn.disabled = true;
                    btnText.classList.add('hidden');
                    btnLoading.classList.remove('hidden');
                    btnIcon.classList.add('hidden');
                } else {
                    submitBtn.disabled = false;
                    btnText.classList.remove('hidden');
                    btnLoading.classList.add('hidden');
                    btnIcon.classList.remove('hidden');
                }
            }

            function clearErrors() {
                const errorMessages = document.querySelectorAll('.error-message');
                errorMessages.forEach(msg => {
                    msg.classList.add('hidden');
                    msg.textContent = '';
                });

                const formInputs = document.querySelectorAll('input, select, textarea');
                formInputs.forEach(input => {
                    input.classList.remove('border-red-500');
                });

                const messagesDiv = document.getElementById('form-messages');
                messagesDiv.classList.add('hidden');
                messagesDiv.innerHTML = '';
            }

            function showFieldErrors(errors) {
                Object.keys(errors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    const errorDiv = input.parentElement.querySelector('.error-message');

                    if (input && errorDiv) {
                        input.classList.add('border-red-500');
                        errorDiv.textContent = errors[field][0];
                        errorDiv.classList.remove('hidden');
                    }
                });
            }

            function showSuccessMessage(message) {
                const messagesDiv = document.getElementById('form-messages');
                messagesDiv.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <p class="text-green-800 font-medium">${message}</p>
                        </div>
                    </div>
                `;
                messagesDiv.classList.remove('hidden');
                messagesDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            function showErrorMessage(message) {
                const messagesDiv = document.getElementById('form-messages');
                messagesDiv.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-red-800 font-medium">${message}</p>
                        </div>
                    </div>
                `;
                messagesDiv.classList.remove('hidden');
                messagesDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    </script>
    @endpush
</x-app-layout>
