@section('title', 'Safety Tips')
@section('description', 'Stay safe while making friends online with SettingWala. Learn essential safety tips for online friendships, meeting in person, and protecting your privacy.')

<x-app-layout>
    <!-- Modern Hero Section -->
    <section class="bg-white relative overflow-hidden">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20 py-20">
            <div class="text-center max-w-4xl mx-auto">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-red-600 to-red-700 rounded-full mb-8">
                    <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                </div>
                <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                    Safety Tips
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Your safety is our top priority. Follow these essential guidelines to ensure a safe and enjoyable dating experience on SettingWala.
                </p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <!-- Safety First Banner -->
            <div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-3xl p-12 mb-20">
                <div class="flex items-center justify-center mb-8">
                    <div class="w-20 h-20 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-6">
                        <i data-lucide="alert-triangle" class="w-10 h-10 text-white"></i>
                    </div>
                    <h2 class="text-4xl font-bold text-red-800">Safety First</h2>
                </div>
                <p class="text-xl text-red-700 text-center max-w-4xl mx-auto leading-relaxed">
                    While SettingWala provides a secure platform with verified profiles, it's important to stay vigilant and follow these safety guidelines for the best experience.
                </p>
            </div>

            <!-- Safety Categories -->
            <div class="space-y-20">
                <!-- Online Safety -->
                <div class="bg-white rounded-3xl p-12 lg:p-16 shadow-lg">
                    <div class="flex items-center mb-12">
                        <div class="w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-2xl flex items-center justify-center mr-8">
                            <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                        </div>
                        <h2 class="text-4xl font-bold text-gray-900">Online Safety</h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-12">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Protect Your Personal Information</h3>
                            <ul class="space-y-4 text-gray-600">
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Never share your full name, address, or phone number in your profile
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Avoid sharing financial information or workplace details
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Use the platform's messaging system initially
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Be cautious about sharing social media profiles
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Red Flags to Watch For</h3>
                            <ul class="space-y-4 text-gray-600">
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="x" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Requests for money or financial assistance
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="x" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Profiles with limited or suspicious photos
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="x" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Immediate requests to move off the platform
                                </li>
                                <li class="flex items-start">
                                    <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 mt-0.5 flex-shrink-0">
                                        <i data-lucide="x" class="w-3 h-3 text-white"></i>
                                    </div>
                                    Overly aggressive or inappropriate messages
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Meeting in Person -->
                <div class="card-romantic p-8 lg:p-12">
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-blush-500 to-blush-600 rounded-2xl flex items-center justify-center mr-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-warm-900">Meeting in Person</h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-xl font-semibold text-warm-900 mb-4">First Meeting Guidelines</h3>
                            <ul class="space-y-3 text-warm-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Meet in public places during daytime
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Inform a friend or family member about your plans
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Arrange your own transportation
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Keep your phone charged and accessible
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold text-warm-900 mb-4">Trust Your Instincts</h3>
                            <ul class="space-y-3 text-warm-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    If something feels off, leave immediately
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Don't feel obligated to stay if uncomfortable
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Watch your drink and never leave it unattended
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-blush-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Set boundaries and communicate them clearly
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Event Safety -->
                <div class="card-romantic p-8 lg:p-12">
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-mauve-500 to-mauve-600 rounded-2xl flex items-center justify-center mr-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-warm-900">Event Safety</h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-xl font-semibold text-warm-900 mb-4">Before the Event</h3>
                            <ul class="space-y-3 text-warm-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Research the event location and venue
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Plan your route and transportation
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Share event details with someone you trust
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Bring emergency contact information
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold text-warm-900 mb-4">During the Event</h3>
                            <ul class="space-y-3 text-warm-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Stay in well-lit, populated areas
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Don't leave with someone you just met
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Report any inappropriate behavior to organizers
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-mauve-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Check in with your emergency contact
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contacts -->
            <div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-3xl p-8 mt-16">
                <h2 class="text-3xl font-bold text-red-800 text-center mb-8">Emergency Contacts</h2>
                <div class="grid md:grid-cols-2 gap-8 text-center max-w-4xl mx-auto">
                    <div>
                        <div class="text-2xl font-bold text-red-700 mb-2">Emergency Services</div>
                        <div class="text-red-600">Call 100 (Police)</div>
                        <div class="text-red-600">Call 108 (Ambulance)</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-red-700 mb-2">Women's Helpline</div>
                        <div class="text-red-600">Call 1091</div>
                        <div class="text-red-600">24/7 Support</div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-24">
                <div class="bg-gradient-to-r from-red-100 to-pink-100 border border-red-200 rounded-3xl p-16">
                    <div class="w-20 h-20 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-8">
                        <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                    </div>
                    <h2 class="text-4xl font-bold mb-6 text-red-800">Stay Safe, Find Love</h2>
                    <p class="text-xl mb-10 max-w-3xl mx-auto leading-relaxed text-red-700">
                        Following these safety guidelines will help ensure you have a positive and secure experience on SettingWala.
                    </p>
                    @guest
                        <a href="{{ route('login') }}" class="inline-flex items-center px-10 py-5 bg-gradient-to-r from-red-500 to-red-600 text-white font-bold text-xl rounded-2xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                            Join Safely
                            <i data-lucide="arrow-right" class="w-6 h-6 ml-3"></i>
                        </a>
                    @else
                        @if(\App\Models\MeetingAddress::hasAvailableEvents())
                            <a href="{{ route('event.address') }}" class="inline-flex items-center px-10 py-5 bg-gradient-to-r from-red-500 to-red-600 text-white font-bold text-xl rounded-2xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                                Browse Safe Events
                                <i data-lucide="arrow-right" class="w-6 h-6 ml-3"></i>
                            </a>
                        @else
                            <a href="{{ route('home') }}" class="inline-flex items-center px-10 py-5 bg-gradient-to-r from-red-500 to-red-600 text-white font-bold text-xl rounded-2xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                                Go to Dashboard
                                <i data-lucide="arrow-right" class="w-6 h-6 ml-3"></i>
                            </a>
                        @endif
                    @endguest
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
