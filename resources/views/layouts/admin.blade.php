<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Admin Panel</title>

    <!-- Favicon -->
    @if(App\Models\Setting::get('favicon'))
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . App\Models\Setting::get('favicon')) }}">
    @else
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">



    <!-- Fallback for Bootstrap Icons -->
    <style>
        .bi::before {
            display: inline-block;
            font-family: "bootstrap-icons" !important;
            font-style: normal;
            font-weight: normal !important;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            vertical-align: -.125em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Ensure icons are visible */
        .bi {
            font-family: "bootstrap-icons" !important;
        }
    </style>

    <!-- Bootstrap CSS (CDN) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Admin Styles -->
    <style>
        :root {
            --bs-primary: #3b82f6;
            --bs-primary-rgb: 59, 130, 246;
            --bs-secondary: #6b7280;
            --bs-success: #10b981;
            --bs-danger: #ef4444;
            --bs-warning: #f59e0b;
            --bs-info: #06b6d4;
            --bs-light: #f8fafc;
            --bs-dark: #1f2937;
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 0.75rem;
            font-size: 1.5rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
            height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            color: rgba(255,255,255,0.6);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 1.5rem;
            margin-bottom: 0.5rem;
        }

        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-right: 3px solid white;
        }

        .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: margin-left 0.3s ease;
            min-height: 100vh;
        }

        .main-content.sidebar-collapsed {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Header */
        .top-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Cards */
        .card {
            border: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 0.75rem;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
        }

        /* Buttons */
        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
        }

        /* Button animations for meeting events */
        .btn-toggle-event-status {
            transition: all 0.3s ease;
        }

        .btn-toggle-event-status:hover {
            transform: scale(1.05);
        }

        .btn-delete-event {
            transition: all 0.3s ease;
        }

        .btn-delete-event:hover {
            transform: scale(1.05);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Custom Scrollbar */
        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }

        /* Timeline Styles */
        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }

        .timeline-marker {
            position: absolute;
            left: -2rem;
            top: 0;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #e5e7eb;
        }

        .timeline-content {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 3px solid var(--bs-primary);
        }

        /* Table Styles */
        .table-hover tbody tr:hover {
            background-color: rgba(var(--bs-primary-rgb), 0.05);
        }

        /* Badge Styles */
        .badge {
            font-weight: 500;
        }
    </style>

    <!-- Page-specific styles -->
    @stack('styles')
    @yield('styles')
</head>
<body>
    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <a href="{{ route('admin.dashboard') }}" class="sidebar-brand">
                <i class="bi bi-shield-check"></i>
                <span class="sidebar-text">Admin Panel</span>
            </a>
        </div>

        <!-- Navigation -->
        <div class="sidebar-nav">
            <!-- Main Section -->
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <a href="{{ route('admin.dashboard') }}" data-page="dashboard"
                   class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                    <i class="bi bi-speedometer2"></i>
                    <span class="sidebar-text">Dashboard</span>
                </a>
            </div>

            <!-- User Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">User Management</div>
                <a href="{{ route('admin.users') }}" data-page="users"
                   class="nav-link {{ request()->routeIs('admin.users') ? 'active' : '' }}">
                    <i class="bi bi-people"></i>
                    <span class="sidebar-text">Users</span>
                </a>
                <a href="{{ route('admin.sugar-partners.index') }}" data-page="sugar-partners"
                   class="nav-link {{ request()->routeIs('admin.sugar-partners.*') ? 'active' : '' }}">
                    <i class="bi bi-heart"></i>
                    <span class="sidebar-text">Sugar Partners</span>
                </a>
                <a href="{{ route('admin.reports.index') }}" data-page="reports"
                   class="nav-link {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}">
                    <i class="bi bi-flag"></i>
                    <span class="sidebar-text">User Reports</span>
                </a>
                <a href="{{ route('admin.management-users') }}" data-page="management-users"
                   class="nav-link {{ request()->routeIs('admin.management-users*') ? 'active' : '' }}">
                    <i class="bi bi-shield-check"></i>
                    <span class="sidebar-text">Management Users</span>
                </a>
            </div>

            <!-- Content Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">Content</div>
                <a href="{{ route('admin.meeting-events.index') }}" data-page="meeting-events"
                   class="nav-link {{ request()->routeIs('admin.meeting-events.*') ? 'active' : '' }}">
                    <i class="bi bi-calendar-event"></i>
                    <span class="sidebar-text">Meeting Events</span>
                </a>
            </div>

            <!-- Communication Section -->
            <div class="nav-section">
                <div class="nav-section-title">Communication</div>
                <a href="{{ route('admin.notifications.index') }}" data-page="notifications"
                   class="nav-link {{ request()->routeIs('admin.notifications.*') ? 'active' : '' }}">
                    <i class="bi bi-bell"></i>
                    <span class="sidebar-text">Notifications</span>
                </a>
                <a href="{{ route('admin.contact.index') }}" data-page="contact"
                   class="nav-link {{ request()->routeIs('admin.contact.*') ? 'active' : '' }}">
                    <i class="bi bi-envelope"></i>
                    <span class="sidebar-text">Contact Forms</span>
                </a>
            </div>

            <!-- Financial Section -->
            <div class="nav-section">
                <div class="nav-section-title">Financial</div>
                <a href="{{ route('admin.revenue.index') }}" data-page="revenue"
                   class="nav-link {{ request()->routeIs('admin.revenue.*') ? 'active' : '' }}">
                    <i class="bi bi-currency-rupee"></i>
                    <span class="sidebar-text">Revenue</span>
                </a>
                <a href="{{ route('admin.escrow.index') }}" data-page="escrow"
                   class="nav-link {{ request()->routeIs('admin.escrow.*') ? 'active' : '' }}">
                    <i class="bi bi-shield-lock"></i>
                    <span class="sidebar-text">Escrow</span>
                </a>
                <a href="{{ route('admin.withdrawals.index') }}" data-page="withdrawals"
                   class="nav-link {{ request()->routeIs('admin.withdrawals.*') ? 'active' : '' }}">
                    <i class="bi bi-cash-stack"></i>
                    <span class="sidebar-text">Withdrawals</span>
                </a>
            </div>

            <!-- Subscription Section -->
            <div class="nav-section">
                <div class="nav-section-title">Subscriptions</div>
                <a href="{{ route('admin.subscription-plans.index') }}" data-page="subscription-plans"
                   class="nav-link {{ request()->routeIs('admin.subscription-plans.*') ? 'active' : '' }}">
                    <i class="bi bi-credit-card"></i>
                    <span class="sidebar-text">Subscription Plans</span>
                </a>
            </div>

            <!-- System Section -->
            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <a href="{{ route('admin.features.index') }}" data-page="features"
                   class="nav-link {{ request()->routeIs('admin.features.*') ? 'active' : '' }}">
                    <i class="bi bi-toggles2"></i>
                    <span class="sidebar-text">Features</span>
                </a>
                <a href="{{ route('admin.settings.index') }}" data-page="settings"
                   class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                    <i class="bi bi-gear"></i>
                    <span class="sidebar-text">Settings</span>
                </a>
            </div>
        </div>

        <!-- User Info & Logout -->
        <div class="sidebar-footer" style="position: absolute; bottom: 0; left: 0; right: 0; padding: 1rem; border-top: 1px solid rgba(255,255,255,0.1); background: rgba(255,255,255,0.05);">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    <div class="rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 40px; height: 40px; background: rgba(255,255,255,0.2);">
                        <span class="text-white fw-semibold">{{ substr(auth()->user()->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="ms-3 flex-fill sidebar-text">
                    <p class="text-white mb-0 fw-medium" style="font-size: 0.875rem;">{{ auth()->user()->name }}</p>
                    <p class="mb-0" style="font-size: 0.75rem; color: rgba(255,255,255,0.6);">Administrator</p>
                </div>
                <div class="ms-3">
                    <form method="POST" action="{{ route('admin.logout') }}">
                        @csrf
                        <button type="submit" class="btn btn-link text-white p-1" title="Logout" style="text-decoration: none;">
                            <i class="bi bi-box-arrow-right"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Mobile menu button -->
        <div class="d-md-none bg-white border-bottom p-3">
            <button class="btn btn-outline-secondary" id="mobileMenuButton" type="button">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <!-- Top Header -->
        <header class="top-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="flex-fill">
                    <!-- Breadcrumbs -->
                    <nav aria-label="breadcrumb" class="mb-2">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}" class="text-decoration-none">
                                    <i class="bi bi-house me-1"></i>Dashboard
                                </a>
                            </li>
                            @hasSection('breadcrumbs')
                                @yield('breadcrumbs')
                            @endif
                        </ol>
                    </nav>

                    <!-- Page Title -->
                    <h1 class="h3 mb-0 fw-bold text-dark">
                        @yield('page-title', 'Admin Dashboard')
                    </h1>
                    @hasSection('page-description')
                        <p class="text-muted mb-0 mt-1">
                            @yield('page-description')
                        </p>
                    @endif
                </div>

                <!-- Header Actions -->
                <div class="d-flex align-items-center gap-3">
                    <!-- View Site -->
                    <a href="{{ url('/') }}" class="btn btn-outline-primary" title="View Site">
                        <i class="bi bi-box-arrow-up-right me-1"></i>
                        <span class="d-none d-sm-inline">View Site</span>
                    </a>

                    @hasSection('header-actions')
                        @yield('header-actions')
                    @endif
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="flex-fill" style="background-color: #f8fafc; padding: 2rem;">
            <div class="container-fluid">
                <!-- Flash Messages -->
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <div>{{ session('success') }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-circle-fill me-2"></i>
                            <div>{{ session('error') }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if (session('warning'))
                    <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <div>{{ session('warning') }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if (session('info'))
                    <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>{{ session('info') }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <!-- Page Content -->
                <div class="fade-in" id="pageContent">
                    @yield('content')
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userDetailsModalLabel">
                        <i class="bi bi-person-circle me-2"></i>User Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="userDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading user details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (Local) -->
    <script src="{{ asset('js/jquery-3.6.0.min.js') }}"></script>

    <!-- Bootstrap JavaScript (CDN) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="{{ asset('js/admin.js') }}?v={{ time() }}"></script>

    <script>
        // Simple DOM ready handler
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize basic elements
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mainContent = document.getElementById('mainContent');

            // Mobile sidebar toggle
            function toggleSidebar() {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            }

            function closeSidebar() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            }

            // Event listeners
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', toggleSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Close sidebar on window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    closeSidebar();
                }
            });

            // Sidebar text toggle for collapsed state
            function updateSidebarText() {
                const sidebarTexts = document.querySelectorAll('.sidebar-text');
                const isCollapsed = sidebar.classList.contains('collapsed');

                sidebarTexts.forEach(text => {
                    text.style.display = isCollapsed ? 'none' : 'inline';
                });
            }

            // Basic initialization complete
        });

        // Simple toast function
        function showToast(message, type = 'success') {
            // Toast functionality
        }

        // Simple functions - no loading modals
        function showLoading() {
            // Loading functionality
        }

        function hideLoading() {
            // Loading complete
        }
    </script>

    <!-- Page-specific scripts -->
    @stack('scripts')
</body>
</html>
