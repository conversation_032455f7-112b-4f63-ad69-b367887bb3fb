<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="{{ App\Models\Setting::get('theme_color', '#C9B6E4') }}">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="SettingWala">
        <meta name="mobile-web-app-capable" content="yes">

        <!-- Mobile App Icon -->
        @if(App\Models\Setting::get('mobile_icon'))
            <meta name="mobile-app-icon" content="{{ asset('storage/' . App\Models\Setting::get('mobile_icon')) }}">
        @endif

        <!-- PWA Manifest -->
        <link rel="manifest" href="{{ asset('manifest.json') }}">

        <!-- Apple Touch Icons -->
        @php
            $mobileIcon = App\Models\Setting::get('mobile_icon');
            $iconUrl = $mobileIcon ? asset('storage/' . $mobileIcon) : asset('images/icon-192x192.png');
        @endphp
        <link rel="apple-touch-icon" href="{{ $iconUrl }}">
        <link rel="apple-touch-icon" sizes="152x152" href="{{ $iconUrl }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ $iconUrl }}">

        <title>{{ config('app.name', 'SettingWala') }} - @yield('title', 'Welcome')</title>
        <meta name="description" content="@yield('description', 'Join thousands who found love through our platform. Sign in or create your account to start your journey.')">



        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap"></noscript>

        <!-- Tailwind CSS CDN -->
        <script>
            // Suppress Tailwind CDN production warning
            if (typeof console !== 'undefined' && console.warn) {
                const originalWarn = console.warn;
                console.warn = function(...args) {
                    if (args[0] && typeof args[0] === 'string' && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
                        return; // Suppress this specific warning
                    }
                    originalWarn.apply(console, args);
                };
            }

            tailwind.config = {
                mode: 'jit',
                corePlugins: {
                    preflight: false,
                },
                theme: {
                    extend: {
                        animation: {
                            'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                            'bounce': 'bounce 1s infinite',
                        }
                    }
                }
            }
        </script>
        <script src="https://cdn.tailwindcss.com"></script>

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

        @stack('styles')
    </head>
    <body class="font-sans text-gray-900 antialiased min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 relative overflow-hidden">
            <!-- Background Decorations -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-30 animate-pulse"></div>
                <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-slate-100 to-blue-100 rounded-full opacity-30 animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-full opacity-20 animate-bounce" style="animation-duration: 3s;"></div>
                <div class="absolute top-1/4 right-1/4 w-24 h-24 bg-gradient-to-br from-slate-100 to-indigo-100 rounded-full opacity-20 animate-bounce" style="animation-delay: 2s; animation-duration: 3s;"></div>
            </div>

            <!-- Simple Logo/Brand -->
            <div class="relative z-10 mb-8">
                <a href="{{ url('/') }}" class="group">
                    <div class="flex items-center justify-center">
                        @php
                            $headerLogo = App\Models\Setting::get('header_logo');
                        @endphp

                        @if($headerLogo)
                            <!-- Uploaded Logo -->
                            <div class="flex items-center">
                                <img src="{{ asset('storage/' . $headerLogo) }}"
                                     alt="{{ config('app.name', 'SettingWala') }}"
                                     class="h-16 w-auto object-contain group-hover:scale-105 transition-transform duration-200 drop-shadow-lg">
                            </div>
                        @else
                            <!-- Default Heart Icon Logo -->
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-200">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h1 class="text-2xl font-bold text-gray-800 drop-shadow-sm">{{ config('app.name', 'SettingWala') }}</h1>
                            </div>
                        @endif
                    </div>
                </a>
            </div>

            <!-- Main Content Card -->
            <div class="w-full sm:max-w-lg mx-4 relative z-10">
                <div class="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8 sm:p-12 transform hover:scale-[1.02] transition-transform duration-300">
                    {{ $slot }}
                </div>
            </div>

            <!-- Footer (hidden on login page) -->
            @unless(request()->routeIs('login'))
                <div class="relative z-10 mt-8">
                    <x-footer />
                </div>
            @endunless

            <!-- Floating Hearts Animation - Removed -->
            <div class="absolute inset-0 pointer-events-none overflow-hidden">
                <!-- Background decorative elements removed -->
            </div>
        </div>

        @stack('scripts')
    </body>
</html>
