@extends('layouts.app')

@section('title', 'Sugar Partner Rejections Management')

@section('content')
<!-- Hero Section -->
<x-hero-section
    title="Manage Your Rejections"
    subtitle="Manage your <span class='font-semibold text-gradient-primary'>Sugar Partner</span> rejections"
    description="Review and modify users you have rejected (both hard and soft rejections)"
    :showSteps="false"
/>

<div class="min-h-screen bg-gray-50">
    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Stats Card -->
            <div class="mb-8 animate-slide-up">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between text-white">
                        <div>
                            <h3 class="text-2xl font-bold mb-2">{{ count($rejections) }}</h3>
                            <p class="text-indigo-100 mb-0">Users Rejected By you</p>
                        </div>
                        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden">
                <div class="p-6 sm:p-8">
                    @if(session('success'))
                        <div class="mb-6 animate-slide-up">
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-4 shadow-sm">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-green-800 font-medium">{{ session('success') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-6 animate-slide-up">
                            <div class="bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-2xl p-4 shadow-sm">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-rose-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-red-800 font-medium">{{ session('error') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif


                    @if(count($rejections) > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($rejections as $rejection)
                                <div class="bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden animate-slide-up">
                                    <!-- Card Header -->
                                    <div class="p-4 bg-gradient-to-r {{ $rejection['rejection_type'] === 'hard_reject' ? 'from-red-500 to-rose-500' : 'from-yellow-500 to-orange-500' }}">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                                    @if($rejection['rejection_type'] === 'hard_reject')
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                        </svg>
                                                    @endif
                                                </div>
                                                <h3 class="text-white font-bold text-lg">{{ $rejection['user']->name }}</h3>
                                            </div>
                                            <span class="bg-white/20 text-white text-xs font-semibold px-3 py-1 rounded-full backdrop-blur-sm">
                                                {{ $rejection['rejection_type'] === 'hard_reject' ? 'Hard Rejected' : 'Soft Rejected' }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Card Body -->
                                    <div class="p-6">
                                        <!-- User Info -->
                                        <div class="flex items-center space-x-4 mb-6">
                                            @if($rejection['user']->profile_picture)
                                                <img src="{{ $rejection['user']->profile_picture_url }}"
                                                     alt="{{ $rejection['user']->name }}"
                                                     class="w-16 h-16 rounded-2xl object-cover border-2 border-gray-100 shadow-sm">
                                            @else
                                                <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center border-2 border-gray-100">
                                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                </div>
                                            @endif
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-900 mb-1">{{ $rejection['user']->name }}</h4>
                                                <p class="text-sm text-gray-500 mb-2">{{ $rejection['user']->email }}</p>
                                                @if($rejection['user']->getWhatIAmForAdmin() !== 'None')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                        {{ $rejection['user']->getWhatIAmForAdmin() }}
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Rejection Details -->
                                        <div class="space-y-4 mb-6">
                                            <div class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <p class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Rejection Date</p>
                                                    <p class="text-sm font-semibold text-gray-900">{{ $rejection['rejected_at']->format('M d, Y') }}</p>
                                                </div>
                                                <div>
                                                    <p class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Time Ago</p>
                                                    <p class="text-sm font-semibold text-gray-900">{{ $rejection['rejected_at']->diffForHumans() }}</p>
                                                </div>
                                            </div>
                                            @if($rejection['rejection_reason'])
                                                <div>
                                                    <p class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Reason</p>
                                                    <div class="bg-gray-50 rounded-xl p-3 border border-gray-100">
                                                        <p class="text-sm text-gray-700">{{ $rejection['rejection_reason'] }}</p>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Action Section -->
                                        <div class="space-y-4">
                                            <!-- Status Badge -->
                                            <div class="text-center">
                                                <div class="inline-flex items-center px-4 py-2 {{ $rejection['rejection_type'] === 'hard_reject' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }} rounded-full text-sm font-medium">
                                                    @if($rejection['rejection_type'] === 'hard_reject')
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                        Hard Rejected
                                                    @else
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                        </svg>
                                                        Soft Rejected
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Reason Textarea -->
                                            <div>
                                                <label for="reason_{{ $rejection['user']->id }}" class="block text-sm font-medium text-gray-700 mb-2">
                                                    Reason (Optional)
                                                </label>
                                                <textarea
                                                    id="reason_{{ $rejection['user']->id }}"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                                                    rows="3"
                                                    placeholder="Add a reason for your action..."></textarea>
                                            </div>

                                            <!-- Action Buttons -->
                                            <div class="space-y-2">
                                                @if($rejection['rejection_type'] === 'hard_reject')
                                                    <!-- Hard Reject Actions -->
                                                    <button type="button"
                                                            class="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                                                            onclick="convertToSoftReject({{ $rejection['user']->id }}, '{{ $rejection['user']->name }}')">
                                                        <div class="flex items-center justify-center space-x-2">
                                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                            </svg>
                                                            <span>Convert to Soft Reject</span>
                                                        </div>
                                                    </button>
                                                @else
                                                    <!-- Soft Reject Actions -->
                                                    <button type="button"
                                                            class="w-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                                                            onclick="convertToHardReject({{ $rejection['user']->id }}, '{{ $rejection['user']->name }}')">
                                                        <div class="flex items-center justify-center space-x-2">
                                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            <span>Convert to Hard Reject</span>
                                                        </div>
                                                    </button>
                                                @endif

                                                <!-- Remove Completely Button (for both types) -->
                                                <button type="button"
                                                        class="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                                                        onclick="removeRejection({{ $rejection['user']->id }}, '{{ $rejection['user']->name }}')">
                                                    <div class="flex items-center justify-center space-x-2">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                        <span>Clear Rejection</span>
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <!-- Empty State -->
                        <div class="text-center py-16 animate-slide-up">
                            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-3xl p-12 text-white shadow-xl">
                                <div class="mb-6">
                                    <div class="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center mx-auto backdrop-blur-sm">
                                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <h3 class="text-3xl font-bold mb-4">No Rejections Found</h3>
                                <p class="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
                                    You haven't rejected anyone yet.
                                    Keep an open mind in your Sugar Partner journey!
                                </p>
                                <a href="{{ route('profile.edit', ['tab' => 'sugar-partner']) }}"
                                   class="inline-flex items-center space-x-2 bg-white text-indigo-600 font-semibold py-3 px-6 rounded-xl hover:bg-gray-50 transition-colors duration-200 shadow-lg">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    <span>Back to Sugar Partner</span>
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
</div>

@endsection

@push('scripts')
<script>
function convertToSoftReject(userId, userName) {
    const reasonTextarea = document.getElementById(`reason_${userId}`);
    const reason = reasonTextarea.value.trim();

    if (confirm(`Convert your hard reject of ${userName} to soft reject?`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("sugar-partner.hard-rejects.convert-to-soft") }}';

        // CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // User ID
        const userIdInput = document.createElement('input');
        userIdInput.type = 'hidden';
        userIdInput.name = 'rejected_user_id';
        userIdInput.value = userId;
        form.appendChild(userIdInput);

        // Reason
        if (reason) {
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

function convertToHardReject(userId, userName) {
    const reasonTextarea = document.getElementById(`reason_${userId}`);
    const reason = reasonTextarea.value.trim();

    if (confirm(`Convert your soft reject of ${userName} to hard reject? This will permanently block future exchanges.`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("sugar-partner.hard-rejects.convert-to-hard") }}';

        // CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // User ID
        const userIdInput = document.createElement('input');
        userIdInput.type = 'hidden';
        userIdInput.name = 'rejected_user_id';
        userIdInput.value = userId;
        form.appendChild(userIdInput);

        // Reason
        if (reason) {
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

function removeRejection(userId, userName) {
    const reasonTextarea = document.getElementById(`reason_${userId}`);
    const reason = reasonTextarea.value.trim();

    if (confirm(`Remove your rejection of ${userName} completely?`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("sugar-partner.hard-rejects.remove-completely") }}';

        // CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // User ID
        const userIdInput = document.createElement('input');
        userIdInput.type = 'hidden';
        userIdInput.name = 'rejected_user_id';
        userIdInput.value = userId;
        form.appendChild(userIdInput);

        // Reason
        if (reason) {
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
