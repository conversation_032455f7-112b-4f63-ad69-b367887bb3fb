
@extends('layouts.app')

@section('title', $profileUser->name . ' - Sugar Partner Profile')

@section('content')
<div class="container py-4">
    <div class="row">
        <!-- Profile Header -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-heart-fill me-2"></i>{{ $profileUser->getSugarPartnerRoleLabel() }}'s Profile
                            @if($profileUserHasSoftRejected)
                                <span class="badge bg-warning ms-2">
                                    <i class="bi bi-dash-circle me-1"></i>Soft Rejected
                                </span>
                            @endif
                        </h4>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-light text-dark">Sugar Partner Exchange</span>
                            @auth
                                @if(auth()->id() !== $profileUser->id)
                                    <button onclick="openReportModal({{ $profileUser->id }}, '{{ addslashes($profileUser->name) }}')" class="btn btn-sm btn-outline-light" title="Report User">
                                        <i class="bi bi-flag"></i> Report
                                    </button>
                                @endif
                            @endauth
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($mutualRejectionStatus['any_rejection'])
                        @if($mutualRejectionStatus['mutual_rejection'])
                            <!-- Both users rejected each other -->
                            <div class="alert alert-danger mb-3">
                                <i class="bi bi-arrow-left-right me-2"></i>
                                <strong>
                                    @if($mutualRejectionStatus['you_rejected_them'] === 'hard_reject' || $mutualRejectionStatus['they_rejected_you'] === 'hard_reject')
                                        Hard rejected each other
                                    @else
                                        Soft rejected each other
                                    @endif
                                </strong>
                                <br>
                                <small>
                                    You {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'hard' : 'soft' }} rejected this user, and they {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'hard' : 'soft' }} rejected you.
                                </small>
                            </div>
                        @elseif($mutualRejectionStatus['you_rejected_them'])
                            <!-- Current user rejected the other user -->
                            <div class="alert {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'alert-danger' : 'alert-warning' }} mb-3">
                                <i class="bi {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'bi-x-circle' : 'bi-dash-circle' }} me-2"></i>
                                <strong>You {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'hard' : 'soft' }} rejected this user previously</strong>
                            </div>
                        @elseif($mutualRejectionStatus['they_rejected_you'])
                            <!-- Other user rejected the current user -->
                            <div class="alert {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'alert-danger' : 'alert-warning' }} mb-3">
                                <i class="bi {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'bi-x-circle' : 'bi-dash-circle' }} me-2"></i>
                                <strong>You {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'hard' : 'soft' }} rejected by this user previously</strong>
                            </div>
                        @endif
                    @endif

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        You are viewing this profile as part of an admin-initiated Sugar Partner exchange.
                        After reviewing, please submit your response below.
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0 fw-bold">
                        <i class="bi bi-person me-2 text-primary"></i>Basic Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">Name</label>
                            <p class="fw-semibold">{{ $profileUser->name }}</p>
                        </div>
                        @if($profileUser->show_date_of_birth || $profileUser->is_public_profile)
                            <div class="col-md-6">
                                <label class="form-label text-muted">
                                    @if($profileUser->hide_dob_year)
                                        Date of Birth
                                    @else
                                        Age
                                    @endif
                                </label>
                                <p class="fw-semibold">
                                    @if($profileUser->date_of_birth)
                                        @if($profileUser->hide_dob_year)
                                            {{ $profileUser->date_of_birth->format('F d') }}
                                        @else
                                            {{ \Carbon\Carbon::parse($profileUser->date_of_birth)->age }} years old
                                        @endif
                                    @else
                                        Not specified
                                    @endif
                                </p>
                            </div>
                        @endif
                        <div class="col-md-6">
                            <label class="form-label text-muted">Gender</label>
                            <p class="fw-semibold">{{ ucfirst($profileUser->gender ?? 'Not specified') }}</p>
                        </div>
                        @if($profileUser->show_contact_number || $profileUser->is_public_profile)
                            <div class="col-md-6">
                                <label class="form-label text-muted">Contact</label>
                                <p class="fw-semibold">{{ $profileUser->contact_number ?? 'Not provided' }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sugar Partner Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0 fw-bold">
                        <i class="bi bi-heart me-2 text-danger"></i>Sugar Partner Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">What I Am</label>
                            <p class="fw-semibold">
                                @php $whatIAm = $profileUser->getWhatIAmForAdmin(); @endphp
                                @if($whatIAm !== 'None')
                                    <span class="badge bg-primary-subtle text-primary">
                                        <i class="bi bi-person-check me-1"></i>{{ $whatIAm }}
                                    </span>
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">What I Want</label>
                            <p class="fw-semibold">
                                @php $whatIWant = $profileUser->getWhatIWantForAdmin(); @endphp
                                @if(!in_array('None', $whatIWant))
                                    <div class="d-flex flex-wrap gap-1">
                                        @foreach($whatIWant as $type)
                                            <span class="badge bg-danger-subtle text-danger">
                                                <i class="bi bi-heart me-1"></i>{{ $type }}
                                            </span>
                                        @endforeach
                                    </div>
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </p>
                        </div>
                        @if($profileUser->sugar_partner_bio)
                            <div class="col-12">
                                <label class="form-label text-muted">Bio</label>
                                <p class="fw-semibold">{{ $profileUser->sugar_partner_bio }}</p>
                            </div>
                        @endif
                        @if($profileUser->sugar_partner_expectations)
                            <div class="col-12">
                                <label class="form-label text-muted">Expectations</label>
                                <p class="fw-semibold">{{ $profileUser->sugar_partner_expectations }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Interests & Hobbies -->
            @if($profileUser->show_interests_hobbies || $profileUser->is_public_profile)
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="card-title mb-0 fw-bold">
                            <i class="bi bi-star me-2 text-warning"></i>Interests & Hobbies
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($profileUser->interests)
                            <div class="d-flex flex-wrap gap-2">
                                @foreach(explode(',', $profileUser->interests) as $interest)
                                    <span class="badge bg-light text-dark border">{{ trim($interest) }}</span>
                                @endforeach
                            </div>
                        @else
                            <p class="text-muted mb-0">No interests specified</p>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Expectations -->
            @if($profileUser->show_expectations || $profileUser->is_public_profile)
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="card-title mb-0 fw-bold">
                            <i class="bi bi-bullseye me-2 text-success"></i>Expectations
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($profileUser->expectation)
                            <p class="mb-0">{{ $profileUser->expectation }}</p>
                        @else
                            <p class="text-muted mb-0">No expectations specified</p>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Profile Picture -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body text-center">
                    @if($profileUser->profile_picture)
                        <img src="{{ asset('storage/' . $profileUser->profile_picture) }}" 
                             alt="{{ $profileUser->name }}" 
                             class="rounded-circle mb-3" 
                             style="width: 150px; height: 150px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 150px; height: 150px;">
                            <i class="bi bi-person-circle text-muted" style="font-size: 4rem;"></i>
                        </div>
                    @endif
                    <h5 class="card-title">{{ $profileUser->name }}</h5>
                    <p class="text-muted">
                        @if($profileUser->date_of_birth && ($profileUser->show_date_of_birth || $profileUser->is_public_profile))
                            @if($profileUser->hide_dob_year)
                                {{ $profileUser->date_of_birth->format('F d') }}
                            @else
                                {{ \Carbon\Carbon::parse($profileUser->date_of_birth)->age }} years old
                            @endif
                        @endif
                        @if($profileUser->gender)
                            @if($profileUser->date_of_birth && ($profileUser->show_date_of_birth || $profileUser->is_public_profile))
                                • {{ ucfirst($profileUser->gender) }}
                            @else
                                {{ ucfirst($profileUser->gender) }}
                            @endif
                        @endif
                    </p>
                </div>
            </div>

            <!-- Gallery Images -->
            @if($profileUser->show_gallery_images || $profileUser->is_public_profile)
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="card-title mb-0 fw-bold">
                            <i class="bi bi-images me-2 text-info"></i>Gallery
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($profileUser->galleryImages->count() > 0)
                            <div class="row g-2">
                                @foreach($profileUser->galleryImages->take(6) as $image)
                                    <div class="col-6">
                                        <img src="{{ asset('storage/' . $image->image_path) }}" 
                                             alt="Gallery Image" 
                                             class="img-fluid rounded"
                                             style="height: 100px; width: 100%; object-fit: cover;">
                                    </div>
                                @endforeach
                            </div>
                            @if($profileUser->galleryImages->count() > 6)
                                <p class="text-muted text-center mt-2 mb-0">
                                    +{{ $profileUser->galleryImages->count() - 6 }} more images
                                </p>
                            @endif
                        @else
                            <p class="text-muted text-center mb-0">No gallery images</p>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Response Actions -->
            @if(!$exchange->getUserResponse($user->id))
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="card-title mb-0 fw-bold">
                            <i class="bi bi-chat-heart me-2 text-primary"></i>Your Response
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">After reviewing {{ $profileUser->name }}'s profile, please submit your response:</p>
                        
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#responseModal" data-response="accept">
                                <i class="bi bi-heart-fill me-2"></i>Accept
                            </button>
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#responseModal" data-response="soft_reject">
                                <i class="bi bi-dash-circle me-2"></i>Soft Reject
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#responseModal" data-response="hard_reject">
                                <i class="bi bi-x-circle me-2"></i>Hard Reject
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{ route('sugar-partner.exchange.status', $exchange) }}" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-arrow-left me-1"></i>Back to Exchange Status
                            </a>
                        </div>
                    </div>
                </div>
            @else
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="card-title mb-0 fw-bold">
                            <i class="bi bi-check-circle me-2 text-success"></i>Response Submitted
                        </h6>
                    </div>
                    <div class="card-body">
                        @php $userResponse = $exchange->getUserResponse($user->id); @endphp
                        <p class="mb-2">
                            <strong>Your Response:</strong> 
                            <span class="badge {{ $userResponse->getRejectionTypeBadgeClass() }}">
                                {{ $userResponse->getRejectionTypeDisplayName() }}
                            </span>
                        </p>
                        @if($userResponse->rejection_reason)
                            <p class="mb-2">
                                <strong>Reason:</strong> {{ $userResponse->rejection_reason }}
                            </p>
                        @endif
                        <p class="text-muted mb-3">
                            <strong>Submitted:</strong> {{ $userResponse->created_at->format('M d, Y h:i A') }}
                        </p>
                        
                        <a href="{{ route('sugar-partner.exchange.status', $exchange) }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-arrow-left me-1"></i>Back to Exchange Status
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Response Modal -->
@if(!$exchange->getUserResponse($user->id))
<div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="responseModalLabel">
                    <i class="bi bi-chat-heart me-2"></i>Submit Your Response
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('sugar-partner.exchange.submit-response', $exchange) }}">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="rejection_type" id="modal_rejection_type">
                    
                    <div class="alert alert-info" id="response_description">
                        <!-- Will be populated by JavaScript -->
                    </div>

                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Reason (Optional)</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" placeholder="Share your thoughts about {{ $profileUser->name }}..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="admin_note" class="form-label">Note for Admin (Private)</label>
                        <textarea class="form-control" id="admin_note" name="admin_note" rows="2" placeholder="Private feedback for admin (optional)"></textarea>
                        <div class="form-text">This note will only be visible to administrators.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="submit_response_btn">
                        <i class="bi bi-send me-1"></i>Submit Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const responseModal = document.getElementById('responseModal');
    if (responseModal) {
        responseModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const responseType = button.getAttribute('data-response');
            
            document.getElementById('modal_rejection_type').value = responseType;
            
            const descriptions = {
                'accept': '<i class="bi bi-heart-fill text-success me-2"></i><strong>Accept:</strong> You are expressing interest in {{ $profileUser->name }}. They will be notified of your acceptance.',
                'soft_reject': '<i class="bi bi-dash-circle text-warning me-2"></i><strong>Soft Reject:</strong> You are declining this match but leaving the possibility open for future exchanges.',
                'hard_reject': '<i class="bi bi-x-circle text-danger me-2"></i><strong>Hard Reject:</strong> You are permanently rejecting this match. No future profile exchanges will be allowed between you and {{ $profileUser->name }}.'
            };
            
            document.getElementById('response_description').innerHTML = descriptions[responseType];
            
            const submitBtn = document.getElementById('submit_response_btn');
            const btnClasses = {
                'accept': 'btn-success',
                'soft_reject': 'btn-warning',
                'hard_reject': 'btn-danger'
            };
            
            submitBtn.className = 'btn ' + btnClasses[responseType];
        });
    }
});
</script>

<!-- Report User Modal -->
@auth
    @if(auth()->id() !== $profileUser->id)
        <x-report-user-modal :userId="$profileUser->id" :userName="$profileUser->name" />
    @endif
@endauth

@endpush
