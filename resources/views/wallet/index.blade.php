<x-app-layout>
    @section('title', 'My Wallet')
    @section('description', 'Manage your wallet balance and view transaction history')

    <!-- Compact Hero Section -->
    <x-hero-section
        title="My Wallet"
        subtitle="Manage your earnings and transactions"
        description=""
        :showSteps="false"
        backgroundGradient="linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 51, 234, 0.3) 50%, rgba(236, 72, 153, 0.3) 100%)"
    />

    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Wallet Summary Section -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                <!-- Wallet Balance -->
                <div class="md:col-span-2 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-lg p-6 text-white" style="background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-blue-100 mb-1">Wallet Balance</h2>
                            <p class="text-3xl font-bold">₹{{ number_format($wallet->balance, 2) }}</p>
                            <p class="text-blue-200 text-sm mt-1">Available for withdrawal</p>
                        </div>
                        <div class="text-blue-200">
                            <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Earnings -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg shadow-sm border border-green-200 p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-green-700 mb-1">Total Earnings</h3>
                            <p class="text-2xl font-bold text-green-600">₹{{ number_format($totalEarnings, 2) }}</p>
                        </div>
                        <div class="text-green-500">
                            <!-- Downward Arrow for Total Earnings (Paisa Aana) -->
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 20l-8-8h6V4h4v8h6l-8 8z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Spending -->
                <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg shadow-sm border border-red-200 p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-red-700 mb-1">Total Spending</h3>
                            <p class="text-2xl font-bold text-red-600">₹{{ number_format($totalSpending, 2) }}</p>
                        </div>
                        <div class="text-red-500">
                            <!-- Upward Arrow for Total Spending (Paisa Jaana) -->
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 4l8 8h-6v8h-4v-8H4l8-8z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column - Payment Methods & Actions -->
                <div class="lg:col-span-1 space-y-6">

                    <!-- Payment Methods -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-5">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Payment Methods</h3>

                        <!-- Withdrawal Info -->
                        <div class="mb-4 p-3 bg-indigo-50 rounded-lg">
                            <div class="text-xs text-indigo-800">
                                <p class="font-medium mb-1">Withdrawal Info:</p>
                                <div class="flex flex-wrap gap-3">
                                    <span>Min: ₹{{ number_format($minWithdrawal, 0) }}</span>
                                    @if($processingFee > 0)
                                        <span>Fee: ₹{{ number_format($processingFee, 0) }}</span>
                                    @endif
                                    <span>Time: {{ $processingTime }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods List -->
                        <div class="space-y-3">
                            @php
                                $gpayAccount = $bankAccounts->where('payment_method_type', 'gpay')->first();
                                $bankAccount = $bankAccounts->where('payment_method_type', 'bank')->first();
                            @endphp

                            <!-- G-Pay Account -->
                            @if($gpayAccount)
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center border border-gray-200">
                                                <!-- Google Pay Logo -->
                                                <svg class="w-5 h-5" viewBox="0 0 48 48" fill="none">
                                                    <path d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z" fill="#4285F4"/>
                                                    <path d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z" fill="#34A853"/>
                                                    <path d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z" fill="#FBBC05"/>
                                                    <path d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z" fill="#EA4335"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h5 class="text-sm font-medium text-gray-900">{{ $gpayAccount->account_holder_name }}</h5>
                                                <p class="text-xs text-gray-500">G-Pay • {{ $gpayAccount->gpay_number }}</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button onclick="editBankAccount({{ $gpayAccount->id }}, @json($gpayAccount))" class="text-blue-600 hover:text-blue-700 text-xs font-medium">
                                                Edit
                                            </button>
                                            <button onclick="deleteBankAccount({{ $gpayAccount->id }})" class="text-red-600 hover:text-red-700 text-xs font-medium">
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif



                            <!-- Add Payment Method -->
                            @if(!$gpayAccount)
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-3 text-center">
                                    <p class="text-sm text-gray-500 mb-2">
                                        Add G-Pay account for withdrawals
                                    </p>
                                    <div class="flex justify-center space-x-2">
                                        <button onclick="showAddBankModal('gpay')" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                            + G-Pay
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Withdrawal Button -->
                        <div class="mt-4">
                            @if($wallet->balance >= $minWithdrawal)
                                <button onclick="showWithdrawalModal()" class="w-full text-white font-medium py-2.5 px-4 rounded-lg transition-colors text-sm" style="background-color: #4F46E5;" onmouseover="this.style.backgroundColor='#3730A3'" onmouseout="this.style.backgroundColor='#4F46E5'">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                    </svg>
                                    Withdraw Money
                                </button>
                            @else
                                <button disabled class="w-full bg-gray-400 text-white font-medium py-2.5 px-4 rounded-lg cursor-not-allowed text-sm">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    Minimum ₹{{ number_format($minWithdrawal, 0) }} required
                                </button>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Right Column - Transaction History -->
                <div class="lg:col-span-2">
                    <!-- Transaction History -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100">
                        <div class="p-5 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Transactions</h3>
                        </div>

                        @if($walletTransactions->count() > 0 || $eventPayments->count() > 0 || $timeSpendingPayments->count() > 0 || $timeSpendingEarnings->count() > 0 || $subscriptionPayments->count() > 0)
                            @php
                                // Combine and sort all transactions by date
                                $allTransactions = collect();

                                // Add event payments
                                foreach($eventPayments as $payment) {
                                    $allTransactions->push([
                                        'type' => 'event_payment',
                                        'category' => 'spending',
                                        'id' => $payment->id,
                                        'transaction_id' => 'EP-' . $payment->id,
                                        'description' => 'Event Payment: ' . ($payment->meetingAddress->title ?? 'Event'),
                                        'sub_description' => $payment->meetingAddress->location ?? '',
                                        'amount' => $payment->amount_paid,
                                        'payment_method' => $payment->payment_method ?? 'razorpay',
                                        'date' => $payment->created_at,
                                        'status' => 'completed',
                                        'badge_color' => 'green'
                                    ]);
                                }

                                // Add time spending payments (as client)
                                foreach($timeSpendingPayments as $booking) {
                                    $allTransactions->push([
                                        'type' => 'time_spending_payment',
                                        'category' => 'spending',
                                        'id' => $booking->id,
                                        'transaction_id' => 'TSP-' . $booking->id,
                                        'description' => 'Time Spending with ' . $booking->provider->name,
                                        'sub_description' => $booking->duration_hours . ' hours @ ₹' . number_format($booking->hourly_rate, 2) . '/hr' . ($booking->platform_fee > 0 ? ' + ₹' . number_format($booking->platform_fee, 2) . ' platform fee' : ''),
                                        'amount' => $booking->total_amount ?? ($booking->base_amount + ($booking->platform_fee ?? 0)),
                                        'payment_method' => $booking->payment_method ?? 'razorpay',
                                        'date' => $booking->paid_at ?: $booking->created_at,
                                        'status' => 'completed',
                                        'badge_color' => 'green'
                                    ]);
                                }

                                // Add time spending earnings (as provider) - only released payments
                                foreach($timeSpendingEarnings as $booking) {
                                    $allTransactions->push([
                                        'type' => 'time_spending_earning',
                                        'category' => 'earning',
                                        'id' => $booking->id,
                                        'transaction_id' => 'TSE-' . $booking->id,
                                        'description' => 'Time Spending with ' . $booking->client->name,
                                        'sub_description' => $booking->duration_hours . ' hours @ ₹' . number_format($booking->hourly_rate, 2) . '/hr',
                                        'amount' => $booking->provider_amount, // Show actual amount received by provider
                                        'payment_method' => 'wallet',
                                        'date' => $booking->escrow_released_at ?: ($booking->paid_at ?: $booking->created_at), // Use actual release date, fallback to payment date or creation date
                                        'status' => 'completed',
                                        'badge_color' => 'green'
                                    ]);
                                }

                                // Add wallet transactions
                                foreach($walletTransactions as $transaction) {
                                    // Determine category based on transaction type and description
                                    // Earnings: credit, refund (money coming in)
                                    // Also check description for refund-related keywords
                                    $isEarning = in_array($transaction->type, ['credit', 'refund']) ||
                                                stripos($transaction->description, 'refund') !== false ||
                                                stripos($transaction->description, 'earning') !== false ||
                                                stripos($transaction->description, 'payment received') !== false;

                                    $allTransactions->push([
                                        'type' => 'wallet_transaction',
                                        'category' => $isEarning ? 'earning' : 'spending',
                                        'id' => $transaction->id,
                                        'transaction_id' => 'WT-' . $transaction->id,
                                        'description' => $transaction->description,
                                        'sub_description' => $transaction->commission_amount > 0 ? 'Commission: ₹' . number_format($transaction->commission_amount, 2) : '',
                                        'amount' => $transaction->final_amount,
                                        'payment_method' => 'wallet',
                                        'date' => $transaction->created_at,
                                        'status' => 'completed',
                                        'badge_color' => 'green'
                                    ]);
                                }

                                // Add subscription payments
                                foreach($subscriptionPayments as $subscription) {
                                    $allTransactions->push([
                                        'type' => 'subscription_payment',
                                        'category' => 'spending',
                                        'id' => $subscription->id,
                                        'transaction_id' => 'SUB-' . $subscription->id,
                                        'description' => 'Subscription: ' . $subscription->subscriptionPlan->name,
                                        'sub_description' => $subscription->subscriptionPlan->duration_months . ' month(s) • Valid until ' . $subscription->formatted_expiry_date,
                                        'amount' => $subscription->amount_paid,
                                        'payment_method' => $subscription->payment_method ?? 'razorpay',
                                        'date' => $subscription->created_at,
                                        'status' => $subscription->status === 'active' ? 'completed' : $subscription->status,
                                        'badge_color' => $subscription->status === 'active' ? 'green' : ($subscription->status === 'queued' ? 'yellow' : 'gray')
                                    ]);
                                }

                                // Sort by date (newest first) and limit to recent transactions
                                $allTransactions = $allTransactions->sortByDesc('date')->take(10);
                            @endphp

                            <div class="p-4 space-y-3 max-h-96 overflow-y-auto transaction-scroll">
                                @foreach($allTransactions as $index => $transaction)
                                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3">
                                                    <div class="w-10 h-10 rounded-full flex items-center justify-center
                                                        {{ $transaction['category'] === 'earning' ? 'bg-green-100 border-2 border-green-200' : 'bg-red-100 border-2 border-red-200' }}">
                                                        @if($transaction['category'] === 'earning')
                                                            <!-- Downward Arrow for Earnings (Paisa Aana - Money Coming In) -->
                                                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M12 20l-8-8h6V4h4v8h6l-8 8z"/>
                                                            </svg>
                                                        @else
                                                            <!-- Upward Arrow for Spending (Paisa Jaana - Money Going Out) -->
                                                            <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M12 4l8 8h-6v8h-4v-8H4l8-8z"/>
                                                            </svg>
                                                        @endif
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-sm font-medium text-gray-900">{{ $transaction['description'] }}</h4>
                                                        @if($transaction['sub_description'])
                                                            <p class="text-xs text-gray-500">{{ $transaction['sub_description'] }}</p>
                                                        @endif
                                                        <div class="flex items-center space-x-2 mt-1">
                                                            <span class="text-xs text-gray-500">{{ $transaction['transaction_id'] }}</span>
                                                            <span class="text-xs text-gray-400">•</span>
                                                            <span class="text-xs text-gray-500">{{ $transaction['date']->format('M d, Y h:i A') }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-lg font-bold {{ $transaction['category'] === 'earning' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50' }} px-3 py-1 rounded-lg">
                                                    {{ $transaction['category'] === 'earning' ? '+' : '-' }}₹{{ number_format($transaction['amount'], 2) }}
                                                </div>
                                                <div class="flex items-center justify-end space-x-1 mt-1">
                                                    @if($transaction['payment_method'] === 'razorpay')
                                                        <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                                        </svg>
                                                        <span class="text-xs text-blue-600">Razorpay</span>
                                                    @elseif($transaction['payment_method'] === 'wallet')
                                                        <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                        </svg>
                                                        <span class="text-xs text-green-600">Wallet</span>
                                                    @else
                                                        <span class="text-xs text-gray-600">{{ ucfirst($transaction['payment_method']) }}</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="p-8 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    Your transaction history will appear here once you start earning or spending.
                                </p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Payment Method Modal -->
    <div id="addBankModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="modalTitle" class="text-lg font-medium text-gray-900">Add G-Pay Account</h3>
                    <button onclick="closeAddBankModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Payment Method Type Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                    <div class="flex justify-center">
                        <button type="button" onclick="selectPaymentType('gpay')" id="gpayTypeBtn"
                                class="payment-type-btn active flex flex-col items-center p-3 border-2 border-green-500 bg-green-50 rounded-lg hover:bg-green-100 transition-colors w-32">
                            <!-- Google Pay Logo -->
                            <svg class="w-6 h-6 mb-1" viewBox="0 0 48 48" fill="none">
                                <path d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z" fill="#4285F4"/>
                                <path d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z" fill="#34A853"/>
                                <path d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z" fill="#FBBC05"/>
                                <path d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z" fill="#EA4335"/>
                            </svg>
                            <span class="text-xs font-medium text-green-700">G-Pay</span>
                        </button>
                    </div>
                </div>

                <form id="addBankForm">
                    <input type="hidden" id="paymentMethodType" name="payment_method_type" value="gpay">



                    <!-- G-Pay Fields -->
                    <div id="gpayFields" class="space-y-3">
                        <div>
                            <label for="gpayName" class="block text-sm font-medium text-gray-700 mb-1">
                                Account Holder Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="gpayName" name="gpay_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="Enter name as per G-Pay account">
                        </div>

                        <div>
                            <label for="gpayNumber" class="block text-sm font-medium text-gray-700 mb-1">
                                G-Pay Mobile Number <span class="text-red-500">*</span>
                            </label>
                            <input type="tel" id="gpayNumber" name="gpay_number" maxlength="10" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="Enter 10-digit mobile number">
                        </div>

                        <div>
                            <label for="gpayUpi" class="block text-sm font-medium text-gray-700 mb-1">
                                UPI ID <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="gpayUpi" name="gpay_upi" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="e.g., yourname@paytm">
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeAddBankModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="addBankBtn"
                                class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors">
                            <span id="addBankBtnText">Add G-Pay Account</span>
                            <span id="addBankBtnLoading" class="hidden">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Adding...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Withdrawal Modal -->
    <div id="withdrawalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Withdraw to G-Pay</h3>
                    <button onclick="closeWithdrawalModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="withdrawalForm">
                    <div class="space-y-4">
                        <div>
                            <label for="withdrawalAmount" class="block text-sm font-medium text-gray-700 mb-1">
                                Amount <span class="text-red-500">*</span>
                            </label>
                            <input type="number" id="withdrawalAmount" name="amount" required
                                   min="{{ $minWithdrawal }}" max="{{ $wallet->balance }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Enter amount to withdraw">
                            <div class="text-xs text-gray-500 mt-1">
                                Available: ₹{{ number_format($wallet->balance, 2) }} |
                                Min: ₹{{ number_format($minWithdrawal, 0) }}
                            </div>
                        </div>

                        <div>
                            <label for="bankAccountSelect" class="block text-sm font-medium text-gray-700 mb-1">
                                G-Pay Account <span class="text-red-500">*</span>
                            </label>
                            <select id="bankAccountSelect" name="bank_account_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">Select G-Pay account</option>
                                @foreach($bankAccounts->where('is_verified', true)->where('payment_method_type', 'gpay') as $account)
                                    <option value="{{ $account->id }}">{{ $account->display_name }}</option>
                                @endforeach
                            </select>
                            @if($bankAccounts->where('is_verified', true)->where('payment_method_type', 'gpay')->count() === 0)
                                <p class="text-xs text-red-600 mt-1">No verified G-Pay accounts found. Please add a G-Pay account first.</p>
                            @endif
                        </div>

                        <div>
                            <label for="withdrawalNotes" class="block text-sm font-medium text-gray-700 mb-1">
                                Notes (Optional)
                            </label>
                            <textarea id="withdrawalNotes" name="notes" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Add any notes for this withdrawal..."></textarea>
                        </div>

                       
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeWithdrawalModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="withdrawalBtn"
                                class="px-4 py-2 text-sm font-medium text-white rounded-md transition-colors" style="background-color: #4F46E5;" onmouseover="this.style.backgroundColor='#3730A3'" onmouseout="this.style.backgroundColor='#4F46E5'">
                            <span id="withdrawalBtnText">Submit Request</span>
                            <span id="withdrawalBtnLoading" class="hidden">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Production console optimization
        (function() {
            const originalWarn = console.warn;
            const originalLog = console.log;
            const originalError = console.error;

            console.warn = function(...args) {
                const message = args.join(' ');
                if (typeof message === 'string' && (
                    message.includes('tailwindcss') ||
                    message.includes('cdn.tailwindcss.com') ||
                    message.includes('should not be used in production') ||
                    message.includes('script failed to load')
                )) {
                    return;
                }
                originalWarn.apply(console, args);
            };

            console.log = function(...args) {
                const message = args.join(' ');
                if (typeof message === 'string' && (
                    message.includes('tailwindcss') ||
                    message.includes('development')
                )) {
                    return;
                }
                originalLog.apply(console, args);
            };

            console.error = function(...args) {
                const message = args.join(' ');
                if (typeof message === 'string' && message.includes('tailwindcss')) {
                    return;
                }
                originalError.apply(console, args);
            };
        })();

        // Load more transactions functionality
        document.getElementById('load-more-btn')?.addEventListener('click', function() {
            // Load more transactions functionality can be implemented here
        });

        // Withdrawal settings
        const withdrawalSettings = {
            minAmount: {{ $minWithdrawal ?? 0 }},
            processingFee: {{ $processingFee ?? 0 }},
            currentBalance: {{ $wallet->balance ?? 0 }}
        };

        // Show/hide modals
        function showAddBankModal(defaultType = 'gpay') {
            document.getElementById('addBankModal').classList.remove('hidden');
            selectPaymentType(defaultType);
        }

        function closeAddBankModal() {
            document.getElementById('addBankModal').classList.add('hidden');
            document.getElementById('addBankForm').reset();
            // Reset to G-Pay (default)
            selectPaymentType('gpay');
            // Reset edit mode
            isEditMode = false;
            editingAccountId = null;
            document.getElementById('modalTitle').textContent = 'Add G-Pay Account';
        }

        // Payment type selection
        function selectPaymentType(type) {
            const gpayBtn = document.getElementById('gpayTypeBtn');
            const gpayFields = document.getElementById('gpayFields');
            const paymentMethodType = document.getElementById('paymentMethodType');

            // Always set to G-Pay
            gpayBtn.className = 'payment-type-btn active flex flex-col items-center p-3 border-2 border-green-500 bg-green-50 rounded-lg hover:bg-green-100 transition-colors w-32';
            gpayFields.classList.remove('hidden');
            paymentMethodType.value = 'gpay';

            // Make G-Pay fields required
            document.getElementById('gpayName').required = true;
            document.getElementById('gpayNumber').required = true;
            document.getElementById('gpayUpi').required = true;
        }

        // Initialize default state when page loads
        document.addEventListener('DOMContentLoaded', function() {
            selectPaymentType('gpay'); // Set G-Pay as default
        });

        // Global variable to track edit mode
        let isEditMode = false;
        let editingAccountId = null;

        // Edit bank account
        function editBankAccount(accountId, accountData) {
            isEditMode = true;
            editingAccountId = accountId;

            // Update modal title
            document.getElementById('modalTitle').textContent = 'Update G-Pay Account';

            // Pre-fill form with G-Pay data only
            selectPaymentType('gpay');
            document.getElementById('gpayName').value = accountData.account_holder_name || '';
            document.getElementById('gpayNumber').value = accountData.gpay_number || '';
            document.getElementById('gpayUpi').value = accountData.gpay_upi || '';

            // Show modal
            showAddBankModal();
        }

        // Delete bank account
        async function deleteBankAccount(accountId) {
            if (!confirm('Are you sure you want to delete this payment method?')) {
                return;
            }

            try {
                const response = await fetch(`/wallet/delete-bank-account/${accountId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorMessage(data.message || 'Failed to delete payment method.');
                }
            } catch (error) {
                console.error('Error deleting payment method:', error);
                showErrorMessage('An error occurred while deleting payment method.');
            }
        }

        function showWithdrawalModal() {
            document.getElementById('withdrawalModal').classList.remove('hidden');
        }

        function closeWithdrawalModal() {
            document.getElementById('withdrawalModal').classList.add('hidden');
            document.getElementById('withdrawalForm').reset();
            document.getElementById('netAmountDisplay').textContent = '₹0';
        }

        // Calculate net amount
        document.getElementById('withdrawalAmount')?.addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            const netAmount = Math.max(0, amount - withdrawalSettings.processingFee);
            document.getElementById('netAmountDisplay').textContent = '₹' + netAmount.toFixed(2);
        });

        // Add payment method form submission
        document.getElementById('addBankForm')?.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.getElementById('addBankBtn');
            const btnText = document.getElementById('addBankBtnText');
            const btnLoading = document.getElementById('addBankBtnLoading');
            const paymentType = formData.get('payment_method_type');

            // Client-side validation for G-Pay only
            const gpayNumber = formData.get('gpay_number');
            const gpayUpi = formData.get('gpay_upi');

            if (!gpayNumber || gpayNumber.length !== 10) {
                showErrorMessage('Mobile number must be exactly 10 digits.');
                return;
            }

            if (!gpayUpi || !gpayUpi.includes('@')) {
                showErrorMessage('Please enter a valid UPI ID.');
                return;
            }

            // Show loading state
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;

            try {


                let url, method;
                if (isEditMode && editingAccountId) {
                    url = `/wallet/update-bank-account/${editingAccountId}`;
                    method = 'PUT';
                    formData.append('_method', 'PUT');
                } else {
                    url = '/wallet/add-bank-account';
                    method = 'POST';
                }

                const response = await fetch(url, {
                    method: 'POST', // Always POST for Laravel form method spoofing
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    closeAddBankModal();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join(', ');
                        showErrorMessage(errorMessages);
                    } else {
                        showErrorMessage(data.message || `Failed to ${isEditMode ? 'update' : 'add'} payment method.`);
                    }
                }
            } catch (error) {
                console.error('Error adding payment method:', error);
                showErrorMessage('An error occurred while adding payment method.');
            } finally {
                // Reset loading state
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        });

        // Withdrawal form submission
        document.getElementById('withdrawalForm')?.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.getElementById('withdrawalBtn');
            const btnText = document.getElementById('withdrawalBtnText');
            const btnLoading = document.getElementById('withdrawalBtnLoading');

            // Show loading state
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;

            try {
                const response = await fetch('/wallet/request-withdrawal', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    closeWithdrawalModal();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join(', ');
                        showErrorMessage(errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Failed to submit withdrawal request.');
                    }
                }
            } catch (error) {
                showErrorMessage('An error occurred while submitting withdrawal request.');
            } finally {
                // Reset loading state
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        });

        // Utility functions for showing messages
        function showSuccessMessage(message) {
            // Create and show success toast
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg';
            toast.style.zIndex = '100000';
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }

        function showErrorMessage(message) {
            // Create and show error toast
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg';
            toast.style.zIndex = '100000';
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        // Cancel withdrawal function
        async function cancelWithdrawal(withdrawalId) {
            if (!confirm('Are you sure you want to cancel this withdrawal request?')) {
                return;
            }

            try {
                const response = await fetch(`/wallet/cancel-withdrawal/${withdrawalId}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorMessage(data.message || 'Failed to cancel withdrawal.');
                }
            } catch (error) {
                console.error('Error cancelling withdrawal:', error);
                showErrorMessage('An error occurred while cancelling withdrawal.');
            }
        }
    </script>

    <style>
        .payment-type-btn {
            transition: all 0.2s ease-in-out;
        }

        .payment-type-btn:hover {
            background-color: #f9fafb;
        }

        .payment-type-btn.active {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Custom scrollbar for transaction list */
        .transaction-scroll::-webkit-scrollbar {
            width: 4px;
        }

        .transaction-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .transaction-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        .transaction-scroll::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Hide Tailwind CDN warning */
        .tailwind-warning {
            display: none !important;
        }
    </style>
</x-app-layout>
