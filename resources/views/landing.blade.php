@section('title', '<PERSON><PERSON> Kar Pagle Rulayega Kya? SettingWala Mein Aa Jao! 😭🔥')
@section('description', '<PERSON><PERSON> sahab, yahan sab cool log hain! Verified profiles, mast events, aur guaranteed setting. "Ye bhi theek hai" se setting complete tak ka journey. SettingWala pe aa jao!')

<x-app-layout>
    




    <!-- Creative CTA Section -->
    <section class="pb-12 bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="floating-circle absolute top-10 left-10 w-20 h-20 bg-indigo-200 rounded-full opacity-30"></div>
            <div class="floating-circle absolute top-32 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-40 animation-delay-1"></div>
            <div class="floating-circle absolute bottom-20 left-1/4 w-12 h-12 bg-rose-200 rounded-full opacity-35 animation-delay-2"></div>
            <div class="floating-circle absolute bottom-32 right-1/3 w-24 h-24 bg-blue-200 rounded-full opacity-25 animation-delay-3"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            
<!-- Animated SVG Hero Section -->
    <x-hero-section
        title="Chal Bhai tera <span class='text-indigo-600'>Setting </span><br/>karva dete hain!"
        subtitle=""
        description=""
        :showSteps="true"
        customClass=""
        backgroundGradient=""
    />
            <!-- Interactive CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-10">
                @guest
                    <div class="cta-button-wrapper group">
                        <a href="{{ route('register') }}" class="btn-primary-enhanced">
                            <div class="btn-icon-wrapper">
                                <i data-lucide="users" class="w-6 h-6"></i>
                            </div>
                            <span class="btn-text">Chalo Shuru Karte Hain</span>
                            <div class="btn-arrow">
                                <i data-lucide="arrow-right" class="w-5 h-5"></i>
                            </div>
                        </a>
                        <div class="btn-glow"></div>
                    </div>

                    <div class="cta-button-wrapper group">
                        <a href="{{ route('static.how-it-works') }}" class="btn-secondary-enhanced">
                            <div class="btn-icon-wrapper">
                                <i data-lucide="play-circle" class="w-6 h-6"></i>
                            </div>
                            <span class="btn-text">Dekho Kaise Hota Hai</span>
                            <div class="btn-arrow">
                                <i data-lucide="arrow-right" class="w-5 h-5"></i>
                            </div>
                        </a>
                        <div class="btn-glow secondary"></div>
                    </div>
                @else
                    @if(\App\Models\MeetingAddress::hasAvailableEvents())
                        <div class="cta-button-wrapper group">
                            <a href="{{ route('event.address') }}" class="btn-primary-enhanced">
                                <div class="btn-icon-wrapper">
                                    <i data-lucide="calendar" class="w-6 h-6"></i>
                                </div>
                                <span class="btn-text">Events Dekho</span>
                                <div class="btn-arrow">
                                    <i data-lucide="arrow-right" class="w-5 h-5"></i>
                                </div>
                            </a>
                            <div class="btn-glow"></div>
                        </div>
                    @else
                        <div class="cta-button-wrapper group">
                            <a href="{{ route('home') }}" class="btn-primary-enhanced">
                                <div class="btn-icon-wrapper">
                                    <i data-lucide="heart" class="w-6 h-6"></i>
                                </div>
                                <span class="btn-text">Dashboard Dekho</span>
                                <div class="btn-arrow">
                                    <i data-lucide="arrow-right" class="w-5 h-5"></i>
                                </div>
                            </a>
                            <div class="btn-glow"></div>
                        </div>
                    @endif
                @endguest
            </div>

            <!-- Enhanced Trust Indicators -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <div class="trust-indicator group">
                    <div class="trust-icon">
                        <i data-lucide="shield-check" class="w-6 h-6 text-green-500"></i>
                    </div>
                    <div class="trust-content">
                        <div class="trust-title">Sab Sahi Hai 100%</div>
                        <div class="trust-subtitle">Fake ID Nahi Milegi</div>
                    </div>
                </div>

                <div class="trust-divider"></div>

                <div class="trust-indicator group">
                    <div class="trust-icon">
                        <i data-lucide="users" class="w-6 h-6 text-indigo-500"></i>
                    </div>
                    <div class="trust-content">
                        <div class="trust-title">Ghar Ghar Ki Kahani</div>
                        <div class="trust-subtitle">Sabko Pata Hai</div>
                    </div>
                </div>

                <div class="trust-divider"></div>

                <div class="trust-indicator group">
                    <div class="trust-icon">
                        <i data-lucide="heart" class="w-6 h-6 text-rose-500"></i>
                    </div>
                    <div class="trust-content">
                        <div class="trust-title">Setting Guaranteed</div>
                        <div class="trust-subtitle">Pakka Wala</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Our Platform Section -->
    <section class="py-16 lg:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title mb-4">
                    Tum Idhar Kaiko Aaoge? 🤔
                </h2>
                <p class="section-subtitle max-w-3xl mx-auto">
                    Kyunki yahan sab kuch first class hai! Real offline events, cool people, aur guaranteed setting. Life mein thoda masti chahiye na bhai!
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1: Authenticity -->
                <div class="modern-card feature-card group hover:shadow-xl transition-all duration-300">
                    <div class="feature-icon bg-gradient-to-br from-blue-500 to-blue-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Sab Sahi Hai 100% 🔥</h3>
                    <p class="text-gray-600 leading-relaxed flex-grow">
                        Bhai ne angutha lagvake check karvaya hai isliye sab 100% sahi hi hai! Fake profiles ka koi chance nahi. Sirf genuine log milenge!
                    </p>
                    <div class="mt-6 pt-4 border-t border-gray-100">
                        <div class="flex items-center text-sm text-indigo-600 font-medium">
                            <i data-lucide="check-circle" class="w-4 h-4 mr-2"></i>
                            Angutha Laga Ke Guarantee
                        </div>
                    </div>
                </div>

                <!-- Feature 2: Offline Events -->
                <div class="modern-card feature-card group hover:shadow-xl transition-all duration-300">
                    <div class="feature-icon bg-gradient-to-br from-purple-500 to-indigo-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="map-pin" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Mast Jagah Pe Masti 🎉</h3>
                    <p class="text-gray-600 leading-relaxed flex-grow">
                        Bhai sahab, sirf best places pe events hote hain! CCD se lekar fancy restaurants tak. "Yahan toh scene hi kuch aur hai" wala feeling!
                    </p>
                    <div class="mt-6 pt-4 border-t border-gray-100">
                        <div class="flex items-center text-sm text-purple-600 font-medium">
                            <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                            Roz Kuch Na Kuch
                        </div>
                    </div>
                </div>

                <!-- Feature 3: Security -->
                <div class="modern-card feature-card group hover:shadow-xl transition-all duration-300">
                    <div class="feature-icon bg-gradient-to-br from-green-500 to-green-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="lock" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Security Ki Seal Hai 🛡️</h3>
                    <p class="text-gray-600 leading-relaxed flex-grow">
                        "Tension lene ka nahi, dene ka!" Tumhara data bilkul safe hai. Bank se bhi zyada secure! Koi bhi chapri nahi ghus sakta andar.
                    </p>
                    <div class="mt-6 pt-4 border-t border-gray-100">
                        <div class="flex items-center text-sm text-green-600 font-medium">
                            <i data-lucide="shield" class="w-4 h-4 mr-2"></i>
                            Fort Knox Level Security
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 lg:py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title mb-4">
                    Kaise Hota Hai Ye Sab? 🤯
                </h2>
                <p class="section-subtitle max-w-3xl mx-auto">
                    Bas 4 steps mein teri setting start! "Itna aasan hai?" Haan bhai, bilkul aasan hai. Dekh le!
                </p>
            </div>

            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Step 1: Sign Up -->
                <div class="step-card group hover:shadow-xl transition-all duration-300">
                    <div class="step-number group-hover:scale-110 transition-transform duration-300">1</div>
                    <div class="mb-6">
                        <i data-lucide="user-plus" class="w-16 h-16 mx-auto text-indigo-600 group-hover:scale-110 transition-transform duration-300"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Bas Ek Click Mein Entry</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Google se login kar aur ho gaya! "Itni jaldi?" Haan bhai, Maggi se bhi fast! 30 seconds mein andar!
                    </p>
                    <div class="text-sm text-indigo-600 font-medium">
                        ⚡ Maggi Se Bhi Fast
                    </div>
                </div>

                <!-- Step 2: Complete Profile -->
                <div class="step-card group hover:shadow-xl transition-all duration-300">
                    <div class="step-number group-hover:scale-110 transition-transform duration-300">2</div>
                    <div class="mb-6">
                        <i data-lucide="edit-3" class="w-16 h-16 mx-auto text-purple-600 group-hover:scale-110 transition-transform duration-300"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Apna Swag Dikhao</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Best photos daal, interests batao, aur preferences set kar. "Main kaun hun" wala vibe create kar! Profile dekh ke log bolenge "Ye toh mast hai!"
                    </p>
                    <div class="text-sm text-purple-600 font-medium">
                        📝 5 Min Mein Swag Ready
                    </div>
                </div>

                <!-- Step 3: Join Events -->
                <div class="step-card group hover:shadow-xl transition-all duration-300">
                    <div class="step-number group-hover:scale-110 transition-transform duration-300">3</div>
                    <div class="mb-6">
                        <i data-lucide="calendar-heart" class="w-16 h-16 mx-auto text-indigo-600 group-hover:scale-110 transition-transform duration-300"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Events Join Karo</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Events dekho, pasand aaye toh join karo! Coffee pe milna ho ya adventure pe jana ho - "Chal na yaar" bol ke nikal jao!
                    </p>
                    <div class="text-sm text-indigo-600 font-medium">
                        🎉 Roz Nayi Masti
                    </div>
                </div>

                <!-- Step 4: Connect -->
                <div class="step-card group hover:shadow-xl transition-all duration-300">
                    <div class="step-number group-hover:scale-110 transition-transform duration-300">4</div>
                    <div class="mb-6">
                        <i data-lucide="heart" class="w-16 h-16 mx-auto text-purple-600 group-hover:scale-110 transition-transform duration-300"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Setting Ho Gayi</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Real mein mil ke baat karo, activities karo together. "Ye toh mast hai" wala feeling aa jayega! Setting complete!
                    </p>
                    <div class="text-sm text-purple-600 font-medium">
                        🔥 Setting Guarantee
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose SettingWala Section -->
    <section class="py-16 lg:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <i data-lucide="sparkles" class="w-10 h-10 text-white"></i>
                </div>
                <h2 class="section-title mb-4">
                    <span class="text-indigo-600">SettingWala</span> Hi Kyun? Aur Koi Option Hai Kya? 😏
                </h2>
                <p class="section-subtitle max-w-4xl mx-auto">
                    Bhai, competition toh hai but humara level hi alag hai! Young people ke liye specially banaya hai. Lakhs log already setting karva chuke hain!
                </p>
            </div>

            <!-- Value Proposition Cards -->
            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Quick Setup -->
                <div class="value-card group hover:shadow-xl transition-all duration-300">
                    <div class="value-icon bg-gradient-to-br from-blue-500 to-blue-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="zap" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-3">Ek Dum Jhatpat</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        "Abhi abhi" wala setup! Google se login kar aur bas ho gaya. Koi form-varm nahi bharni. "Itni jaldi?" Haan bhai!
                    </p>
                    <div class="w-full bg-blue-50 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full w-full"></div>
                    </div>
                    <div class="text-xs text-blue-600 font-medium">Usain Bolt Se Bhi Fast</div>
                </div>

                <!-- Safe Environment -->
                <div class="value-card featured group hover:shadow-xl transition-all duration-300">
                    <div class="value-icon bg-gradient-to-br from-purple-500 to-indigo-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="shield-check" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-3">Bilkul Safe Hai Bhai</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Itna safe hai ki tension lene ki zarurat nahi! Verified profiles, safe places, aur full confidence ke saath setting karo!
                    </p>
                    <div class="w-full bg-indigo-50 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-2 rounded-full w-full"></div>
                    </div>
                    <div class="text-xs text-purple-600 font-medium">100% Verified</div>
                </div>

                <!-- Real Connections -->
                <div class="value-card group hover:shadow-xl transition-all duration-300">
                    <div class="value-icon bg-gradient-to-br from-emerald-500 to-emerald-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="users" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-3">Asli Wala Setting</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Apne city ke cool people se real mein milo! "Ye toh mere type ka hai" wala feeling guaranteed. Online chat nahi, real setting!
                    </p>
                    <div class="w-full bg-emerald-50 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full w-full"></div>
                    </div>
                    <div class="text-xs text-emerald-600 font-medium">Cool People</div>
                </div>

                <!-- Lasting Relationships -->
                <div class="value-card group hover:shadow-xl transition-all duration-300">
                    <div class="value-icon bg-gradient-to-br from-rose-500 to-pink-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="heart" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-3">Long Term Setting</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Yahan sirf timepass nahi, proper setting hoti hai! "Ye toh serious wala hai" type ka connection milta hai. Quality guarantee!
                    </p>
                    <div class="w-full bg-rose-50 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-rose-500 to-pink-600 h-2 rounded-full w-[95%]"></div>
                    </div>
                    <div class="text-xs text-rose-600 font-medium">95% Success Rate</div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-16 text-center">
                <div class="inline-flex items-center px-6 py-3 bg-white rounded-full border border-indigo-200 shadow-sm">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-green-500"></i>
                    <span class="text-gray-600 font-medium">Ghar Ghar Mein Famous</span>
                    <i data-lucide="heart" class="w-5 h-5 ml-2 text-rose-500"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 lg:py-20 bg-white">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="cta-card">
                <div class="mb-10">
                    <div class="w-24 h-24 mx-auto mb-8 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-xl">
                        <i data-lucide="users" class="w-12 h-12 text-white"></i>
                    </div>
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-6 leading-tight text-center">
                        Bas Kar Pagle,<br>
                        <span class="text-indigo-600">Rulayega Kya</span>? 😭
                    </h2>
                    <p class="text-lg text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed text-center">
                        Lakhs log already "Ye bhi theek hai" bol chuke hain aur apni setting karva li hai! Tu kab tak single rahega? Chal na yaar!
                    </p>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
                    @guest
                        <a href="{{ route('register') }}" class="btn-primary text-lg px-8 py-4">
                            <i data-lucide="users" class="w-6 h-6"></i>
                            Chal Bhai, Shuru Karte Hain
                        </a>
                        <a href="{{ route('static.how-it-works') }}" class="btn-secondary text-lg px-8 py-4">
                            <i data-lucide="play-circle" class="w-6 h-6"></i>
                            Pehle Dekh Toh Le
                        </a>
                    @else
                        <a href="{{ route('event.address') }}" class="btn-primary text-lg px-8 py-4">
                            <i data-lucide="calendar" class="w-6 h-6"></i>
                            Events Dekho
                        </a>
                    @endguest
                </div>

                <!-- Trust Indicators -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 pt-8 border-t border-gray-100">
                    <div class="flex items-center justify-center">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <i data-lucide="lock" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-bold text-gray-800">Bilkul Safe Hai</div>
                            <div class="text-sm text-gray-600">Fort Knox Level Security</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-center">
                        <div class="w-12 h-12 rounded-full bg-rose-100 flex items-center justify-center mr-4">
                            <i data-lucide="heart" class="w-6 h-6 text-rose-600"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-bold text-gray-800">Setting Guaranteed</div>
                            <div class="text-sm text-gray-600">95% Success Rate</div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Initialize Lucide Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>

</x-app-layout>
