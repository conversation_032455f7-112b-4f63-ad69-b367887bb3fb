@extends('layouts.admin')

@section('page-title', 'Settings')

@push('styles')
<style>
/* Simple Admin Settings Design */
.settings-page-header {
    background: #4f46e5;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    color: white;
}

.settings-container {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.settings-tabs {
    background: #f9fafb;
    border-right: 1px solid #e5e7eb;
    min-height: 500px;
    padding: 1rem;
}

.settings-tab-content {
    background: white;
    min-height: 500px;
    padding: 1.5rem;
}

.settings-tab-btn {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem;
    border: none;
    background: transparent;
    text-align: left;
    color: #6b7280;
    border-radius: 6px;
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.settings-tab-btn.active {
    background: #4f46e5;
    color: white;
}

.settings-tab-btn:hover:not(.active) {
    background: #f3f4f6;
    color: #374151;
}

.tab-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    background: #e5e7eb;
    color: #6b7280;
    font-size: 1rem;
}

.settings-tab-btn.active .tab-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.tab-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
}

.tab-description {
    font-size: 0.75rem;
    opacity: 0.8;
    margin: 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-header {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.tab-header-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: #4f46e5;
    color: white;
    font-size: 1.25rem;
}

.tab-header h5 {
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
}

.tab-header p {
    color: #6b7280;
    margin: 0;
    font-size: 0.875rem;
}

.settings-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.settings-card-header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.settings-card-body {
    padding: 1.5rem;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    background: #f9fafb;
}

.upload-area:hover {
    border-color: #4f46e5;
}

.form-check-input:checked {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

@media (max-width: 768px) {
    .settings-tabs {
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
        min-height: auto;
        padding: 1rem;
        display: flex;
        overflow-x: auto;
    }

    .settings-tab-btn {
        min-width: 150px;
        flex-shrink: 0;
    }

    .settings-tab-content {
        padding: 1rem;
    }

    .tab-header {
        padding: 1rem;
    }

    .settings-card-body {
        padding: 1rem;
    }
}

/* Database Management Styles */
.database-operation-card {
    transition: all 0.2s ease;
}

.database-operation-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text-sm {
    font-size: 0.875rem;
}

.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}
</style>
@endpush

@section('page-description', 'Configure application settings, authentication, payments, and system preferences.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Settings</span>
    </li>
@endsection

@section('content')
<div class="container-fluid px-4">
    <!-- Simple Page Header -->
    <div class="settings-page-header">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="bi bi-gear-fill" style="font-size: 2rem;"></i>
            </div>
            <div>
                <h1 class="mb-1">Application Settings</h1>
                <p class="mb-0">Configure your application settings, authentication, payments, and system preferences.</p>
            </div>
        </div>
    </div>

    <!-- Simple Settings Container -->
    <div class="settings-container">
        <div class="row g-0">
            <!-- Simple Vertical Tabs Navigation -->
            <div class="col-lg-3 col-md-4">
                <div class="settings-tabs">
                    <button type="button" class="settings-tab-btn active" data-tab="general" aria-label="General Settings">
                        <div class="tab-icon">
                            <i class="bi bi-house-gear"></i>
                        </div>
                        <div>
                            <div class="tab-title">General</div>
                            <p class="tab-description">Branding & basic settings</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="social-media" aria-label="Social Media Settings">
                        <div class="tab-icon">
                            <i class="bi bi-share"></i>
                        </div>
                        <div>
                            <div class="tab-title">Social Media</div>
                            <p class="tab-description">Social media links</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="authentication" aria-label="Authentication Settings">
                        <div class="tab-icon">
                            <i class="bi bi-shield-lock"></i>
                        </div>
                        <div>
                            <div class="tab-title">Authentication</div>
                            <p class="tab-description">Google OAuth settings</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="payments" aria-label="Payment Settings">
                        <div class="tab-icon">
                            <i class="bi bi-credit-card"></i>
                        </div>
                        <div>
                            <div class="tab-title">Payments</div>
                            <p class="tab-description">Razorpay & commission</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="notifications" aria-label="Notification Settings">
                        <div class="tab-icon">
                            <i class="bi bi-bell"></i>
                        </div>
                        <div>
                            <div class="tab-title">Notifications</div>
                            <p class="tab-description">Firebase & messaging</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="site-management" aria-label="Site Management Settings">
                        <div class="tab-icon">
                            <i class="bi bi-shield-exclamation"></i>
                        </div>
                        <div>
                            <div class="tab-title">Site Management</div>
                            <p class="tab-description">Maintenance & coming soon</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="security" aria-label="Security Settings">
                        <div class="tab-icon">
                            <i class="bi bi-lock"></i>
                        </div>
                        <div>
                            <div class="tab-title">Security</div>
                            <p class="tab-description">Access controls</p>
                        </div>
                    </button>

                    <button type="button" class="settings-tab-btn" data-tab="database-management" aria-label="Database Management">
                        <div class="tab-icon">
                            <i class="bi bi-database"></i>
                        </div>
                        <div>
                            <div class="tab-title">Database Management</div>
                            <p class="tab-description">Clean & export database</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Simple Tab Content -->
            <div class="col-lg-9 col-md-8">
                <div class="settings-tab-content">
                    <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data" onsubmit="return validateSiteModeSettings(event)" id="settingsForm">
                        @csrf

                        <!-- General Tab -->
                        <div class="tab-pane active" id="general-tab" role="tabpanel" aria-labelledby="general-tab-btn">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-house-gear"></i>
                                    </div>
                                    <div>
                                        <h5>General Settings</h5>
                                        <p>Manage your brand identity and basic application settings.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Logo & Branding Section -->
                            <div class="settings-card mb-4">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Logo & Branding</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-4">
                                        <!-- Header Logo -->
                                        <div class="col-md-6 col-lg-3">
                                            <label class="form-label">Header Logo</label>
                                            <div class="upload-area">
                                                @if(App\Models\Setting::get('header_logo'))
                                                    <div class="mb-3">
                                                        <img src="{{ asset('storage/' . App\Models\Setting::get('header_logo')) }}"
                                                             alt="Header Logo" class="img-fluid rounded" style="max-height: 80px;">
                                                    </div>
                                                @else
                                                    <div class="mb-3">
                                                        <i class="bi bi-image display-5 text-muted"></i>
                                                    </div>
                                                @endif
                                                <div class="mb-2">
                                                    <label for="header_logo" class="btn btn-primary btn-sm">
                                                        <i class="bi bi-upload me-2"></i>Choose File
                                                        <input id="header_logo" name="header_logo" type="file" accept="image/*" class="d-none">
                                                    </label>
                                                </div>
                                                <small class="text-muted d-block">PNG, JPG, SVG<br>Max: 2MB</small>
                                            </div>
                                        </div>

                                        <!-- Footer Logo -->
                                        <div class="col-md-6 col-lg-3">
                                            <label class="form-label">Footer Logo</label>
                                            <div class="upload-area">
                                                @if(App\Models\Setting::get('footer_logo'))
                                                    <div class="mb-3">
                                                        <img src="{{ asset('storage/' . App\Models\Setting::get('footer_logo')) }}"
                                                             alt="Footer Logo" class="img-fluid rounded" style="max-height: 80px;">
                                                    </div>
                                                @else
                                                    <div class="mb-3">
                                                        <i class="bi bi-image display-5 text-muted"></i>
                                                    </div>
                                                @endif
                                                <div class="mb-2">
                                                    <label for="footer_logo" class="btn btn-primary btn-sm">
                                                        <i class="bi bi-upload me-2"></i>Choose File
                                                        <input id="footer_logo" name="footer_logo" type="file" accept="image/*" class="d-none">
                                                    </label>
                                                </div>
                                                <small class="text-muted d-block">PNG, JPG, SVG<br>Max: 2MB</small>
                                            </div>
                                        </div>

                                        <!-- Mobile Icon -->
                                        <div class="col-md-6 col-lg-3">
                                            <label class="form-label">Mobile App Icon</label>
                                            <div class="upload-area">
                                                @if(App\Models\Setting::get('mobile_icon'))
                                                    <div class="mb-3">
                                                        <img src="{{ asset('storage/' . App\Models\Setting::get('mobile_icon')) }}"
                                                             alt="Mobile App Icon" class="img-fluid rounded" style="max-height: 80px;">
                                                    </div>
                                                @else
                                                    <div class="mb-3">
                                                        <i class="bi bi-phone display-5 text-muted"></i>
                                                    </div>
                                                @endif
                                                <div class="mb-2">
                                                    <label for="mobile_icon" class="btn btn-primary btn-sm">
                                                        <i class="bi bi-upload me-2"></i>Choose File
                                                        <input id="mobile_icon" name="mobile_icon" type="file" accept="image/*" class="d-none">
                                                    </label>
                                                </div>
                                                <small class="text-muted d-block">PNG, JPG<br>512x512px</small>
                                            </div>
                                        </div>

                                        <!-- Favicon -->
                                        <div class="col-md-6 col-lg-3">
                                            <label class="form-label">Favicon</label>
                                            <div class="upload-area">
                                                @if(App\Models\Setting::get('favicon'))
                                                    <div class="mb-3">
                                                        <img src="{{ asset('storage/' . App\Models\Setting::get('favicon')) }}"
                                                             alt="Favicon" class="img-fluid rounded" style="max-height: 80px;">
                                                    </div>
                                                @else
                                                    <div class="mb-3">
                                                        <i class="bi bi-star display-5 text-muted"></i>
                                                    </div>
                                                @endif
                                                <div class="mb-2">
                                                    <label for="favicon" class="btn btn-primary btn-sm">
                                                        <i class="bi bi-upload me-2"></i>Choose File
                                                        <input id="favicon" name="favicon" type="file" accept="image/*" class="d-none">
                                                    </label>
                                                </div>
                                                <small class="text-muted d-block">ICO, PNG<br>32x32px</small>
                                            </div>
                                        </div>

                                        <!-- Copyright Text -->
                                        <div class="col-12">
                                            <div class="settings-card">
                                                <div class="settings-card-header">
                                                    <h6 class="mb-0">Copyright Information</h6>
                                                </div>
                                                <div class="settings-card-body">
                                                    <div class="mb-3">
                                                        <label for="copyright_text" class="form-label">Copyright Text</label>
                                                        <textarea id="copyright_text" name="settings[copyright_text]" class="form-control" rows="3" placeholder="© 2024 Your Company Name. All rights reserved.">{{ App\Models\Setting::get('copyright_text') }}</textarea>
                                                        <div class="form-text">This text will appear in the footer of your website.</div>
                                                    </div>
                                                    <div class="p-3 rounded bg-light border">
                                                        <p class="text-muted mb-2 small">Preview:</p>
                                                        <div id="copyright_preview" class="text-muted small">{{ App\Models\Setting::get('copyright_text') ?: 'Copyright text will appear here...' }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- PWA Theme Settings -->
                                        <div class="col-12">
                                            <div class="settings-card">
                                                <div class="settings-card-header">
                                                    <h6 class="mb-0">PWA Theme Settings</h6>
                                                </div>
                                                <div class="settings-card-body">
                                                    <div class="row g-3">
                                                        <div class="col-md-6">
                                                            <label for="theme_color" class="form-label">Theme Color</label>
                                                            <div class="input-group">
                                                                <input type="color" id="theme_color" name="settings[theme_color]" class="form-control form-control-color" value="{{ App\Models\Setting::get('theme_color', '#C9B6E4') }}" title="Choose theme color">
                                                                <input type="text" class="form-control" value="{{ App\Models\Setting::get('theme_color', '#C9B6E4') }}" readonly>
                                                            </div>
                                                            <div class="form-text">This color will be used for PWA theme and browser address bar</div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="background_color" class="form-label">Background Color</label>
                                                            <div class="input-group">
                                                                <input type="color" id="background_color" name="settings[background_color]" class="form-control form-control-color" value="{{ App\Models\Setting::get('background_color', '#ffffff') }}" title="Choose background color">
                                                                <input type="text" class="form-control" value="{{ App\Models\Setting::get('background_color', '#ffffff') }}" readonly>
                                                            </div>
                                                            <div class="form-text">Background color for PWA splash screen</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <!-- Social Media Tab -->
                        <div class="tab-pane" id="social-media-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-share"></i>
                                    </div>
                                    <div>
                                        <h5>Social Media Settings</h5>
                                        <p>Configure your social media links to display in the footer.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Social Media Links</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="facebook_url" class="form-label">Facebook URL</label>
                                            <input type="url" id="facebook_url" name="settings[facebook_url]" class="form-control" value="{{ App\Models\Setting::get('facebook_url') }}" placeholder="https://facebook.com/yourpage">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="twitter_url" class="form-label">Twitter URL</label>
                                            <input type="url" id="twitter_url" name="settings[twitter_url]" class="form-control" value="{{ App\Models\Setting::get('twitter_url') }}" placeholder="https://twitter.com/yourhandle">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="instagram_url" class="form-label">Instagram URL</label>
                                            <input type="url" id="instagram_url" name="settings[instagram_url]" class="form-control" value="{{ App\Models\Setting::get('instagram_url') }}" placeholder="https://instagram.com/yourhandle">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                                            <input type="url" id="linkedin_url" name="settings[linkedin_url]" class="form-control" value="{{ App\Models\Setting::get('linkedin_url') }}" placeholder="https://linkedin.com/company/yourcompany">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="youtube_url" class="form-label">YouTube URL</label>
                                            <input type="url" id="youtube_url" name="settings[youtube_url]" class="form-control" value="{{ App\Models\Setting::get('youtube_url') }}" placeholder="https://youtube.com/channel/yourchannel">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="whatsapp_url" class="form-label">WhatsApp URL</label>
                                            <input type="url" id="whatsapp_url" name="settings[whatsapp_url]" class="form-control" value="{{ App\Models\Setting::get('whatsapp_url') }}" placeholder="https://wa.me/1234567890">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <!-- Authentication Tab -->
                        <div class="tab-pane" id="authentication-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="tab-header-icon">
                                            <i class="bi bi-shield-lock"></i>
                                        </div>
                                        <div>
                                            <h5>Authentication Settings</h5>
                                            <p>Configure Google OAuth integration and authentication preferences.</p>
                                        </div>
                                    </div>
                                    <div>
                                        @php
                                            $googleClientId = App\Models\Setting::get('google_client_id');
                                            $googleClientSecret = App\Models\Setting::get('google_client_secret');
                                            $isGoogleConfigured = !empty($googleClientId) && !empty($googleClientSecret);
                                        @endphp
                                        @if($isGoogleConfigured)
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-warning">Setup Required</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Google OAuth Configuration</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="google_client_id" class="form-label">Google Client ID</label>
                                            <input type="text" id="google_client_id" name="settings[google_client_id]" class="form-control" value="{{ App\Models\Setting::get('google_client_id') }}" placeholder="Enter Google Client ID">
                                            <div class="form-text">Client ID from Google Cloud Console</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="google_client_secret" class="form-label">Google Client Secret</label>
                                            <input type="password" id="google_client_secret" name="settings[google_client_secret]" class="form-control" value="{{ App\Models\Setting::get('google_client_secret') }}" placeholder="Enter Google Client Secret">
                                            <div class="form-text">Client Secret from Google Cloud Console</div>
                                        </div>
                                        <div class="col-12">
                                            <label for="google_redirect_uri" class="form-label">Google Redirect URI</label>
                                            <input type="url" id="google_redirect_uri" name="settings[google_redirect_uri]" class="form-control" value="{{ App\Models\Setting::get('google_redirect_uri', url('/auth/callback/google')) }}" placeholder="https://yourdomain.com/auth/callback/google">
                                            <div class="form-text">This URL must be added to your Google OAuth app's authorized redirect URIs</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <!-- Payments Tab -->
                        <div class="tab-pane" id="payments-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="tab-header-icon">
                                            <i class="bi bi-credit-card"></i>
                                        </div>
                                        <div>
                                            <h5>Payment Settings</h5>
                                            <p>Configure Razorpay integration and payment processing options.</p>
                                        </div>
                                    </div>
                                    <div>
                                        @php
                                            $razorpayKeyId = App\Models\Setting::get('razorpay_key_id');
                                            $razorpayKeySecret = App\Models\Setting::get('razorpay_key_secret');
                                            $isConfigured = !empty($razorpayKeyId) && !empty($razorpayKeySecret);
                                        @endphp
                                        @if($isConfigured)
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-warning">Setup Required</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Razorpay Configuration</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="razorpay_key_id" class="form-label">Razorpay Key ID</label>
                                            <input type="text" id="razorpay_key_id" name="settings[razorpay_key_id]" class="form-control" value="{{ App\Models\Setting::get('razorpay_key_id') }}" placeholder="rzp_test_...">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="razorpay_key_secret" class="form-label">Razorpay Key Secret</label>
                                            <input type="password" id="razorpay_key_secret" name="settings[razorpay_key_secret]" class="form-control" value="{{ App\Models\Setting::get('razorpay_key_secret') }}" placeholder="Enter secret key">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="platform_fee" class="form-label">Platform Fee (₹)</label>
                                            <input type="number" id="platform_fee" name="settings[platform_fee]" class="form-control" value="{{ App\Models\Setting::get('platform_fee', 0) }}" min="0" max="10000" step="0.01">
                                            <div class="form-text">Fixed platform fee in INR applied to all time spending bookings</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="commission_percentage" class="form-label">Commission Percentage (%)</label>
                                            <input type="number" id="commission_percentage" name="settings[commission_percentage]" class="form-control" value="{{ App\Models\Setting::get('commission_percentage', 10) }}" min="0" max="100" step="0.01">
                                            <div class="form-text">Commission percentage deducted from provider earnings</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Withdrawal Settings -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Withdrawal Settings</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="minimum_withdrawal_amount" class="form-label">Minimum Withdrawal Amount (₹)</label>
                                            <input type="number" id="minimum_withdrawal_amount" name="settings[minimum_withdrawal_amount]" class="form-control" value="{{ App\Models\Setting::get('minimum_withdrawal_amount', 100) }}" min="1" max="10000" step="1">
                                            <div class="form-text">Minimum amount required in wallet to request withdrawal</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="maximum_withdrawal_amount" class="form-label">Maximum Withdrawal Amount (₹)</label>
                                            <input type="number" id="maximum_withdrawal_amount" name="settings[maximum_withdrawal_amount]" class="form-control" value="{{ App\Models\Setting::get('maximum_withdrawal_amount', 50000) }}" min="100" max="1000000" step="1">
                                            <div class="form-text">Maximum amount that can be withdrawn per request</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="daily_withdrawal_limit" class="form-label">Daily Withdrawal Limit (₹)</label>
                                            <input type="number" id="daily_withdrawal_limit" name="settings[daily_withdrawal_limit]" class="form-control" value="{{ App\Models\Setting::get('daily_withdrawal_limit', 100000) }}" min="1000" max="10000000" step="1">
                                            <div class="form-text">Maximum amount that can be withdrawn per day</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="withdrawal_processing_fee" class="form-label">Withdrawal Processing Fee (₹)</label>
                                            <input type="number" id="withdrawal_processing_fee" name="settings[withdrawal_processing_fee]" class="form-control" value="{{ App\Models\Setting::get('withdrawal_processing_fee', 5) }}" min="0" max="100" step="0.01">
                                            <div class="form-text">Processing fee charged for each withdrawal request</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="withdrawal_processing_time" class="form-label">Withdrawal Processing Time</label>
                                            <input type="text" id="withdrawal_processing_time" name="settings[withdrawal_processing_time]" class="form-control" value="{{ App\Models\Setting::get('withdrawal_processing_time', '1-3 business days') }}" placeholder="1-3 business days">
                                            <div class="form-text">Expected processing time displayed to users</div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mt-4">
                                                <input class="form-check-input" type="checkbox" id="auto_withdrawal_enabled" name="settings[auto_withdrawal_enabled]" value="true" {{ App\Models\Setting::get('auto_withdrawal_enabled') === 'true' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="auto_withdrawal_enabled">
                                                    Enable Auto Withdrawal
                                                </label>
                                            </div>
                                            <div class="form-text">Automatically process withdrawals via payment gateway</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <!-- Notification Settings Tab -->
                        <div class="tab-pane" id="notifications-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="tab-header-icon">
                                            <i class="bi bi-bell"></i>
                                        </div>
                                        <div>
                                            <h5>Notification Settings</h5>
                                            <p>Configure Firebase Cloud Messaging for push notifications.</p>
                                        </div>
                                    </div>
                                    <div>
                                        @php
                                            $firebaseProjectId = App\Models\Setting::get('firebase_project_id');
                                            $firebaseClientEmail = App\Models\Setting::get('firebase_client_email');
                                            $firebasePrivateKey = App\Models\Setting::get('firebase_private_key');
                                            $isFirebaseConfigured = !empty($firebaseProjectId) && !empty($firebaseClientEmail) && !empty($firebasePrivateKey);
                                        @endphp
                                        @if($isFirebaseConfigured)
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-warning">Setup Required</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Firebase Cloud Messaging Configuration</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="firebase_project_id" class="form-label">Firebase Project ID</label>
                                            <input type="text" id="firebase_project_id" name="settings[firebase_project_id]" class="form-control" value="{{ App\Models\Setting::get('firebase_project_id') }}" placeholder="your-project-id">
                                            <div class="form-text">Project ID from Firebase Console</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="firebase_client_email" class="form-label">Firebase Client Email</label>
                                            <input type="email" id="firebase_client_email" name="settings[firebase_client_email]" class="form-control" value="{{ App\Models\Setting::get('firebase_client_email') }}" placeholder="<EMAIL>">
                                            <div class="form-text">Service account email from Firebase</div>
                                        </div>
                                        <div class="col-12">
                                            <label for="firebase_private_key" class="form-label">Firebase Private Key</label>
                                            <textarea id="firebase_private_key" name="settings[firebase_private_key]" class="form-control" rows="4" placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----">{{ App\Models\Setting::get('firebase_private_key') }}</textarea>
                                            <div class="form-text">Private key from Firebase service account JSON file</div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-outline-primary" onclick="testFirebaseConfiguration()">
                                                    <i class="bi bi-wifi me-2"></i>Test Configuration
                                                </button>
                                                <button type="button" class="btn btn-outline-info" onclick="loadNotificationStatistics()">
                                                    <i class="bi bi-graph-up me-2"></i>View Statistics
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Statistics Card -->
                            <div class="settings-card" id="notification-stats-card" style="display: none;">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Notification Delivery Statistics</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div id="notification-stats-content">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <div class="tab-pane" id="site-management-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-shield-exclamation"></i>
                                    </div>
                                    <div>
                                        <h5>Site Management</h5>
                                        <p>Configure maintenance mode, coming soon mode, and site status settings.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Maintenance Mode -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Maintenance Mode</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="maintenance_mode_enabled" name="settings[maintenance_mode_enabled]" value="1" {{ App\Models\Setting::get('maintenance_mode_enabled') == '1' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="maintenance_mode_enabled">
                                                    Enable Maintenance Mode
                                                </label>
                                            </div>
                                            <div class="form-text">When enabled, only admin users can access the site</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="maintenance_mode_end_time" class="form-label">Maintenance End Time (Optional)</label>
                                            <input type="datetime-local" id="maintenance_mode_end_time" name="settings[maintenance_mode_end_time]" class="form-control" value="{{ App\Models\Setting::get('maintenance_mode_end_time') ? \Carbon\Carbon::parse(App\Models\Setting::get('maintenance_mode_end_time'))->format('Y-m-d\TH:i') : '' }}">
                                            <div class="form-text">Maintenance mode will auto-disable at this time</div>
                                        </div>
                                        <div class="col-12">
                                            <label for="maintenance_mode_message" class="form-label">Maintenance Message</label>
                                            <textarea id="maintenance_mode_message" name="settings[maintenance_mode_message]" class="form-control" rows="3" placeholder="We are currently under maintenance. Please check back later.">{{ App\Models\Setting::get('maintenance_mode_message', 'We are currently under maintenance. The website will be back online approximately at {end_time}.') }}</textarea>
                                            <div class="form-text">Use {end_time} placeholder to show maintenance end time</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Coming Soon Mode -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Coming Soon Mode</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="coming_soon_mode_enabled" name="settings[coming_soon_mode_enabled]" value="1" {{ App\Models\Setting::get('coming_soon_mode_enabled') == '1' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="coming_soon_mode_enabled">
                                                    Enable Coming Soon Mode
                                                </label>
                                            </div>
                                            <div class="form-text">When enabled, shows a coming soon page to visitors</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="coming_soon_launch_time" class="form-label">Launch Time (Optional)</label>
                                            <input type="datetime-local" id="coming_soon_launch_time" name="settings[coming_soon_launch_time]" class="form-control" value="{{ App\Models\Setting::get('coming_soon_launch_time') ? \Carbon\Carbon::parse(App\Models\Setting::get('coming_soon_launch_time'))->format('Y-m-d\TH:i') : '' }}">
                                            <div class="form-text">Coming soon mode will auto-disable at this time</div>
                                        </div>
                                        <div class="col-12">
                                            <label for="coming_soon_message" class="form-label">Coming Soon Message</label>
                                            <textarea id="coming_soon_message" name="settings[coming_soon_message]" class="form-control" rows="3" placeholder="We're launching soon! Stay tuned.">{{ App\Models\Setting::get('coming_soon_message', 'We\'re launching soon! Our amazing platform will be available at {launch_time}.') }}</textarea>
                                            <div class="form-text">Use {launch_time} placeholder to show launch time</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <div class="tab-pane" id="security-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-lock"></i>
                                    </div>
                                    <div>
                                        <h5>Security Settings</h5>
                                        <p>Configure access controls and security preferences for your application.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Admin Panel Security</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="custom_admin_url" class="form-label">Custom Admin URL Path</label>
                                            <input type="text" id="custom_admin_url" name="settings[custom_admin_url]" class="form-control" value="{{ App\Models\Setting::get('custom_admin_url') }}" placeholder="my-secret-admin">
                                            <div class="form-text">Leave empty to use default /admin/ path. Only alphanumeric characters, hyphens, and underscores allowed.</div>
                                        </div>
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                <strong>Important:</strong> If you set a custom admin URL, make sure to bookmark the new URL before saving. The current admin URL will no longer work.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>

                        <!-- Database Management Tab -->
                        <div class="tab-pane" id="database-management-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-database"></i>
                                    </div>
                                    <div>
                                        <h5>Database Management</h5>
                                        <p>Manage database operations including cleaning and exporting data.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Database Operations</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-4">
                                        <!-- Clean Database Section -->
                                        <div class="col-md-6">
                                            <div class="border rounded p-3 h-100">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="me-3">
                                                        <i class="bi bi-trash3 text-danger" style="font-size: 1.5rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Clean Database</h6>
                                                        <small class="text-muted">Remove all user data while preserving essential records</small>
                                                    </div>
                                                </div>
                                                <p class="text-sm mb-3">
                                                    This operation will permanently delete all user data, bookings, transactions, and notifications.
                                                    <strong>Subscription plans, features, and settings will be preserved.</strong>
                                                </p>
                                                <div class="alert alert-warning alert-sm mb-3">
                                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                                    <strong>Warning:</strong> This action cannot be undone!
                                                </div>
                                                <button type="button" class="btn btn-danger btn-sm w-100" id="cleanDatabaseBtn">
                                                    <i class="bi bi-trash3 me-2"></i>Clean Database
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Export Database Section -->
                                        <div class="col-md-6">
                                            <div class="border rounded p-3 h-100">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="me-3">
                                                        <i class="bi bi-download text-primary" style="font-size: 1.5rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Export Database</h6>
                                                        <small class="text-muted">Download a complete backup of your database</small>
                                                    </div>
                                                </div>
                                                <p class="text-sm mb-3">
                                                    Export your entire database to a downloadable file. Choose from multiple formats
                                                    including SQL dump and JSON export.
                                                </p>
                                                <div class="mb-3">
                                                    <label for="exportFormat" class="form-label">Export Format</label>
                                                    <select class="form-select form-select-sm" id="exportFormat">
                                                        <option value="sql">SQL Dump (.sql)</option>
                                                        <option value="json">JSON Export (.json)</option>
                                                    </select>
                                                </div>
                                                <button type="button" class="btn btn-primary btn-sm w-100" id="exportDatabaseBtn">
                                                    <i class="bi bi-download me-2"></i>Export Database
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Database Management Tab -->
                        <div class="tab-pane" id="database-management-tab" role="tabpanel">
                            <div class="tab-header">
                                <div class="d-flex align-items-center">
                                    <div class="tab-header-icon">
                                        <i class="bi bi-database"></i>
                                    </div>
                                    <div>
                                        <h5>Database Management</h5>
                                        <p>Manage database operations including cleaning and exporting data.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h6 class="mb-0">Database Operations</h6>
                                </div>
                                <div class="settings-card-body">
                                    <div class="row g-4">
                                        <!-- Clean Database Section -->
                                        <div class="col-md-6">
                                            <div class="border rounded p-3 h-100">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="me-3">
                                                        <i class="bi bi-trash3 text-danger" style="font-size: 1.5rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Clean Database</h6>
                                                        <small class="text-muted">Remove all user data while preserving essential records</small>
                                                    </div>
                                                </div>
                                                <p class="text-sm mb-3">
                                                    This operation will permanently delete all user data, bookings, transactions, and notifications.
                                                    <strong>Subscription plans, features, and settings will be preserved.</strong>
                                                </p>
                                                <div class="alert alert-warning alert-sm mb-3">
                                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                                    <strong>Warning:</strong> This action cannot be undone!
                                                </div>
                                                <button type="button" class="btn btn-danger btn-sm w-100" id="cleanDatabaseBtnTab">
                                                    <i class="bi bi-trash3 me-2"></i>Clean Database
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Export Database Section -->
                                        <div class="col-md-6">
                                            <div class="border rounded p-3 h-100">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="me-3">
                                                        <i class="bi bi-download text-primary" style="font-size: 1.5rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Export Database</h6>
                                                        <small class="text-muted">Download a complete backup of your database</small>
                                                    </div>
                                                </div>
                                                <p class="text-sm mb-3">
                                                    Export your entire database to a downloadable file. Choose from multiple formats
                                                    including SQL dump and JSON export.
                                                </p>
                                                <div class="mb-3">
                                                    <label for="exportFormatTab" class="form-label">Export Format</label>
                                                    <select class="form-select form-select-sm" id="exportFormatTab">
                                                        <option value="sql">SQL Dump (.sql)</option>
                                                        <option value="json">JSON Export (.json)</option>
                                                    </select>
                                                </div>
                                                <button type="button" class="btn btn-primary btn-sm w-100" id="exportDatabaseBtnTab">
                                                    <i class="bi bi-download me-2"></i>Export Database
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Simple tab switching
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.settings-tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });

    // Copyright preview
    const copyrightInput = document.getElementById('copyright_text');
    const copyrightPreview = document.getElementById('copyright_preview');

    if (copyrightInput && copyrightPreview) {
        copyrightInput.addEventListener('input', function() {
            copyrightPreview.textContent = this.value || 'Copyright text will appear here...';
        });
    }

    // Color picker functionality
    const themeColorPicker = document.getElementById('theme_color');
    const backgroundColorPicker = document.getElementById('background_color');

    if (themeColorPicker) {
        const themeColorText = themeColorPicker.parentElement.querySelector('input[type="text"]');
        themeColorPicker.addEventListener('input', function() {
            themeColorText.value = this.value;
        });
    }

    if (backgroundColorPicker) {
        const backgroundColorText = backgroundColorPicker.parentElement.querySelector('input[type="text"]');
        backgroundColorPicker.addEventListener('input', function() {
            backgroundColorText.value = this.value;
        });
    }

    // Database Management functionality
    const cleanDatabaseBtn = document.getElementById('cleanDatabaseBtn');
    const cleanDatabaseBtnTab = document.getElementById('cleanDatabaseBtnTab');
    const exportDatabaseBtn = document.getElementById('exportDatabaseBtn');
    const exportDatabaseBtnTab = document.getElementById('exportDatabaseBtnTab');
    const exportFormat = document.getElementById('exportFormat');
    const exportFormatTab = document.getElementById('exportFormatTab');

    if (cleanDatabaseBtn) {
        cleanDatabaseBtn.addEventListener('click', function() {
            showCleanDatabaseConfirmation();
        });
    }

    if (cleanDatabaseBtnTab) {
        cleanDatabaseBtnTab.addEventListener('click', function() {
            showCleanDatabaseConfirmation();
        });
    }

    if (exportDatabaseBtn) {
        exportDatabaseBtn.addEventListener('click', function() {
            const format = exportFormat ? exportFormat.value : 'sql';
            exportDatabase(format);
        });
    }

    if (exportDatabaseBtnTab) {
        exportDatabaseBtnTab.addEventListener('click', function() {
            const format = exportFormatTab ? exportFormatTab.value : 'sql';
            exportDatabase(format);
        });
    }
});

// Database Management Functions
function showCleanDatabaseConfirmation() {
    const confirmed = confirm(
        'WARNING: This action will permanently delete ALL user data, bookings, transactions, and notifications.\n\n' +
        'Only subscription plans, features, and settings will be preserved.\n\n' +
        'This action CANNOT be undone!\n\n' +
        'Are you absolutely sure you want to continue?'
    );

    if (confirmed) {
        const doubleConfirmed = confirm(
            'FINAL CONFIRMATION:\n\n' +
            'You are about to delete all user data from the database.\n\n' +
            'Type "DELETE" in the next prompt to confirm.'
        );

        if (doubleConfirmed) {
            const userInput = prompt('Type "DELETE" to confirm:');
            if (userInput === 'DELETE') {
                cleanDatabase();
            } else {
                alert('Operation cancelled. You must type "DELETE" exactly to confirm.');
            }
        }
    }
}

function cleanDatabase() {
    const btn = document.getElementById('cleanDatabaseBtn');
    const btnTab = document.getElementById('cleanDatabaseBtnTab');

    // Determine which button was clicked
    const activeBtn = btn && !btn.disabled ? btn : btnTab;
    if (!activeBtn) return;

    const originalText = activeBtn.innerHTML;

    // Show loading state on both buttons
    [btn, btnTab].forEach(button => {
        if (button) {
            button.classList.add('btn-loading');
            button.innerHTML = '<span class="me-2">Cleaning...</span>';
            button.disabled = true;
        }
    });

    fetch('{{ route("admin.settings.clean-database") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Success: ' + data.message);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while cleaning the database.');
    })
    .finally(() => {
        // Reset button state on both buttons
        [btn, btnTab].forEach(button => {
            if (button) {
                button.classList.remove('btn-loading');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        });
    });
}

function exportDatabase(format) {
    const confirmed = confirm(
        'Export Database\n\n' +
        'This will create a backup of your entire database in ' + format.toUpperCase() + ' format.\n\n' +
        'The export may take a few moments depending on the size of your database.\n\n' +
        'Do you want to continue?'
    );

    if (!confirmed) {
        return;
    }

    const btn = document.getElementById('exportDatabaseBtn');
    const originalText = btn.innerHTML;

    // Show loading state
    btn.classList.add('btn-loading');
    btn.innerHTML = '<span class="me-2">Exporting...</span>';
    btn.disabled = true;

    // Create a form to submit the export request
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("admin.settings.export-database") }}';
    form.style.display = 'none';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfInput);

    // Add format input
    const formatInput = document.createElement('input');
    formatInput.type = 'hidden';
    formatInput.name = 'format';
    formatInput.value = format;
    form.appendChild(formatInput);

    document.body.appendChild(form);

    // Handle potential errors
    try {
        form.submit();
        document.body.removeChild(form);

        // Show success message after a delay
        setTimeout(() => {
            alert('Export initiated successfully. The download should start shortly.');
        }, 1000);

    } catch (error) {
        console.error('Export error:', error);
        alert('An error occurred while initiating the export.');
        document.body.removeChild(form);
    }

    // Reset button state after a short delay
    setTimeout(() => {
        btn.classList.remove('btn-loading');
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 3000);
}

// Simple form validation
function validateSiteModeSettings(event) {
    return true; // Simple validation for now
}

// Firebase Configuration Testing
function testFirebaseConfiguration() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Testing...';
    button.disabled = true;

    fetch('{{ route("admin.api.notifications.test-firebase") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Firebase Configuration Test', data.message);
        } else {
            showAlert('danger', 'Firebase Configuration Test Failed', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Test Failed', 'An error occurred while testing Firebase configuration.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Load Notification Statistics
function loadNotificationStatistics() {
    const statsCard = document.getElementById('notification-stats-card');
    const statsContent = document.getElementById('notification-stats-content');

    statsCard.style.display = 'block';
    statsContent.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    fetch('{{ route("admin.api.notifications.statistics") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayNotificationStatistics(data.data);
        } else {
            statsContent.innerHTML = '<div class="alert alert-danger">Failed to load statistics.</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        statsContent.innerHTML = '<div class="alert alert-danger">An error occurred while loading statistics.</div>';
    });
}

// Display Notification Statistics
function displayNotificationStatistics(stats) {
    const statsContent = document.getElementById('notification-stats-content');

    statsContent.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="card-title">${stats.total_notifications || 0}</h4>
                        <p class="card-text">Total Notifications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="card-title">${stats.firebase_stats.successful || 0}</h4>
                        <p class="card-text">Successfully Sent</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4 class="card-title">${stats.firebase_stats.failed || 0}</h4>
                        <p class="card-text">Failed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4 class="card-title">${stats.firebase_stats.success_rate || 0}%</h4>
                        <p class="card-text">Success Rate</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">${stats.notifications_today || 0}</h5>
                        <p class="card-text">Today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">${stats.notifications_this_week || 0}</h5>
                        <p class="card-text">This Week</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">${stats.users_with_fcm_tokens || 0}</h5>
                        <p class="card-text">Users with FCM Tokens</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Helper function to show alerts
function showAlert(type, title, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <strong>${title}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Insert alert at the top of the notifications tab
    const notificationsTab = document.getElementById('notifications-tab');
    const firstCard = notificationsTab.querySelector('.settings-card');
    firstCard.insertAdjacentHTML('beforebegin', alertHtml);

    // Auto-remove success alerts after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            const alert = notificationsTab.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
@endpush
@endsection
