@extends('layouts.admin')

@section('page-title', 'Dashboard')

@section('page-description', 'Comprehensive analytics and insights for your dating app platform.')

@section('content')
<div class="container-fluid">
    <!-- Time Filter Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-speedometer2 me-2 text-primary"></i>Admin Dashboard
                        </h5>
                        <div class="d-flex flex-wrap gap-2">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm time-filter" data-filter="today">Today</button>
                                <button type="button" class="btn btn-outline-primary btn-sm time-filter" data-filter="week">This Week</button>
                                <button type="button" class="btn btn-primary btn-sm time-filter" data-filter="month">This Month</button>
                                <button type="button" class="btn btn-outline-primary btn-sm time-filter" data-filter="year">This Year</button>
                            </div>
                            <div class="d-flex gap-2">
                                <input type="date" id="startDate" class="form-control form-control-sm" style="width: 140px;">
                                <input type="date" id="endDate" class="form-control form-control-sm" style="width: 140px;">
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="customFilter">Apply</button>
                            </div>
                            <button type="button" class="btn btn-success btn-sm" id="exportData">
                                <i class="bi bi-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="row g-4 mb-4">
        <!-- Total Users -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <i class="bi bi-people text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Total Users</p>
                                    <h3 class="mb-0 fw-bold" id="totalUsers">{{ number_format($overviewStats['total_users']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Events -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="bi bi-calendar-event text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Total Events</p>
                                    <h3 class="mb-0 fw-bold" id="totalEvents">{{ number_format($overviewStats['total_events']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                                <i class="bi bi-currency-rupee text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Total Revenue</p>
                                    <h3 class="mb-0 fw-bold" id="totalRevenue">₹{{ number_format($overviewStats['total_revenue'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Platform Fees -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                <i class="bi bi-cash-stack text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Platform Fees</p>
                                    <h3 class="mb-0 fw-bold" id="totalPlatformFees">₹{{ number_format($overviewStats['total_platform_fees'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                                <i class="bi bi-credit-card text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Active Subscriptions</p>
                                    <h3 class="mb-0 fw-bold" id="activeSubscriptions">{{ number_format($overviewStats['active_subscriptions']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Reports -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                                <i class="bi bi-flag text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Pending Reports</p>
                                    <h3 class="mb-0 fw-bold" id="pendingReports">{{ number_format($overviewStats['pending_reports'] ?? 0) }}</h3>
                                </div>
                                <div>
                                    <a href="{{ route('admin.reports.index') }}" class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row g-4 mb-4">
        <!-- Revenue Analytics Chart -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-currency-rupee me-2 text-success"></i>Revenue Analytics
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- User Registration Distribution Chart -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-pie-chart me-2 text-primary"></i>User Registrations
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="userRegistrationChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row g-4 mb-4">
        <!-- Active Subscriptions Distribution -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-pie-chart me-2 text-info"></i>Active Subscriptions
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="activeSubscriptionChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Revenue by Source -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-pie-chart-fill me-2 text-success"></i>Revenue by Source
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueSourceChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature Usage Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-calendar-event text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="totalBookings">{{ number_format($overviewStats['total_bookings']) }}</h4>
                    <p class="text-muted mb-0">Total Bookings</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-calendar-heart text-danger" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="eventParticipants">{{ number_format($overviewStats['event_participants']) }}</h4>
                    <p class="text-muted mb-0">Event Participants</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-chat-dots text-info" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="chatMessages">{{ number_format($overviewStats['chat_messages']) }}</h4>
                    <p class="text-muted mb-0">Chat Messages</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-people-fill text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="totalActiveUsers">{{ number_format($overviewStats['total_users']) }}</h4>
                    <p class="text-muted mb-0">Active Users</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Submissions Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-envelope text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="contactSubmissions">{{ number_format($overviewStats['contact_submissions'] ?? 0) }}</h4>
                    <p class="text-muted mb-0">Contact Submissions</p>
                    <a href="{{ route('admin.contact.index') }}" class="btn btn-outline-primary btn-sm mt-2">
                        <i class="bi bi-arrow-right"></i> View All
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="fw-bold mb-1" id="pendingContacts">{{ number_format($overviewStats['pending_contacts'] ?? 0) }}</h4>
                    <p class="text-muted mb-0">Pending Contacts</p>
                    <a href="{{ route('admin.contact.index') }}?status=pending" class="btn btn-outline-warning btn-sm mt-2">
                        <i class="bi bi-arrow-right"></i> Review Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="d-none position-fixed top-0 start-0 w-100 h-100" style="background: rgba(255,255,255,0.8); z-index: 9999;">
    <div class="d-flex align-items-center justify-content-center h-100">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Analytics JavaScript -->
<script src="{{ asset('js/admin-analytics.js') }}?v={{ time() }}"></script>

<script>
// Initialize analytics with data
document.addEventListener('DOMContentLoaded', function() {
    if (typeof AdminAnalytics !== 'undefined') {
        window.analytics = new AdminAnalytics({
            userRegistrationData: @json($userRegistrationData),
            revenueData: @json($revenueData),
            activeSubscriptionData: @json($activeSubscriptionData),
            featureUsageData: @json($featureUsageData),
            currentFilter: '{{ $timeFilter }}',
            startDate: '{{ $startDate }}',
            endDate: '{{ $endDate }}'
        });
    }
});
</script>
@endpush
