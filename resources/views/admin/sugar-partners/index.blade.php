@extends('layouts.admin')

@section('page-title', 'Sugar Partners Management')

@section('page-description', 'Manage Sugar Partners, view profiles, and monitor sugar partner activity across the platform.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Sugar Partners</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.exchanges.index') }}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left-right me-2"></i>View Exchanges
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#initiateExchangeModal">
            <i class="bi bi-plus-circle me-2"></i>Initiate Exchange
        </button>
        <button type="button" class="btn btn-outline-secondary">
            <i class="bi bi-download me-2"></i>Export Sugar Partners
        </button>
    </div>
@endsection

@section('content')
    <!-- Filters and Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.sugar-partners.index') }}" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-3">
                        <label class="form-label fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" name="search" class="form-control border-start-0"
                                   placeholder="Search by name, email, or phone..."
                                   value="{{ request('search') }}" style="box-shadow: none;">
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Gender</label>
                        <select name="gender" class="form-select">
                            <option value="">All Genders</option>
                            <option value="male" {{ request('gender') === 'male' ? 'selected' : '' }}>Male</option>
                            <option value="female" {{ request('gender') === 'female' ? 'selected' : '' }}>Female</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Sugar Partner Type</label>
                        <select name="sugar_partner_type" class="form-select">
                            <option value="">All Types</option>
                            @foreach($sugarPartnerTypes as $value => $label)
                                <option value="{{ $value }}" {{ request('sugar_partner_type') === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label fw-medium">Registration Date Range</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="date" name="date_from" class="form-control form-control-sm"
                                       value="{{ request('date_from') }}" placeholder="From">
                            </div>
                            <div class="col-6">
                                <input type="date" name="date_to" class="form-control form-control-sm"
                                       value="{{ request('date_to') }}" placeholder="To">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3 align-items-end mt-2">
                    <div class="col-lg-12">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.sugar-partners.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Hidden sorting inputs -->
                <input type="hidden" name="sort_by" value="{{ request('sort_by', 'created_at') }}">
                <input type="hidden" name="sort_order" value="{{ request('sort_order', 'desc') }}">
            </form>
        </div>
    </div>

    <!-- Sugar Partners Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partners (<span id="sugar-partners-count">{{ $users->total() ?? $users->count() }}</span>)
                </h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- Bulk Actions -->
                    <div class="d-none" id="bulk-actions">
                        <select class="form-select form-select-sm">
                            <option>Bulk Actions</option>
                            <option>Export Selected</option>
                            <option>Suspend Selected</option>
                            <option>Activate Selected</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <div id="sugar-partners-table">
            @include('admin.sugar-partners.table', ['users' => $users])
        </div>
    </div>
@endsection

<!-- Initiate Exchange Modal -->
<div class="modal fade" id="initiateExchangeModal" tabindex="-1" aria-labelledby="initiateExchangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="initiateExchangeModalLabel">
                    <i class="bi bi-arrow-left-right me-2"></i>Initiate Profile Exchange
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('admin.sugar-partners.initiate-exchange') }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Profile Exchange:</strong> Both selected users will be required to pay the configured exchange fee before they can view each other's profiles and submit responses.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user1_id" class="form-label">First User</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" id="user1Dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span id="user1Selected">Select first user...</span>
                                    </button>
                                    <div class="dropdown-menu w-100" style="max-height: 350px; overflow-y: auto;">
                                        <div class="px-3 py-2 border-bottom">
                                            <input type="text" class="form-control form-control-sm" id="user1Search" placeholder="Search users..." autocomplete="off">
                                        </div>
                                        <ul class="list-unstyled mb-0" id="user1List">
                                            @foreach($usersWithRejectionStatus as $user)
                                                <li data-user-id="{{ $user->id }}" data-rejection-status="{{ json_encode($user->rejection_status) }}">
                                                    <a class="dropdown-item user-option" href="#"
                                                       data-user-id="{{ $user->id }}"
                                                       data-target="user1"
                                                       data-user-name="{{ $user->name }}"
                                                       data-user-role="{{ $user->getWhatIAmForAdmin() }}"
                                                       data-user-wants="{{ implode(', ', $user->getWhatIWantForAdmin()) }}"
                                                       data-user-img="{{ $user->profile_picture_url }}">
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ $user->profile_picture_url }}" alt="{{ $user->name }}"
                                                                 class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                            <div class="flex-grow-1">
                                                                <div class="fw-medium">{{ $user->name }}</div>
                                                                <small class="text-muted">
                                                                    @if($user->getWhatIAmForAdmin() !== 'None')
                                                                        I am {{ $user->getWhatIAmForAdmin() }}
                                                                    @else
                                                                        No Role
                                                                    @endif
                                                                    @if(!empty($user->getWhatIWantForAdmin()) && $user->getWhatIWantForAdmin()[0] !== 'None')
                                                                        | I want {{ implode(', ', $user->getWhatIWantForAdmin()) }}
                                                                    @endif
                                                                </small>
                                                            </div>
                                                            <div class="soft-reject-indicator" style="display: none;">
                                                                <i class="bi bi-dash-circle text-warning" title="Soft Rejected"></i>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <input type="hidden" id="user1_id" name="user1_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user2_id" class="form-label">Second User</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" id="user2Dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span id="user2Selected">Select second user...</span>
                                    </button>
                                    <div class="dropdown-menu w-100" style="max-height: 350px; overflow-y: auto;">
                                        <div class="px-3 py-2 border-bottom">
                                            <input type="text" class="form-control form-control-sm" id="user2Search" placeholder="Search users..." autocomplete="off">
                                        </div>
                                        <ul class="list-unstyled mb-0" id="user2List">
                                            @foreach($usersWithRejectionStatus as $user)
                                                <li data-user-id="{{ $user->id }}" data-rejection-status="{{ json_encode($user->rejection_status) }}">
                                                    <a class="dropdown-item user-option" href="#"
                                                       data-user-id="{{ $user->id }}"
                                                       data-target="user2"
                                                       data-user-name="{{ $user->name }}"
                                                       data-user-role="{{ $user->getWhatIAmForAdmin() }}"
                                                       data-user-wants="{{ implode(', ', $user->getWhatIWantForAdmin()) }}"
                                                       data-user-img="{{ $user->profile_picture_url }}">
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ $user->profile_picture_url }}" alt="{{ $user->name }}"
                                                                 class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                            <div class="flex-grow-1">
                                                                <div class="fw-medium">{{ $user->name }}</div>
                                                                <small class="text-muted">
                                                                    @if($user->getWhatIAmForAdmin() !== 'None')
                                                                        I am {{ $user->getWhatIAmForAdmin() }}
                                                                    @else
                                                                        No Role
                                                                    @endif
                                                                    @if(!empty($user->getWhatIWantForAdmin()) && $user->getWhatIWantForAdmin()[0] !== 'None')
                                                                        | I want {{ implode(', ', $user->getWhatIWantForAdmin()) }}
                                                                    @endif
                                                                </small>
                                                            </div>
                                                            <div class="soft-reject-indicator" style="display: none;">
                                                                <i class="bi bi-dash-circle text-warning" title="Soft Rejected"></i>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <input type="hidden" id="user2_id" name="user2_id" required>
                            </div>
                        </div>
                    </div>

                    <!-- Exchange Amount Section -->
                    <div class="mb-3" id="exchangeAmountSection" style="display: none;">
                        <div class="card border-primary">
                            <div class="card-header bg-primary-subtle">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-currency-rupee me-2"></i>Exchange Amount
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="user1_amount" class="form-label">
                                            <span id="user1AmountLabel">First User Amount</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $currency ?? 'INR' }}</span>
                                            <input type="number" class="form-control" id="user1_amount" name="user1_amount"
                                                   min="0" step="0.01" placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="user2_amount" class="form-label">
                                            <span id="user2AmountLabel">Second User Amount</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $currency ?? 'INR' }}</span>
                                            <input type="number" class="form-control" id="user2_amount" name="user2_amount"
                                                   min="0" step="0.01" placeholder="0.00">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div id="totalAmountDisplay" class="text-end">
                                            <strong>Total: <span id="totalAmount">{{ $currency ?? 'INR' }} 0.00</span></strong>
                                        </div>
                                        <small class="text-muted">You can directly edit the amounts above if needed</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @php
                        $sugarPartnerFeature = \App\Models\Feature::where('name', 'sugar_partner')->first();
                        $pricing = $sugarPartnerFeature?->options['pricing'] ?? [
                            'sugar_daddy' => 200.00,
                            'sugar_mommy' => 200.00,
                            'sugar_companion_female' => 100.00,
                            'sugar_companion_male' => 100.00
                        ];
                        $currency = $sugarPartnerFeature?->options['currency'] ?? 'INR';
                    @endphp

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-currency-rupee me-2"></i>Exchange Pricing Structure
                        </h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <strong>Sugar Daddy:</strong> {{ $currency }} {{ number_format($pricing['sugar_daddy'], 2) }}<br>
                                <strong>Sugar Mommy:</strong> {{ $currency }} {{ number_format($pricing['sugar_mommy'], 2) }}
                            </div>
                            <div class="col-6">
                                <strong>Sugar Babe:</strong> {{ $currency }} {{ number_format($pricing['sugar_companion_female'], 2) }}<br>
                                <strong>Sugar Boy:</strong> {{ $currency }} {{ number_format($pricing['sugar_companion_male'], 2) }}
                            </div>
                        </div>
                        <hr>
                        <p class="mb-0">
                            Each user pays according to their Sugar Partner type. Both must complete payment before accessing profiles.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-arrow-left-right me-1"></i>Initiate Exchange
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('#filterForm select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });

    // Auto-submit form on date change
    const dateInputs = document.querySelectorAll('#filterForm input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });

    // Initialize exchange modal functionality
    initializeExchangeModal();
});

// Exchange modal functionality
function initializeExchangeModal() {
    const pricing = @json($pricing);
    const currency = @json($currency);

    let selectedUsers = {
        user1: null,
        user2: null
    };

    // Initialize search functionality
    initializeUserSearch();

    // Apply initial filtering to show soft reject indicators and hide hard rejected users
    updateDropdownVisibility();

    // Handle user selection from custom dropdowns
    document.querySelectorAll('.user-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();

            const userId = this.getAttribute('data-user-id');
            const target = this.getAttribute('data-target');
            const userName = this.getAttribute('data-user-name');
            const userRole = this.getAttribute('data-user-role');
            const userWants = this.getAttribute('data-user-wants');
            const userImg = this.getAttribute('data-user-img');

            // Check if same user is already selected for the other position
            const otherTarget = target === 'user1' ? 'user2' : 'user1';
            const otherUserId = document.getElementById(otherTarget + '_id').value;

            if (otherUserId === userId) {
                alert('Please select two different users for the exchange.');
                return;
            }

            // Update the dropdown button text and hidden input
            const selectedSpan = document.getElementById(target + 'Selected');
            const hiddenInput = document.getElementById(target + '_id');

            // Create simple display text with just name and role for selected display
            let displayText = `${userName}`;
            if (userRole !== 'None') {
                displayText += ` (${userRole})`;
            }

            selectedSpan.innerHTML = `
                <img src="${userImg}" alt="${userName}" class="rounded-circle me-2" style="width: 20px; height: 20px; object-fit: cover;">
                ${displayText}
            `;
            hiddenInput.value = userId;

            // Store user data for calculation
            selectedUsers[target] = {
                id: userId,
                name: userName,
                role: userRole,
                wants: userWants,
                img: userImg
            };

            // Hide selected user from other dropdown and apply compatibility filtering
            updateDropdownVisibility();

            // Calculate exchange amount
            calculateExchangeAmount();
        });
    });

    // Add event listeners for amount changes to update total
    document.getElementById('user1_amount').addEventListener('input', updateTotal);
    document.getElementById('user2_amount').addEventListener('input', updateTotal);

    function calculateExchangeAmount() {
        if (!selectedUsers.user1 || !selectedUsers.user2) {
            document.getElementById('exchangeAmountSection').style.display = 'none';
            return;
        }

        // Show the exchange amount section
        document.getElementById('exchangeAmountSection').style.display = 'block';

        // Get user roles and calculate amounts
        const user1Role = getUserRoleKey(selectedUsers.user1.role);
        const user2Role = getUserRoleKey(selectedUsers.user2.role);

        const user1Amount = pricing[user1Role] || 100.00;
        const user2Amount = pricing[user2Role] || 100.00;
        const totalAmount = user1Amount + user2Amount;

        // Update individual amount inputs
        document.getElementById('user1_amount').value = user1Amount.toFixed(2);
        document.getElementById('user2_amount').value = user2Amount.toFixed(2);

        // Update labels with user names and roles
        document.getElementById('user1AmountLabel').textContent =
            `${selectedUsers.user1.name} (${selectedUsers.user1.role})`;
        document.getElementById('user2AmountLabel').textContent =
            `${selectedUsers.user2.name} (${selectedUsers.user2.role})`;

        // Update total display
        document.getElementById('totalAmount').textContent = `${currency} ${totalAmount.toFixed(2)}`;
    }

    function updateDropdownVisibility() {
        const user1List = document.getElementById('user1List');
        const user2List = document.getElementById('user2List');

        // Reset all items to visible first and hide soft reject indicators
        user1List.querySelectorAll('li').forEach(item => {
            item.style.display = 'block';
            const softRejectIndicator = item.querySelector('.soft-reject-indicator');
            if (softRejectIndicator) {
                softRejectIndicator.style.display = 'none';
            }
        });
        user2List.querySelectorAll('li').forEach(item => {
            item.style.display = 'block';
            const softRejectIndicator = item.querySelector('.soft-reject-indicator');
            if (softRejectIndicator) {
                softRejectIndicator.style.display = 'none';
            }
        });

        // Apply filtering for both dropdowns (this handles search, selection, rejection, and compatibility filtering)
        const user1Search = document.getElementById('user1Search');
        const user2Search = document.getElementById('user2Search');

        // Apply filtering with current search terms (or empty string if no search)
        filterUsers(user1Search.value ? user1Search.value.toLowerCase() : '', user1List);
        filterUsers(user2Search.value ? user2Search.value.toLowerCase() : '', user2List);
    }

    function updateTotal() {
        const user1Amount = parseFloat(document.getElementById('user1_amount').value) || 0;
        const user2Amount = parseFloat(document.getElementById('user2_amount').value) || 0;
        const totalAmount = user1Amount + user2Amount;

        document.getElementById('totalAmount').textContent = `${currency} ${totalAmount.toFixed(2)}`;
    }

    function initializeUserSearch() {
        // Initialize search for user1 dropdown
        const user1Search = document.getElementById('user1Search');
        const user1List = document.getElementById('user1List');

        if (user1Search && user1List) {
            user1Search.addEventListener('input', function() {
                filterUsers(this.value.toLowerCase(), user1List);
            });
        }

        // Initialize search for user2 dropdown
        const user2Search = document.getElementById('user2Search');
        const user2List = document.getElementById('user2List');

        if (user2Search && user2List) {
            user2Search.addEventListener('input', function() {
                filterUsers(this.value.toLowerCase(), user2List);
            });
        }
    }

    function filterUsers(searchTerm, userList) {
        const userItems = userList.querySelectorAll('li');

        userItems.forEach(item => {
            const userOption = item.querySelector('.user-option');
            const userId = userOption.getAttribute('data-user-id');
            const userName = userOption.getAttribute('data-user-name').toLowerCase();
            const userRole = userOption.getAttribute('data-user-role').toLowerCase();
            const userWants = userOption.getAttribute('data-user-wants').toLowerCase();

            // Check if user matches search
            const matchesSearch = userName.includes(searchTerm) ||
                                userRole.includes(searchTerm) ||
                                userWants.includes(searchTerm);

            // Check if user is selected in opposite dropdown
            const isSelectedInOther = (userList.id === 'user1List' && selectedUsers.user2 && selectedUsers.user2.id === userId) ||
                                    (userList.id === 'user2List' && selectedUsers.user1 && selectedUsers.user1.id === userId);

            // Get rejection status for this user
            const rejectionStatus = JSON.parse(item.getAttribute('data-rejection-status') || '{}');
            let shouldHideForHardReject = false;
            let showSoftRejectIndicator = false;

            // Get the other selected user to check specific rejection status
            const otherSelectedUser = userList.id === 'user1List' ? selectedUsers.user2 : selectedUsers.user1;

            if (otherSelectedUser) {
                const otherUserId = otherSelectedUser.id;

                // Hide ONLY if this user has hard rejected the other user
                // Soft rejects should NOT prevent new exchanges
                if (rejectionStatus[otherUserId] && rejectionStatus[otherUserId].hard_reject) {
                    shouldHideForHardReject = true;
                }

                // Show soft reject indicator if this user has soft rejected the other user
                // This is just for information, doesn't prevent exchange
                if (rejectionStatus[otherUserId] && rejectionStatus[otherUserId].soft_reject) {
                    showSoftRejectIndicator = true;
                }
            } else {
                // When no other user is selected, don't show any warning symbols
                // Warning symbols should only appear in the second dropdown based on first user selection
                showSoftRejectIndicator = false;
            }

            // Apply filtering logic based on dropdown type
            let shouldHideForCompatibility = false;

            if (userList.id === 'user1List') {
                // User First dropdown: Only show users who want something specific (not None)
                const currentUserWants = userOption.getAttribute('data-user-wants').toLowerCase();
                if (currentUserWants === 'none' || currentUserWants.trim() === '') {
                    shouldHideForCompatibility = true;
                }
            } else if (userList.id === 'user2List') {
                // User Second dropdown: Show users based on what User First wants
                if (selectedUsers.user1) {
                    const user1Wants = selectedUsers.user1.wants;

                    // Only apply compatibility filtering if user1 wants something specific (not None)
                    if (user1Wants && user1Wants.toLowerCase() !== 'none' && user1Wants.trim() !== '') {
                        const user1WantsArray = user1Wants.split(',').map(want => want.trim().toLowerCase());
                        const currentUserRole = userOption.getAttribute('data-user-role').toLowerCase();

                        // Check if this user's role matches what user1 wants
                        const isCompatible = user1WantsArray.includes(currentUserRole);

                        // Hide users who are not compatible
                        if (!isCompatible) {
                            shouldHideForCompatibility = true;
                        }
                    } else {
                        // If user1 wants 'None', hide all users in user2 dropdown
                        shouldHideForCompatibility = true;
                    }
                }
            }

            // Update soft reject indicator visibility
            const softRejectIndicator = item.querySelector('.soft-reject-indicator');
            if (softRejectIndicator) {
                softRejectIndicator.style.display = showSoftRejectIndicator ? 'block' : 'none';
            }

            // Show only if matches all criteria
            const shouldShow = matchesSearch &&
                              !isSelectedInOther &&
                              !shouldHideForHardReject &&
                              !shouldHideForCompatibility;

            item.style.display = shouldShow ? 'block' : 'none';
        });
    }

    function getUserRoleKey(roleText) {
        const roleMap = {
            'Sugar Daddy': 'sugar_daddy',
            'Sugar Mommy': 'sugar_mommy',
            'Sugar Babe': 'sugar_companion_female',
            'Sugar Boy': 'sugar_companion_male',
            'No Role': 'sugar_companion_male' // Default fallback
        };
        return roleMap[roleText] || 'sugar_companion_male';
    }

    // Handle form submission
    const exchangeForm = document.querySelector('#initiateExchangeModal form');
    if (exchangeForm) {
        exchangeForm.addEventListener('submit', function(e) {
            // Validate that both users are selected
            const user1Id = document.getElementById('user1_id').value;
            const user2Id = document.getElementById('user2_id').value;

            if (!user1Id || !user2Id) {
                e.preventDefault();
                alert('Please select both users before initiating the exchange.');
                return false;
            }

            if (user1Id === user2Id) {
                e.preventDefault();
                alert('Please select two different users for the exchange.');
                return false;
            }

            // Validate that amounts are provided and valid
            const user1Amount = parseFloat(document.getElementById('user1_amount').value);
            const user2Amount = parseFloat(document.getElementById('user2_amount').value);

            if (!user1Amount || user1Amount <= 0) {
                e.preventDefault();
                alert('Please enter a valid amount for the first user.');
                return false;
            }

            if (!user2Amount || user2Amount <= 0) {
                e.preventDefault();
                alert('Please enter a valid amount for the second user.');
                return false;
            }

            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Initiating...';
            submitButton.disabled = true;

            // Re-enable button after a delay in case of errors
            setTimeout(() => {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 10000);
        });
    }

    // Reset modal when it's closed
    const modal = document.getElementById('initiateExchangeModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            // Reset form
            const form = this.querySelector('form');
            if (form) {
                form.reset();
            }

            // Reset custom dropdowns
            document.getElementById('user1Selected').textContent = 'Select first user...';
            document.getElementById('user2Selected').textContent = 'Select second user...';
            document.getElementById('user1_id').value = '';
            document.getElementById('user2_id').value = '';

            // Reset search inputs
            document.getElementById('user1Search').value = '';
            document.getElementById('user2Search').value = '';

            // Reset exchange amount section
            document.getElementById('exchangeAmountSection').style.display = 'none';

            // Reset amount inputs
            document.getElementById('user1_amount').value = '';
            document.getElementById('user2_amount').value = '';
            document.getElementById('user1AmountLabel').textContent = 'First User Amount';
            document.getElementById('user2AmountLabel').textContent = 'Second User Amount';
            document.getElementById('totalAmount').textContent = 'INR 0.00';

            // Show all users in dropdowns
            const user1List = document.getElementById('user1List');
            const user2List = document.getElementById('user2List');
            if (user1List) {
                user1List.querySelectorAll('li').forEach(item => {
                    item.style.display = 'block';
                });
            }
            if (user2List) {
                user2List.querySelectorAll('li').forEach(item => {
                    item.style.display = 'block';
                });
            }

            // Reset selected users
            selectedUsers = { user1: null, user2: null };
        });
    }
}

function loadPage(page) {
    const currentFilters = {
        search: document.querySelector('input[name="search"]')?.value || '',
        gender: document.querySelector('select[name="gender"]')?.value || '',
        sugar_partner_type: document.querySelector('select[name="sugar_partner_type"]')?.value || '',
        status: document.querySelector('select[name="status"]')?.value || '',
        date_from: document.querySelector('input[name="date_from"]')?.value || '',
        date_to: document.querySelector('input[name="date_to"]')?.value || '',
        sort_by: document.querySelector('input[name="sort_by"]')?.value || 'created_at',
        sort_order: document.querySelector('input[name="sort_order"]')?.value || 'desc',
        page: page
    };

    // Build query string
    const queryString = Object.keys(currentFilters)
        .filter(key => currentFilters[key])
        .map(key => `${key}=${encodeURIComponent(currentFilters[key])}`)
        .join('&');

    // Fetch new data
    fetch(`{{ route('admin.sugar-partners.data') }}?${queryString}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('sugar-partners-table').innerHTML = html;
            
            // Update count
            const countMatch = html.match(/Showing.*?of.*?(\d+).*?results/);
            if (countMatch) {
                document.getElementById('sugar-partners-count').textContent = countMatch[1];
            }
        })
        .catch(error => {
            console.error('Error loading page:', error);
        });
}

function sortTable(column) {
    const sortByInput = document.querySelector('input[name="sort_by"]');
    const sortOrderInput = document.querySelector('input[name="sort_order"]');

    // If clicking the same column, toggle order
    if (sortByInput.value === column) {
        sortOrderInput.value = sortOrderInput.value === 'asc' ? 'desc' : 'asc';
    } else {
        // New column, default to ascending
        sortByInput.value = column;
        sortOrderInput.value = 'asc';
    }

    document.getElementById('filterForm').submit();
}
</script>
@endpush
