@extends('layouts.admin')

@section('title', 'Razorpay Debug & Configuration')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Razorpay Debug & Configuration</h3>
                </div>
                <div class="card-body">
                    <!-- Configuration Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Configuration Status</h5>
                                </div>
                                <div class="card-body">
                                    <div id="config-status">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                            <p class="mt-2">Checking configuration...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-primary btn-sm mb-2" onclick="testConfiguration()">
                                        <i class="fas fa-check-circle"></i> Test Configuration
                                    </button>
                                    <button class="btn btn-info btn-sm mb-2" onclick="testOrderCreation()">
                                        <i class="fas fa-plus-circle"></i> Test Order Creation
                                    </button>
                                    <button class="btn btn-secondary btn-sm mb-2" onclick="clearLogs()">
                                        <i class="fas fa-trash"></i> Clear Logs
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Order Creation -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Test Order Creation</h5>
                                </div>
                                <div class="card-body">
                                    <form id="test-order-form">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="test-amount">Amount (₹)</label>
                                                    <input type="number" class="form-control" id="test-amount" value="1" min="1" step="0.01">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="test-currency">Currency</label>
                                                    <select class="form-control" id="test-currency">
                                                        <option value="INR">INR</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="test-receipt">Receipt ID</label>
                                                    <input type="text" class="form-control" id="test-receipt" placeholder="Auto-generated">
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-play"></i> Create Test Order
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Logs -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-dark">
                                <div class="card-header bg-dark text-white">
                                    <h5 class="mb-0">Debug Logs</h5>
                                </div>
                                <div class="card-body">
                                    <div id="debug-logs" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">
                                        <div class="text-muted">Debug logs will appear here...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let debugLogs = [];

function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
        timestamp,
        message,
        type
    };
    
    debugLogs.push(logEntry);
    updateLogsDisplay();
}

function updateLogsDisplay() {
    const logsContainer = document.getElementById('debug-logs');
    const logsHtml = debugLogs.map(log => {
        const colorClass = {
            'info': 'text-info',
            'success': 'text-success',
            'error': 'text-danger',
            'warning': 'text-warning'
        }[log.type] || 'text-dark';
        
        return `<div class="${colorClass}">[${log.timestamp}] ${log.message}</div>`;
    }).join('');
    
    logsContainer.innerHTML = logsHtml || '<div class="text-muted">No logs yet...</div>';
    logsContainer.scrollTop = logsContainer.scrollHeight;
}

function clearLogs() {
    debugLogs = [];
    updateLogsDisplay();
    addLog('Logs cleared', 'info');
}

async function testConfiguration() {
    addLog('Testing Razorpay configuration...', 'info');
    
    try {
        const response = await fetch('/admin/razorpay-debug/test', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('✅ Configuration test successful!', 'success');
            addLog(`Test order created: ${result.test_order_id}`, 'success');
            updateConfigStatus(result.debug_info, true);
        } else {
            addLog(`❌ Configuration test failed: ${result.message}`, 'error');
            if (result.error_details) {
                addLog(`Error details: ${JSON.stringify(result.error_details)}`, 'error');
            }
            updateConfigStatus(result.debug_info, false);
        }
    } catch (error) {
        addLog(`❌ Network error: ${error.message}`, 'error');
        updateConfigStatus({}, false);
    }
}

async function testOrderCreation() {
    const amount = document.getElementById('test-amount').value;
    const currency = document.getElementById('test-currency').value;
    const receipt = document.getElementById('test-receipt').value;
    
    addLog(`Creating test order: ₹${amount} ${currency}`, 'info');
    
    try {
        const response = await fetch('/admin/razorpay-debug/test-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                amount: parseFloat(amount),
                currency: currency,
                receipt: receipt || undefined
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('✅ Test order created successfully!', 'success');
            addLog(`Order ID: ${result.order.id}`, 'success');
            addLog(`Order Status: ${result.order.status}`, 'success');
            addLog(`Amount: ₹${result.order.amount / 100}`, 'success');
        } else {
            addLog(`❌ Order creation failed: ${result.message}`, 'error');
            if (result.error_details) {
                addLog(`Error details: ${JSON.stringify(result.error_details)}`, 'error');
            }
        }
    } catch (error) {
        addLog(`❌ Network error: ${error.message}`, 'error');
    }
}

function updateConfigStatus(debugInfo, isSuccess) {
    const statusContainer = document.getElementById('config-status');
    
    const statusHtml = `
        <div class="alert ${isSuccess ? 'alert-success' : 'alert-danger'}">
            <h6>${isSuccess ? '✅ Configuration Valid' : '❌ Configuration Issues'}</h6>
            <ul class="mb-0">
                <li>Environment: ${debugInfo.environment || 'Unknown'}</li>
                <li>Has Key ID: ${debugInfo.has_key_id ? '✅' : '❌'}</li>
                <li>Has Key Secret: ${debugInfo.has_key_secret ? '✅' : '❌'}</li>
                <li>Key Format: ${debugInfo.key_id_format || 'Unknown'}</li>
                <li>API Test: ${debugInfo.api_test === 'success' ? '✅' : '❌'}</li>
            </ul>
        </div>
    `;
    
    statusContainer.innerHTML = statusHtml;
}

// Test order form submission
document.getElementById('test-order-form').addEventListener('submit', function(e) {
    e.preventDefault();
    testOrderCreation();
});

// Auto-test configuration on page load
document.addEventListener('DOMContentLoaded', function() {
    addLog('Page loaded, starting configuration test...', 'info');
    setTimeout(testConfiguration, 1000);
});
</script>
@endsection
