<div class="row">
    <!-- Report Information -->
    <div class="col-md-6">
        <h6 class="fw-bold mb-3">Report Information</h6>
        <table class="table table-sm">
            <tr>
                <td class="fw-medium">Report ID:</td>
                <td>#{{ $report->id }}</td>
            </tr>
            <tr>
                <td class="fw-medium">Category:</td>
                <td>{{ $report->getCategoryDisplayName() }}</td>
            </tr>
            <tr>
                <td class="fw-medium">Status:</td>
                <td><span class="badge {{ $report->getStatusBadgeClass() }}">{{ $report->getStatusDisplayName() }}</span></td>
            </tr>
            <tr>
                <td class="fw-medium">Submitted:</td>
                <td>{{ $report->created_at->format('M d, Y H:i') }}</td>
            </tr>
            @if($report->reviewed_at)
            <tr>
                <td class="fw-medium">Reviewed:</td>
                <td>{{ $report->reviewed_at->format('M d, Y H:i') }}</td>
            </tr>
            @endif
        </table>
    </div>

    <!-- User Information -->
    <div class="col-md-6">
        <h6 class="fw-bold mb-3">User Information</h6>
        <table class="table table-sm">
            <tr>
                <td class="fw-medium">Reporter:</td>
                <td>
                    @if($report->reporter)
                        {{ $report->reporter->name }}
                        <br><small class="text-muted">{{ $report->reporter->email }}</small>
                    @else
                        <span class="text-muted">Unknown</span>
                    @endif
                </td>
            </tr>
            <tr>
                <td class="fw-medium">Reported User:</td>
                <td>
                    @if($report->reportedUser)
                        {{ $report->reportedUser->name }}
                        <br><small class="text-muted">{{ $report->reportedUser->email }}</small>
                        @if($report->reportedUser->is_suspended)
                            <br><span class="badge bg-danger">Suspended</span>
                        @endif
                    @else
                        <span class="text-muted">Unknown</span>
                    @endif
                </td>
            </tr>
            @if($report->reviewer)
            <tr>
                <td class="fw-medium">Reviewed By:</td>
                <td>
                    {{ $report->reviewer->name }}
                    <br><small class="text-muted">{{ $report->reviewer->email }}</small>
                </td>
            </tr>
            @endif
        </table>
    </div>
</div>

<!-- Description -->
@if($report->description)
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Description</h6>
        <div class="bg-light p-3 rounded">
            {{ $report->description }}
        </div>
    </div>
</div>
@endif

<!-- Evidence File -->
@if($report->evidence_file)
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Evidence</h6>
        <div class="bg-light p-3 rounded">
            @php
                $fileExtension = pathinfo($report->evidence_file, PATHINFO_EXTENSION);
                $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif']);
            @endphp
            
            @if($isImage)
                <img src="{{ asset('storage/' . $report->evidence_file) }}" 
                     alt="Evidence" 
                     class="img-fluid rounded" 
                     style="max-height: 300px;">
            @else
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf fs-2 text-danger me-3"></i>
                    <div>
                        <div class="fw-medium">{{ basename($report->evidence_file) }}</div>
                        <a href="{{ asset('storage/' . $report->evidence_file) }}" 
                           target="_blank" 
                           class="btn btn-sm btn-outline-primary mt-2">
                            <i class="bi bi-download"></i> Download
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endif

<!-- Admin Notes -->
@if($report->admin_notes)
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Admin Notes</h6>
        <div class="bg-warning bg-opacity-10 p-3 rounded border border-warning">
            {{ $report->admin_notes }}
        </div>
    </div>
</div>
@endif

<!-- Action Buttons -->
@if($report->isPending() || $report->isUnderReview())
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Actions</h6>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-danger" onclick="suspendUser({{ $report->id }})">
                <i class="bi bi-person-x"></i> Suspend User
            </button>
            <button type="button" class="btn btn-warning" onclick="sendWarning({{ $report->id }})">
                <i class="bi bi-exclamation-triangle"></i> Send Warning
            </button>
            <button type="button" class="btn btn-secondary" onclick="dismissReport({{ $report->id }})">
                <i class="bi bi-x-circle"></i> Dismiss Report
            </button>
        </div>
    </div>
</div>
@endif

<!-- Decision Modification Section -->
@if($report->isResolved() || $report->isDismissed())
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">
            <i class="bi bi-arrow-clockwise me-2 text-warning"></i>Modify Decision
        </h6>
        <div class="bg-light p-3 rounded">
            <p class="text-muted mb-3">
                <small>This report has been {{ $report->getStatusDisplayName() }}. You can modify the decision if new information becomes available.</small>
            </p>
            <div class="d-flex gap-2 flex-wrap">
                @if($report->isResolved())
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeReportStatus({{ $report->id }}, 'dismissed')">
                        <i class="bi bi-x-circle"></i> Change to Dismissed
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="changeReportStatus({{ $report->id }}, 'under_review')">
                        <i class="bi bi-eye"></i> Reopen for Review
                    </button>
                @endif

                @if($report->isDismissed())
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="changeReportStatus({{ $report->id }}, 'resolved')">
                        <i class="bi bi-check-circle"></i> Change to Resolved
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="changeReportStatus({{ $report->id }}, 'under_review')">
                        <i class="bi bi-eye"></i> Reopen for Review
                    </button>
                @endif

                @if($report->reportedUser && $report->reportedUser->is_suspended)
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="liftSuspension({{ $report->id }})">
                        <i class="bi bi-person-check"></i> Lift User Suspension
                    </button>
                @endif

                <button type="button" class="btn btn-outline-warning btn-sm" onclick="reverseDecision({{ $report->id }})">
                    <i class="bi bi-arrow-counterclockwise"></i> Reverse Decision
                </button>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Decision History Section -->
@if($report->decisionHistory && $report->decisionHistory->count() > 0)
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">
            <i class="bi bi-clock-history me-2 text-info"></i>Decision History
        </h6>
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead class="table-light">
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th style="width: 140px;">Date & Time</th>
                        <th style="width: 120px;">Action</th>
                        <th style="width: 100px;">Admin</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($report->decisionHistory as $index => $history)
                    <tr>
                        <td>
                            <span class="badge bg-light text-dark fw-bold">{{ $index + 1 }}</span>
                        </td>
                        <td>
                            <div class="fw-medium" style="font-size: 0.8rem;">{{ $history->created_at->format('M d, Y') }}</div>
                            <small class="text-muted">{{ $history->created_at->format('H:i A') }}</small>
                        </td>
                        <td>
                            <span class="badge
                                @if($history->action_type === 'suspension_applied') bg-danger
                                @elseif($history->action_type === 'suspension_lifted') bg-success
                                @elseif($history->action_type === 'warning_sent') bg-warning
                                @elseif($history->action_type === 'decision_reversed') bg-info
                                @else bg-secondary
                                @endif">
                                {{ $history->getActionTypeDisplayName() }}
                            </span>
                        </td>
                        <td>
                            @if($history->admin)
                                <small>{{ $history->admin->name }}</small>
                            @else
                                <small class="text-muted">Unknown</small>
                            @endif
                        </td>
                        <td>
                            @if($history->previous_status && $history->new_status)
                                <small>Status: {{ ucfirst($history->previous_status) }} → {{ ucfirst($history->new_status) }}</small>
                            @endif

                            @if($history->suspension_duration)
                                <small class="d-block">Duration: {{ $history->suspension_duration }}</small>
                            @endif

                            @if($history->reason)
                                <small class="d-block text-muted">{{ $history->reason }}</small>
                            @endif

                            @if($history->warning_message)
                                <small class="d-block text-warning">Warning: {{ Str::limit($history->warning_message, 50) }}</small>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endif
