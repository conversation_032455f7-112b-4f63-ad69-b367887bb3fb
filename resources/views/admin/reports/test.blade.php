<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Reports - SettingWala Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-flag me-2 text-danger"></i>User Reports Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Reports Summary -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6>Total Reports</h6>
                                        <h3>{{ $reports->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h6>Pending</h6>
                                        <h3>{{ $reports->where('status', 'pending')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6>Resolved</h6>
                                        <h3>{{ $reports->where('status', 'resolved')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body">
                                        <h6>Dismissed</h6>
                                        <h3>{{ $reports->where('status', 'dismissed')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reports Table -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="reportsTable" width="100%">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Reporter</th>
                                        <th>Reported User</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Manual Data Display for Debugging -->
                        <div class="mt-4">
                            <h6>Raw Data (for debugging):</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Reporter</th>
                                            <th>Reported User</th>
                                            <th>Category</th>
                                            <th>Status</th>
                                            <th>Description</th>
                                            <th>Created At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($reports as $report)
                                        <tr>
                                            <td>{{ $report->id }}</td>
                                            <td>{{ $report->reporter ? $report->reporter->name : 'Unknown' }}</td>
                                            <td>{{ $report->reportedUser ? $report->reportedUser->name : 'Unknown' }}</td>
                                            <td>{{ $report->getCategoryDisplayName() }}</td>
                                            <td>
                                                <span class="badge bg-warning">{{ ucfirst(str_replace('_', ' ', $report->status)) }}</span>
                                            </td>
                                            <td>{{ Str::limit($report->description, 50) }}</td>
                                            <td>{{ $report->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">No reports found</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <script>
    $(document).ready(function() {
        console.log('Initializing DataTable...');
        
        $('#reportsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '/test-reports-data',
                type: 'GET',
                error: function(xhr, error, code) {
                    console.error('DataTables AJAX Error:', error, code);
                    console.error('Response:', xhr.responseText);
                }
            },
            columns: [
                { data: 'id', name: 'id' },
                { data: 'reporter', name: 'reporter' },
                { data: 'reported_user', name: 'reported_user' },
                { data: 'category', name: 'category' },
                { data: 'status', name: 'status', orderable: false },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            order: [[0, 'desc']],
            pageLength: 25,
            language: {
                processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
                emptyTable: 'No reports found',
                zeroRecords: 'No matching reports found'
            }
        });
    });
    </script>
</body>
</html>
