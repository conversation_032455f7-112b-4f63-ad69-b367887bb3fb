@extends('layouts.admin')

@section('title', 'User Reports')

@section('page-title', 'User Reports')
@section('page-description', 'Manage user reports and take appropriate actions')

@section('breadcrumbs')
    <li class="breadcrumb-item active">User Reports</li>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                                <i class="bi bi-flag text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Total Reports</p>
                                    <h3 class="mb-0 fw-bold">{{ number_format($stats['total_reports']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                                <i class="bi bi-clock text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Pending</p>
                                    <h3 class="mb-0 fw-bold">{{ number_format($stats['pending_reports']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="bi bi-check-circle text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Resolved</p>
                                    <h3 class="mb-0 fw-bold">{{ number_format($stats['resolved_reports']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #17a2b8 0%, #6610f2 100%);">
                                <i class="bi bi-calendar-day text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Today</p>
                                    <h3 class="mb-0 fw-bold">{{ number_format($stats['reports_today']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">Status</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">All Statuses</option>
                                <option value="pending">Pending</option>
                                <option value="under_review">Under Review</option>
                                <option value="resolved">Resolved</option>
                                <option value="dismissed">Dismissed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="categoryFilter" class="form-label">Category</label>
                            <select class="form-select" id="categoryFilter">
                                <option value="">All Categories</option>
                                <option value="inappropriate_behavior">Inappropriate Behavior</option>
                                <option value="fake_profile">Fake Profile</option>
                                <option value="harassment">Harassment</option>
                                <option value="spam">Spam</option>
                                <option value="inappropriate_content">Inappropriate Content</option>
                                <option value="scam_fraud">Scam/Fraud</option>
                                <option value="underage_user">Underage User</option>
                                <option value="violence_threats">Violence/Threats</option>
                                <option value="hate_speech">Hate Speech</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchFilter" class="form-label">Search</label>
                            <input type="text" class="form-control" id="searchFilter" placeholder="Search by reporter, reported user, or description...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="refreshTable()">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0 fw-bold">
                        <i class="bi bi-flag me-2 text-danger"></i>Reports List
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="reportsTable" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Reporter</th>
                                    <th>Reported User</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Details Modal -->
<div class="modal fade" id="reportDetailsModal" tabindex="-1" aria-labelledby="reportDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportDetailsModalLabel">Report Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="reportDetailsContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<!-- Suspend User Modal -->
<div class="modal fade" id="suspendUserModal" tabindex="-1" aria-labelledby="suspendUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendUserModalLabel">Suspend User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="suspendUserForm">
                    <div class="mb-3">
                        <label for="suspensionDuration" class="form-label">Suspension Duration</label>
                        <select class="form-select" id="suspensionDuration" name="duration" required>
                            <option value="">Select duration</option>
                            <option value="24h">24 Hours</option>
                            <option value="7d">7 Days</option>
                            <option value="30d">30 Days</option>
                            <option value="permanent">Permanent</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="suspensionReason" class="form-label">Reason</label>
                        <textarea class="form-control" id="suspensionReason" name="reason" rows="3" required 
                                  placeholder="Enter the reason for suspension..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmSuspension()">Suspend User</button>
            </div>
        </div>
    </div>
</div>

<!-- Send Warning Modal -->
<div class="modal fade" id="sendWarningModal" tabindex="-1" aria-labelledby="sendWarningModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendWarningModalLabel">Send Warning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sendWarningForm">
                    <div class="mb-3">
                        <label for="warningMessage" class="form-label">Warning Message</label>
                        <textarea class="form-control" id="warningMessage" name="warning_message" rows="4" required 
                                  placeholder="Enter the warning message to send to the user..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmWarning()">Send Warning</button>
            </div>
        </div>
    </div>
</div>

<!-- Dismiss Report Modal -->
<div class="modal fade" id="dismissReportModal" tabindex="-1" aria-labelledby="dismissReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dismissReportModalLabel">Dismiss Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="dismissReportForm">
                    <div class="mb-3">
                        <label for="adminNotes" class="form-label">Admin Notes (Optional)</label>
                        <textarea class="form-control" id="adminNotes" name="admin_notes" rows="3" 
                                  placeholder="Enter any notes about why this report is being dismissed..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-outline-secondary" onclick="confirmDismissal()">Dismiss Report</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
@endpush

@push('scripts')
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
let reportsTable;
let currentReportId = null;

$(document).ready(function() {
    initializeDataTable();
    
    // Filter change handlers
    $('#statusFilter, #categoryFilter').change(function() {
        reportsTable.ajax.reload();
    });
    
    $('#searchFilter').on('keyup', function() {
        reportsTable.ajax.reload();
    });
});

function initializeDataTable() {
    reportsTable = $('#reportsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("admin.reports.data") }}',
            type: 'GET',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.category = $('#categoryFilter').val();
                d.search = $('#searchFilter').val();
            },
            error: function(xhr, error, code) {
                console.error('DataTables AJAX Error:', error, code);
                console.error('Response:', xhr.responseText);
                showAlert('Error loading reports data: ' + error, 'danger');
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'reporter', name: 'reporter.name' },
            { data: 'reported_user', name: 'reportedUser.name' },
            { data: 'category', name: 'category' },
            { data: 'status', name: 'status', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
            emptyTable: 'No reports found',
            zeroRecords: 'No matching reports found'
        }
    });
}

function refreshTable() {
    reportsTable.ajax.reload();
}

function viewReport(reportId) {
    $.get(`{{ url('admin/reports') }}/${reportId}`)
        .done(function(data) {
            $('#reportDetailsContent').html(data);
            $('#reportDetailsModal').modal('show');
        })
        .fail(function() {
            showAlert('Error loading report details', 'danger');
        });
}

function suspendUser(reportId) {
    currentReportId = reportId;
    $('#suspendUserForm')[0].reset();
    $('#suspendUserModal').modal('show');
}

function sendWarning(reportId) {
    currentReportId = reportId;
    $('#sendWarningForm')[0].reset();
    $('#sendWarningModal').modal('show');
}

function dismissReport(reportId) {
    currentReportId = reportId;
    $('#dismissReportForm')[0].reset();
    $('#dismissReportModal').modal('show');
}

function confirmSuspension() {
    const formData = new FormData($('#suspendUserForm')[0]);
    const duration = formData.get('duration');
    const reason = formData.get('reason');

    // Validate form data before sending
    if (!duration) {
        showAlert('Please select a suspension duration', 'danger');
        return;
    }

    if (!reason || reason.trim() === '') {
        showAlert('Please provide a reason for suspension', 'danger');
        return;
    }

    console.log('Sending suspension request:', {
        currentReportId: currentReportId,
        duration: duration,
        reason: reason
    });

    $.post(`{{ url('admin/reports') }}/${currentReportId}/suspend-user`, {
        _token: '{{ csrf_token() }}',
        duration: duration,
        reason: reason
    })
    .done(function(response) {
        console.log('Suspension response:', response);
        if (response.success) {
            $('#suspendUserModal').modal('hide');
            showAlert(response.message, 'success');
            reportsTable.ajax.reload();
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr, status, error) {
        console.error('Suspension error:', xhr.responseText);
        let errorMessage = 'Error suspending user';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = Object.values(xhr.responseJSON.errors).flat();
            errorMessage = errors.join(', ');
        }

        showAlert(errorMessage, 'danger');
    });
}

function confirmWarning() {
    const formData = new FormData($('#sendWarningForm')[0]);
    const warningMessage = formData.get('warning_message');

    // Validate form data
    if (!warningMessage || warningMessage.trim() === '') {
        showAlert('Please provide a warning message', 'danger');
        return;
    }

    $.post(`{{ url('admin/reports') }}/${currentReportId}/send-warning`, {
        _token: '{{ csrf_token() }}',
        warning_message: warningMessage
    })
    .done(function(response) {
        if (response.success) {
            $('#sendWarningModal').modal('hide');
            showAlert(response.message, 'success');
            reportsTable.ajax.reload();
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr, status, error) {
        console.error('Warning error:', xhr.responseText);
        let errorMessage = 'Error sending warning';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = Object.values(xhr.responseJSON.errors).flat();
            errorMessage = errors.join(', ');
        }

        showAlert(errorMessage, 'danger');
    });
}

function confirmDismissal() {
    const formData = new FormData($('#dismissReportForm')[0]);

    $.post(`{{ url('admin/reports') }}/${currentReportId}/dismiss`, {
        _token: '{{ csrf_token() }}',
        admin_notes: formData.get('admin_notes') || ''
    })
    .done(function(response) {
        if (response.success) {
            $('#dismissReportModal').modal('hide');
            showAlert(response.message, 'success');
            reportsTable.ajax.reload();
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr, status, error) {
        console.error('Dismiss error:', xhr.responseText);
        let errorMessage = 'Error dismissing report';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = Object.values(xhr.responseJSON.errors).flat();
            errorMessage = errors.join(', ');
        }

        showAlert(errorMessage, 'danger');
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Insert at the top of the content
    $('.container-fluid').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

// Decision modification functions
function changeReportStatus(reportId, newStatus) {
    const statusDisplayNames = {
        'pending': 'Pending',
        'under_review': 'Under Review',
        'resolved': 'Resolved',
        'dismissed': 'Dismissed'
    };

    const reason = prompt(`Please provide a reason for changing the status to ${statusDisplayNames[newStatus]}:`);
    if (reason === null) return; // User cancelled

    if (!reason.trim()) {
        showAlert('Please provide a reason for the status change', 'danger');
        return;
    }

    $.post(`{{ url('admin/reports') }}/${reportId}/change-status`, {
        _token: '{{ csrf_token() }}',
        new_status: newStatus,
        reason: reason
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            // Refresh the modal content
            viewReport(reportId);
            // Refresh the table
            if (typeof reportsTable !== 'undefined') {
                reportsTable.ajax.reload();
            }
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr) {
        console.error('Status change error:', xhr.responseText);
        let errorMessage = 'Error changing report status';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        }

        showAlert(errorMessage, 'danger');
    });
}

function liftSuspension(reportId) {
    const reason = prompt('Please provide a reason for lifting the suspension:');
    if (reason === null) return; // User cancelled

    if (!reason.trim()) {
        showAlert('Please provide a reason for lifting the suspension', 'danger');
        return;
    }

    $.post(`{{ url('admin/reports') }}/${reportId}/lift-suspension`, {
        _token: '{{ csrf_token() }}',
        reason: reason
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            // Refresh the modal content
            viewReport(reportId);
            // Refresh the table
            if (typeof reportsTable !== 'undefined') {
                reportsTable.ajax.reload();
            }
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr) {
        console.error('Lift suspension error:', xhr.responseText);
        let errorMessage = 'Error lifting suspension';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        }

        showAlert(errorMessage, 'danger');
    });
}

function reverseDecision(reportId) {
    const reason = prompt('Please provide a detailed reason for reversing this decision:');
    if (reason === null) return; // User cancelled

    if (!reason.trim()) {
        showAlert('Please provide a reason for reversing the decision', 'danger');
        return;
    }

    if (!confirm('Are you sure you want to reverse this decision? This action will be logged in the audit trail.')) {
        return;
    }

    $.post(`{{ url('admin/reports') }}/${reportId}/reverse-decision`, {
        _token: '{{ csrf_token() }}',
        reason: reason
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            // Refresh the modal content
            viewReport(reportId);
            // Refresh the table
            if (typeof reportsTable !== 'undefined') {
                reportsTable.ajax.reload();
            }
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function(xhr) {
        console.error('Reverse decision error:', xhr.responseText);
        let errorMessage = 'Error reversing decision';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        }

        showAlert(errorMessage, 'danger');
    });
}
</script>
@endpush
