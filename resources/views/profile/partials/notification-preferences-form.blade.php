@php
    $preferences = auth()->user()->getNotificationPreferences();
    $notificationTypes = \App\Models\UserNotificationPreference::getNotificationTypes();
    $notificationChannels = \App\Models\UserNotificationPreference::getNotificationChannels();
@endphp

<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
        <h3 class="text-lg font-medium text-gray-900">Notification Preferences</h3>
        <p class="mt-1 text-sm text-gray-600">
            Manage how and when you receive notifications. You can customize preferences for different types of notifications across multiple channels.
        </p>
    </div>

    <form method="POST" action="{{ route('profile.notifications.update') }}" class="space-y-8">
        @csrf
        @method('PATCH')

        <!-- Browser Permission Status -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-base font-medium text-blue-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Browser Notifications
                    </h4>
                    <p class="text-sm text-blue-700">Enable browser notifications to receive real-time updates</p>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="permission-status" class="px-3 py-1 rounded-full text-sm font-medium">
                        <!-- Status will be updated by JavaScript -->
                    </span>
                    <button type="button" id="enable-browser-notifications"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Enable Notifications
                    </button>
                </div>
            </div>
        </div>

        <!-- Push Notifications Section -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h4 class="text-base font-medium text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        Push Notifications
                    </h4>
                    <p class="text-sm text-gray-600">Receive notifications on your device (requires browser permission above)</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="push_notifications_enabled" value="1"
                           {{ $preferences->push_notifications_enabled ? 'checked' : '' }}
                           class="sr-only peer" onchange="toggleSection('push')">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <div id="push-section" class="grid grid-cols-1 md:grid-cols-2 gap-4 {{ !$preferences->push_notifications_enabled ? 'opacity-50 pointer-events-none' : '' }}">
                @foreach($notificationTypes as $type => $label)
                    <label class="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                        <input type="checkbox" name="push_{{ $type }}" value="1" 
                               {{ $preferences->{'push_' . $type} ? 'checked' : '' }}
                               class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                        <span class="text-sm font-medium text-gray-700">{{ $label }}</span>
                    </label>
                @endforeach
            </div>
        </div>

        <!-- Email Notifications Section -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h4 class="text-base font-medium text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Email Notifications
                    </h4>
                    <p class="text-sm text-gray-600">Receive notifications via email</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="email_notifications_enabled" value="1"
                           {{ $preferences->email_notifications_enabled ? 'checked' : '' }}
                           class="sr-only peer" onchange="toggleSection('email')">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                </label>
            </div>

            <div id="email-section" class="grid grid-cols-1 md:grid-cols-2 gap-4 {{ !$preferences->email_notifications_enabled ? 'opacity-50 pointer-events-none' : '' }}">
                @foreach($notificationTypes as $type => $label)
                    <label class="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                        <input type="checkbox" name="email_{{ $type }}" value="1" 
                               {{ $preferences->{'email_' . $type} ? 'checked' : '' }}
                               class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                        <span class="text-sm font-medium text-gray-700">{{ $label }}</span>
                    </label>
                @endforeach
            </div>
        </div>

        <!-- In-App Notifications Section -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h4 class="text-base font-medium text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        In-App Notifications
                    </h4>
                    <p class="text-sm text-gray-600">Show notifications within the application</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="inapp_notifications_enabled" value="1"
                           {{ $preferences->inapp_notifications_enabled ? 'checked' : '' }}
                           class="sr-only peer" onchange="toggleSection('inapp')">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                </label>
            </div>

            <div id="inapp-section" class="grid grid-cols-1 md:grid-cols-2 gap-4 {{ !$preferences->inapp_notifications_enabled ? 'opacity-50 pointer-events-none' : '' }}">
                @foreach($notificationTypes as $type => $label)
                    <label class="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                        <input type="checkbox" name="inapp_{{ $type }}" value="1" 
                               {{ $preferences->{'inapp_' . $type} ? 'checked' : '' }}
                               class="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500">
                        <span class="text-sm font-medium text-gray-700">{{ $label }}</span>
                    </label>
                @endforeach
            </div>
        </div>

        <!-- Quiet Hours Section -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h4 class="text-base font-medium text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        Quiet Hours
                    </h4>
                    <p class="text-sm text-gray-600">Disable push notifications during specific hours</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="quiet_hours_enabled" value="1"
                           {{ $preferences->quiet_hours_enabled ? 'checked' : '' }}
                           class="sr-only peer" onchange="toggleQuietHours()">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
            </div>

            <div id="quiet-hours-section" class="grid grid-cols-1 md:grid-cols-3 gap-4 {{ !$preferences->quiet_hours_enabled ? 'opacity-50 pointer-events-none' : '' }}">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                    <input type="time" name="quiet_hours_start" 
                           value="{{ $preferences->quiet_hours_start ? $preferences->quiet_hours_start->format('H:i') : '22:00' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                    <input type="time" name="quiet_hours_end" 
                           value="{{ $preferences->quiet_hours_end ? $preferences->quiet_hours_end->format('H:i') : '08:00' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                    <select name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="UTC" {{ $preferences->timezone === 'UTC' ? 'selected' : '' }}>UTC</option>
                        <option value="America/New_York" {{ $preferences->timezone === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                        <option value="America/Chicago" {{ $preferences->timezone === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                        <option value="America/Denver" {{ $preferences->timezone === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                        <option value="America/Los_Angeles" {{ $preferences->timezone === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                        <option value="Europe/London" {{ $preferences->timezone === 'Europe/London' ? 'selected' : '' }}>London</option>
                        <option value="Europe/Paris" {{ $preferences->timezone === 'Europe/Paris' ? 'selected' : '' }}>Paris</option>
                        <option value="Asia/Tokyo" {{ $preferences->timezone === 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo</option>
                        <option value="Asia/Kolkata" {{ $preferences->timezone === 'Asia/Kolkata' ? 'selected' : '' }}>India</option>
                        <option value="Australia/Sydney" {{ $preferences->timezone === 'Australia/Sydney' ? 'selected' : '' }}>Sydney</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <button type="button" onclick="resetToDefaults()" 
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Reset to Defaults
            </button>
            
            <div class="flex justify-end">
                <button type="submit"
                        class="px-6 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Preferences
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update browser permission status
    updateBrowserPermissionStatus();

    // Handle browser notification enable button
    document.getElementById('enable-browser-notifications').addEventListener('click', function() {
        requestBrowserNotificationPermission();
    });

    // Check if push notifications should be disabled based on browser permission
    checkPushNotificationAvailability();

    // Initialize toggle sections
    initializeToggleSections();
});

async function requestBrowserNotificationPermission() {
    if (!('Notification' in window)) {
        // Silent handling - no error messages
        return;
    }

    try {
        const permission = await Notification.requestPermission();
        updateBrowserPermissionStatus(permission);

        if (permission === 'granted') {
            // Only show success message, no error messages
            showToast('Browser notifications enabled successfully!', 'success');
            enablePushNotificationControls();

            // Initialize Firebase messaging if available
            if (typeof window.initializeFirebaseMessagingWithPermission === 'function') {
                await window.initializeFirebaseMessagingWithPermission();
            }
        } else if (permission === 'denied') {
            // Completely silent handling - no messages at all
            disablePushNotificationControls();
        }
    } catch (error) {
        // Completely silent error handling
    }
}

function updateBrowserPermissionStatus(status = null) {
    const currentStatus = status || Notification.permission;
    const statusElement = document.getElementById('permission-status');
    const enableButton = document.getElementById('enable-browser-notifications');

    switch (currentStatus) {
        case 'granted':
            statusElement.textContent = 'Enabled';
            statusElement.className = 'px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800';
            enableButton.textContent = 'Enabled';
            enableButton.disabled = true;
            enableButton.className = 'bg-gray-400 text-white px-4 py-2 rounded-lg font-medium cursor-not-allowed';
            enablePushNotificationControls();
            break;
        case 'denied':
            statusElement.textContent = 'Blocked';
            statusElement.className = 'px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800';
            enableButton.textContent = 'Blocked';
            enableButton.disabled = true;
            enableButton.className = 'bg-gray-400 text-white px-4 py-2 rounded-lg font-medium cursor-not-allowed';
            disablePushNotificationControls();
            break;
        default:
            statusElement.textContent = 'Not Enabled';
            statusElement.className = 'px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800';
            enableButton.textContent = 'Enable Notifications';
            enableButton.disabled = false;
            enableButton.className = 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors';
            disablePushNotificationControls();
    }
}

function checkPushNotificationAvailability() {
    if (Notification.permission !== 'granted') {
        disablePushNotificationControls();
    } else {
        enablePushNotificationControls();
    }
}

function disablePushNotificationControls() {
    const pushToggle = document.querySelector('input[name="push_notifications_enabled"]');
    const pushSection = document.getElementById('push-section');
    const pushCheckboxes = pushSection.querySelectorAll('input[type="checkbox"]');

    // Disable and uncheck main push toggle
    if (pushToggle) {
        pushToggle.checked = false;
        pushToggle.disabled = true;
    }

    // Disable all push notification checkboxes
    pushCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        checkbox.disabled = true;
    });

    // Add visual indication
    pushSection.style.opacity = '0.5';
    pushSection.style.pointerEvents = 'none';
}

function enablePushNotificationControls() {
    const pushToggle = document.querySelector('input[name="push_notifications_enabled"]');
    const pushSection = document.getElementById('push-section');
    const pushCheckboxes = pushSection.querySelectorAll('input[type="checkbox"]');

    // Enable main push toggle
    if (pushToggle) {
        pushToggle.disabled = false;
    }

    // Enable push notification checkboxes if main toggle is checked
    if (pushToggle && pushToggle.checked) {
        pushCheckboxes.forEach(checkbox => {
            checkbox.disabled = false;
        });
        pushSection.style.opacity = '1';
        pushSection.style.pointerEvents = 'auto';
    }
}

function initializeToggleSections() {
    // Initialize all toggle sections
    toggleSection('push');
    toggleSection('email');
    toggleSection('inapp');

    // Initialize quiet hours
    const quietHoursToggle = document.querySelector('input[name="quiet_hours_enabled"]');
    if (quietHoursToggle) {
        toggleQuietHours();
        quietHoursToggle.addEventListener('change', toggleQuietHours);
    }
}

function toggleSection(channel) {
    const toggle = document.querySelector(`input[name="${channel}_notifications_enabled"]`);
    const section = document.getElementById(`${channel}-section`);

    if (!toggle || !section) return;

    const isEnabled = toggle.checked;
    const checkboxes = section.querySelectorAll('input[type="checkbox"]');

    // Enable/disable individual checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.disabled = !isEnabled;
        if (!isEnabled) {
            checkbox.checked = false;
        }
    });

    // Visual feedback
    section.style.opacity = isEnabled ? '1' : '0.5';
    section.style.pointerEvents = isEnabled ? 'auto' : 'none';
}

function toggleQuietHours() {
    const toggle = document.querySelector('input[name="quiet_hours_enabled"]');
    const section = document.getElementById('quiet-hours-section');

    if (!toggle || !section) return;

    section.style.display = toggle.checked ? 'block' : 'none';
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 z-50 max-w-sm';

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    toast.innerHTML = `
        <div class="${bgColor} text-white px-6 py-3 rounded-lg shadow-lg">
            <div class="flex items-center">
                <span class="text-sm">${message}</span>
            </div>
        </div>
    `;

    document.body.appendChild(toast);

    // Remove after 4 seconds
    setTimeout(() => {
        toast.remove();
    }, 4000);
}
</script>

<script>
function toggleChannelSection(channel, enabled) {
    const section = document.getElementById(channel + '-section');
    if (enabled) {
        section.classList.remove('opacity-50', 'pointer-events-none');
    } else {
        section.classList.add('opacity-50', 'pointer-events-none');
    }
}

function toggleQuietHours(enabled) {
    const section = document.getElementById('quiet-hours-section');
    if (enabled) {
        section.classList.remove('opacity-50', 'pointer-events-none');
    } else {
        section.classList.add('opacity-50', 'pointer-events-none');
    }
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all notification preferences to defaults?')) {
        fetch('{{ route("profile.notifications.reset") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error resetting preferences. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting preferences. Please try again.');
        });
    }
}

function requestNotificationPermission() {
    // Check if notification permission is already granted
    if (Notification.permission === 'granted') {
        showNotificationFeedback('info', 'Browser notifications are already enabled!');
        return;
    }

    // Check if browser supports notifications
    if (!('Notification' in window)) {
        showNotificationFeedback('warning', 'Your browser does not support push notifications.');
        return;
    }

    // Do nothing - no modal, no permission request
}

function showNotificationFeedback(type, message) {
    // Create a non-intrusive feedback message
    const feedback = document.createElement('div');
    feedback.className = `alert alert-${type} alert-dismissible fade show mt-3`;
    feedback.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert after the notification preferences form
    const form = document.querySelector('form[action*="notifications"]');
    if (form) {
        form.insertAdjacentElement('afterend', feedback);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.remove();
            }
        }, 5000);
    }
}
</script>
