<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Notification Preferences</h3>
                <p class="text-sm text-gray-600 mt-1">Manage how you receive notifications for different activities</p>
            </div>
            <div class="flex items-center space-x-2">
                <button id="enable-push-notifications" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    <i class="fas fa-bell mr-2"></i>Enable Push Notifications
                </button>
                <div id="notification-status" class="text-sm text-gray-500"></div>
            </div>
        </div>
    </div>

    <div class="p-6">
        <form id="notification-preferences-form" method="POST" action="{{ route('profile.notification-preferences.update') }}">
            @csrf
            @method('PUT')

            <!-- Global Settings -->
            <div class="mb-8">
                <h4 class="text-md font-semibold text-gray-900 mb-4">Global Settings</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Quiet Hours -->
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="quiet-hours-enabled" name="quiet_hours_enabled" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm font-medium text-gray-700">Enable Quiet Hours</span>
                        </label>
                        <div id="quiet-hours-settings" class="ml-6 space-y-2 hidden">
                            <div class="flex items-center space-x-2">
                                <input type="time" name="quiet_hours_start" class="border-gray-300 rounded-md text-sm" value="22:00">
                                <span class="text-sm text-gray-500">to</span>
                                <input type="time" name="quiet_hours_end" class="border-gray-300 rounded-md text-sm" value="08:00">
                            </div>
                            <p class="text-xs text-gray-500">No notifications will be sent during these hours</p>
                        </div>
                    </div>

                    <!-- Days of Week -->
                    <div class="space-y-3">
                        <label class="text-sm font-medium text-gray-700">Active Days</label>
                        <div class="grid grid-cols-7 gap-1">
                            @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $index => $day)
                            <label class="flex flex-col items-center">
                                <input type="checkbox" name="days_enabled[]" value="{{ $index + 1 }}" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                <span class="text-xs text-gray-600 mt-1">{{ $day }}</span>
                            </label>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Categories -->
            <div class="space-y-6">
                <h4 class="text-md font-semibold text-gray-900">Notification Categories</h4>

                @php
                    $categories = [
                        'general' => [
                            'name' => 'General Notifications',
                            'description' => 'System announcements, app updates, and feature introductions',
                            'icon' => 'bell',
                            'color' => 'blue'
                        ],
                        'payment' => [
                            'name' => 'Payment Notifications',
                            'description' => 'Payment confirmations, failed transactions, refunds, and wallet updates',
                            'icon' => 'credit-card',
                            'color' => 'green'
                        ],
                        'match' => [
                            'name' => 'Match Notifications',
                            'description' => 'New matches, mutual likes, profile views, and match-related updates',
                            'icon' => 'heart',
                            'color' => 'pink'
                        ],
                        'partner_swapping' => [
                            'name' => 'Partner Swapping',
                            'description' => 'Activity partner exchange requests, responses, and confirmations',
                            'icon' => 'users',
                            'color' => 'purple'
                        ],
                        'booking_request' => [
                            'name' => 'Booking Requests',
                            'description' => 'Meeting requests, booking confirmations, and cancellations',
                            'icon' => 'calendar',
                            'color' => 'orange'
                        ],
                        'meeting_reminder' => [
                            'name' => 'Meeting Reminders',
                            'description' => 'Upcoming meeting alerts, location reminders, and time confirmations',
                            'icon' => 'clock',
                            'color' => 'yellow'
                        ],
                        'subscription' => [
                            'name' => 'Subscription Reminders',
                            'description' => 'Premium subscription expiring, renewal reminders, and plan changes',
                            'icon' => 'star',
                            'color' => 'indigo'
                        ],
                        'email_verification' => [
                            'name' => 'Email Verification',
                            'description' => 'Account verification required and verification status updates',
                            'icon' => 'mail',
                            'color' => 'red'
                        ]
                    ];
                @endphp

                @foreach($categories as $key => $category)
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-{{ $category['color'] }}-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-{{ $category['icon'] }} text-{{ $category['color'] }}-600"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-sm font-semibold text-gray-900">{{ $category['name'] }}</h5>
                                @if($key !== 'email_verification')
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="categories[{{ $key }}][enabled]" class="sr-only peer category-toggle" data-category="{{ $key }}" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                                @else
                                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Required</span>
                                @endif
                            </div>
                            <p class="text-xs text-gray-600 mb-3">{{ $category['description'] }}</p>

                            <div class="category-settings space-y-3" data-category="{{ $key }}" style="{{ $key === 'email_verification' ? '' : 'display: none;' }}">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <!-- Push Notifications -->
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" name="categories[{{ $key }}][push_enabled]" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" {{ in_array($key, ['payment', 'email_verification', 'booking_request', 'meeting_reminder']) ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700">Push</span>
                                        <i class="fas fa-mobile-alt text-gray-400 text-xs"></i>
                                    </label>

                                    <!-- In-App Notifications -->
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" name="categories[{{ $key }}][in_app_enabled]" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="text-sm text-gray-700">In-App</span>
                                        <i class="fas fa-bell text-gray-400 text-xs"></i>
                                    </label>

                                    <!-- Sound -->
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" name="categories[{{ $key }}][sound_enabled]" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="text-sm text-gray-700">Sound</span>
                                        <i class="fas fa-volume-up text-gray-400 text-xs"></i>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Save Button -->
            <div class="mt-8 flex justify-end">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>Save Preferences
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check notification permission status
    updateNotificationStatus();

    // Handle quiet hours toggle
    const quietHoursCheckbox = document.getElementById('quiet-hours-enabled');
    const quietHoursSettings = document.getElementById('quiet-hours-settings');

    quietHoursCheckbox.addEventListener('change', function() {
        if (this.checked) {
            quietHoursSettings.classList.remove('hidden');
        } else {
            quietHoursSettings.classList.add('hidden');
        }
    });

    // Handle category toggles
    document.querySelectorAll('.category-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const category = this.dataset.category;
            const settings = document.querySelector(`.category-settings[data-category="${category}"]`);

            if (this.checked) {
                settings.style.display = 'block';
            } else {
                settings.style.display = 'none';
            }
        });
    });

    // Handle enable push notifications button - DISABLED to remove popups
    document.getElementById('enable-push-notifications').addEventListener('click', function() {
        // DISABLED: No longer show notification permission popups
        // if (window.notificationPermissionModal) {
        //     window.notificationPermissionModal.show();
        // } else {
        //     requestNotificationPermission();
        // }
        console.info('Notification permission popup disabled');
    });

    // Handle form submission
    document.getElementById('notification-preferences-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveNotificationPreferences();
    });
});

function updateNotificationStatus() {
    const statusElement = document.getElementById('notification-status');
    const enableButton = document.getElementById('enable-push-notifications');

    if ('Notification' in window) {
        const permission = Notification.permission;

        switch (permission) {
            case 'granted':
                statusElement.textContent = 'Push notifications enabled';
                statusElement.className = 'text-sm text-green-600';
                enableButton.style.display = 'none';
                break;
            case 'denied':
                statusElement.textContent = 'Push notifications blocked';
                statusElement.className = 'text-sm text-red-600';
                enableButton.textContent = 'Enable in Browser Settings';
                enableButton.disabled = true;
                break;
            default:
                statusElement.textContent = 'Push notifications not enabled';
                statusElement.className = 'text-sm text-gray-500';
                break;
        }
    } else {
        statusElement.textContent = 'Push notifications not supported';
        statusElement.className = 'text-sm text-gray-500';
        enableButton.style.display = 'none';
    }
}

async function requestNotificationPermission() {
    // DISABLED: No longer request notification permission to remove popups
    console.info('Notification permission request disabled to remove popups');

    // if ('Notification' in window) {
    //     try {
    //         const permission = await Notification.requestPermission();
    //         updateNotificationStatus();

    //         if (permission === 'granted') {
    //             // Register service worker and get push subscription
    //             if ('serviceWorker' in navigator) {
    //                 const registration = await navigator.serviceWorker.ready;
    //                 const subscription = await registration.pushManager.subscribe({
    //                     userVisibleOnly: true,
    //                     applicationServerKey: '{{ config("firebase.vapid_key") }}'
    //                 });

    //                 // Send subscription to server
    //                 await fetch('/api/push-subscriptions', {
    //                     method: 'POST',
    //                     headers: {
    //                         'Content-Type': 'application/json',
    //                         'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    //                     },
    //                     body: JSON.stringify(subscription)
    //                 });
    //             }
    //         }
    //     } catch (error) {
    //         console.error('Error requesting notification permission:', error);
    //     }
    // }
}

async function saveNotificationPreferences() {
    const form = document.getElementById('notification-preferences-form');
    const formData = new FormData(form);

    try {
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (response.ok) {
            // Show success message
            showNotificationFeedback('success', 'Notification preferences saved successfully!');
        } else {
            throw new Error('Failed to save preferences');
        }
    } catch (error) {
        showNotificationFeedback('error', 'Failed to save preferences. Please try again.');
    }
}

function showNotificationFeedback(type, message) {
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
        'bg-red-100 text-red-800 border border-red-200'
    }`;
    feedback.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(feedback);

    // Remove after 3 seconds
    setTimeout(() => {
        feedback.remove();
    }, 3000);
}
</script>