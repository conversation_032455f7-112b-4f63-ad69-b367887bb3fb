@extends('layouts.app')

@section('title', $event->title)
@section('description', 'Event details for ' . $event->title . ' - Join this exclusive event and connect with like-minded individuals.')

@section('content')
    <!-- Clean Hero Section -->
    <section class="bg-white relative overflow-hidden py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <!-- Event Title -->
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    {{ $event->title }}
                </h1>

                <!-- Event Subtitle -->
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                    Join this exclusive event and create meaningful connections
                </p>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    @if($event->user_has_paid)
                        <a href="#event-details" class="inline-flex items-center justify-center px-8 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-200">
                            <i data-lucide="check-circle" class="w-5 h-5 mr-2"></i>
                            You're Registered!
                        </a>
                    @else
                        <a href="{{ route('payment.meeting-event', $event->id) }}" class="inline-flex items-center justify-center px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                            <i data-lucide="calendar-plus" class="w-5 h-5 mr-2"></i>
                            Join This Event
                        </a>
                    @endif
                    @if(\App\Models\MeetingAddress::hasAvailableEvents())
                        <a href="{{ route('event.address') }}" class="inline-flex items-center justify-center px-8 py-3 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                            Back to Events
                        </a>
                    @else
                        <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-8 py-3 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                            Back to Dashboard
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Event Details Section -->
    <section id="event-details" class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <!-- Event Information Card -->
            <div class="max-w-4xl mx-auto mb-8">
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    <!-- Status Badge -->
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-2xl font-bold text-gray-900">{{ $event->title }}</h2>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $event->user_has_paid ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                @if($event->user_has_paid)
                                    <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                                    Registered
                                @else
                                    <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                                    Available
                                @endif
                            </span>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Event Meta Information -->
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                            @if($event->event_date)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <i data-lucide="calendar" class="w-6 h-6 text-indigo-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-500">Date</p>
                                        <p class="font-semibold text-gray-900">{{ $event->event_date->format('l, F j, Y') }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <i data-lucide="clock" class="w-6 h-6 text-indigo-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-500">Time</p>
                                        <p class="font-semibold text-gray-900">{{ $event->event_date->format('g:i A') }}</p>
                                    </div>
                                </div>
                            @endif
                            @if($event->location)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg {{ $event->event_date ? 'md:col-span-2 lg:col-span-1' : 'md:col-span-2' }}">
                                    <div class="flex-shrink-0">
                                        <i data-lucide="map-pin" class="w-6 h-6 text-indigo-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-500">Location</p>
                                        <p class="font-semibold text-gray-900">{{ $event->location }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Registration Status -->
                        @if($event->user_has_paid)
                            <div class="text-center py-6">
                                <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                                    <i data-lucide="check" class="w-8 h-8 text-green-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-green-800 mb-2">You're Registered!</h3>
                                <p class="text-green-600">See you at the event</p>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <a href="{{ route('payment.meeting-event', $event->id) }}" class="inline-flex items-center justify-center px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                                    <i data-lucide="calendar-plus" class="w-5 h-5 mr-2"></i>
                                    Join This Event
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Event Description -->
            <div class="max-w-4xl mx-auto mb-8">
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-xl font-bold text-gray-900">Event Details</h3>
                        <p class="text-gray-600">Everything you need to know about this event</p>
                    </div>
                    <div class="p-6">
                        <div class="prose max-w-none text-gray-700">
                            {!! $event->description !!}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rules and Regulations -->
            @if(!empty($event->rules_and_regulations) && trim(strip_tags($event->rules_and_regulations)) !== '')
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center">
                                <h3 class="text-xl font-bold text-gray-900">Rules and Guidelines</h3>
                                <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Important
                                </span>
                            </div>
                            <p class="text-gray-600">Please read these important guidelines for the event</p>
                        </div>
                        <div class="p-6">
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                                <div class="prose max-w-none text-gray-700">
                                    {!! $event->rules_and_regulations !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif


            @if($event->user_has_paid)
                <!-- Payment Confirmation -->
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-xl font-bold text-gray-900">You're All Set!</h3>
                            <p class="text-gray-600">Thank you for registering for this event</p>
                        </div>
                        <div class="p-6 text-center">
                            <div class="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                                <i data-lucide="check-circle" class="w-10 h-10 text-green-600"></i>
                            </div>

                            @if(isset($paymentAmount))
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                    <div class="flex items-center justify-center">
                                        <i data-lucide="check-circle" class="w-5 h-5 text-green-600 mr-2"></i>
                                        <span class="font-semibold text-gray-900">
                                            Payment Completed: <span class="text-green-600">₹{{ number_format($paymentAmount, 0) }}</span>
                                        </span>
                                    </div>
                                </div>
                            @endif

                            <p class="text-gray-600 mb-6">
                                You're now registered for this event. We'll send you event details and updates via email.
                            </p>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-6">
                                @if(\App\Models\MeetingAddress::hasAvailableEvents())
                                    <a href="{{ route('event.address') }}" class="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                                        <i data-lucide="calendar" class="w-5 h-5 mr-2"></i>
                                        View Other Events
                                    </a>
                                @endif
                                <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    <i data-lucide="home" class="w-5 h-5 mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>

                            <!-- Security Note -->
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                                <div class="flex items-center justify-center text-green-600">
                                    <i data-lucide="shield-check" class="w-4 h-4 mr-2"></i>
                                    <span class="text-sm font-medium">Secure payment processed by Razorpay</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Event Tips Section -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Event Tips for Success
                </h2>
                <p class="text-xl text-gray-600">Make the most of your event experience</p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <!-- Tip 1 -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                        <i data-lucide="clock" class="w-8 h-8 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Arrive On Time</h3>
                    <p class="text-gray-500 mb-4">Be punctual for the best experience</p>
                    <p class="text-gray-600">Please arrive 15 minutes early to check in, get settled, and start connecting with other attendees.</p>
                </div>

                <!-- Tip 2 -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                        <i data-lucide="smile" class="w-8 h-8 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Be Yourself</h3>
                    <p class="text-gray-500 mb-4">Authenticity creates genuine connections</p>
                    <p class="text-gray-600">Authenticity is key to making genuine connections. Be open, honest, and let your personality shine.</p>
                </div>

                <!-- Tip 3 -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                        <i data-lucide="heart-handshake" class="w-8 h-8 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Have Fun</h3>
                    <p class="text-gray-500 mb-4">Enjoy every moment of the experience</p>
                    <p class="text-gray-600">Relax, enjoy the experience, and make new friends! Love often finds us when we're having fun.</p>
                </div>
            </div>
        </div>
    </section>
@endsection
