@extends('layouts.app')

@section('title', 'Notifications')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Notifications</h1>
                        <p class="text-sm text-gray-600 mt-1">Stay updated with your latest activities</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button id="mark-all-read" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            <i class="fas fa-check-double mr-1"></i>Mark All Read
                        </button>
                        <button id="clear-all" class="text-red-600 hover:text-red-700 text-sm font-medium">
                            <i class="fas fa-trash mr-1"></i>Clear All
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats -->
            <div class="p-6 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="total-count">{{ $stats['total'] ?? 0 }}</div>
                        <div class="text-sm text-gray-600">Total</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="unread-count">{{ $stats['unread'] ?? 0 }}</div>
                        <div class="text-sm text-gray-600">Unread</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-600" id="read-count">{{ $stats['read'] ?? 0 }}</div>
                        <div class="text-sm text-gray-600">Read</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600" id="today-count">0</div>
                        <div class="text-sm text-gray-600">Today</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="p-4">
                <div class="flex flex-wrap items-center gap-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Filter by:</label>
                        <select id="category-filter" class="border-gray-300 rounded-md text-sm">
                            <option value="">All Categories</option>
                            <option value="general">General</option>
                            <option value="payment">Payment</option>
                            <option value="match">Matches</option>
                            <option value="partner_swapping">Partner Swapping</option>
                            <option value="booking_request">Bookings</option>
                            <option value="meeting_reminder">Meetings</option>
                            <option value="subscription">Subscription</option>
                            <option value="email_verification">Email Verification</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Status:</label>
                        <select id="status-filter" class="border-gray-300 rounded-md text-sm">
                            <option value="">All</option>
                            <option value="unread">Unread</option>
                            <option value="read">Read</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Priority:</label>
                        <select id="priority-filter" class="border-gray-300 rounded-md text-sm">
                            <option value="">All Priorities</option>
                            <option value="urgent">Urgent</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                    <button id="clear-filters" class="text-gray-600 hover:text-gray-700 text-sm">
                        <i class="fas fa-times mr-1"></i>Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div id="notifications-container">
                <!-- Loading state -->
                <div id="loading-state" class="p-8 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="text-gray-600 mt-2">Loading notifications...</p>
                </div>

                <!-- Empty state -->
                <div id="empty-state" class="p-8 text-center hidden">
                    <i class="fas fa-bell-slash text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                    <p class="text-gray-600">You're all caught up! New notifications will appear here.</p>
                </div>

                <!-- Notifications will be loaded here -->
                <div id="notifications-list"></div>
            </div>

            <!-- Load More -->
            <div id="load-more-container" class="p-4 border-t border-gray-200 text-center hidden">
                <button id="load-more-btn" class="text-blue-600 hover:text-blue-700 font-medium">
                    <i class="fas fa-chevron-down mr-1"></i>Load More
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Detail Modal -->
<div id="notification-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 id="modal-title" class="text-lg font-semibold text-gray-900"></h3>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="modal-content" class="text-gray-700"></div>
                <div id="modal-actions" class="mt-6 flex justify-end space-x-3"></div>
            </div>
        </div>
    </div>
</div>

<script>
class NotificationInbox {
    constructor() {
        this.currentPage = 1;
        this.hasMorePages = true;
        this.filters = {
            category: '',
            status: '',
            priority: ''
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadNotifications();
    }
    
    bindEvents() {
        // Filter events
        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.filters.category = e.target.value;
            this.resetAndLoad();
        });
        
        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.resetAndLoad();
        });
        
        document.getElementById('priority-filter').addEventListener('change', (e) => {
            this.filters.priority = e.target.value;
            this.resetAndLoad();
        });
        
        document.getElementById('clear-filters').addEventListener('click', () => {
            this.clearFilters();
        });
        
        // Action events
        document.getElementById('mark-all-read').addEventListener('click', () => {
            this.markAllAsRead();
        });
        
        document.getElementById('clear-all').addEventListener('click', () => {
            this.clearAllNotifications();
        });
        
        document.getElementById('load-more-btn').addEventListener('click', () => {
            this.loadMoreNotifications();
        });
        
        // Modal events
        document.getElementById('close-modal').addEventListener('click', () => {
            this.closeModal();
        });
        
        document.getElementById('notification-modal').addEventListener('click', (e) => {
            if (e.target.id === 'notification-modal') {
                this.closeModal();
            }
        });
    }
    
    async loadNotifications(page = 1) {
        try {
            const params = new URLSearchParams({
                page: page,
                ...this.filters
            });
            
            const response = await fetch(`/api/notifications?${params}`);
            const data = await response.json();
            
            if (page === 1) {
                document.getElementById('notifications-list').innerHTML = '';
                document.getElementById('loading-state').classList.add('hidden');
            }
            
            if (data.notifications.length === 0 && page === 1) {
                document.getElementById('empty-state').classList.remove('hidden');
                document.getElementById('load-more-container').classList.add('hidden');
            } else {
                document.getElementById('empty-state').classList.add('hidden');
                this.renderNotifications(data.notifications);
                
                // Update pagination
                this.hasMorePages = data.has_more_pages;
                this.currentPage = page;
                
                if (this.hasMorePages) {
                    document.getElementById('load-more-container').classList.remove('hidden');
                } else {
                    document.getElementById('load-more-container').classList.add('hidden');
                }
            }
            
            // Update stats
            this.updateStats(data.stats);
            
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }
    
    renderNotifications(notifications) {
        const container = document.getElementById('notifications-list');
        
        notifications.forEach(notification => {
            const element = this.createNotificationElement(notification);
            container.appendChild(element);
        });
    }
    
    createNotificationElement(notification) {
        const div = document.createElement('div');
        div.className = `notification-item border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer ${
            notification.read_at ? '' : 'bg-blue-50 border-l-4 border-l-blue-500'
        }`;
        div.dataset.notificationId = notification.id;
        
        const categoryColors = {
            general: 'blue',
            payment: 'green',
            match: 'pink',
            partner_swapping: 'purple',
            booking_request: 'orange',
            meeting_reminder: 'yellow',
            subscription: 'indigo',
            email_verification: 'red'
        };
        
        const color = categoryColors[notification.category] || 'gray';
        
        div.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-${color}-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-${notification.icon || 'bell'} text-${color}-600"></i>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-semibold text-gray-900 truncate">${notification.title}</h4>
                        <div class="flex items-center space-x-2">
                            ${notification.priority === 'high' || notification.priority === 'urgent' ? 
                                `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    ${notification.priority}
                                </span>` : ''
                            }
                            <span class="text-xs text-gray-500">${this.formatDate(notification.created_at)}</span>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mt-1 line-clamp-2">${notification.message}</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            ${notification.category.replace('_', ' ')}
                        </span>
                        ${!notification.read_at ? '<div class="w-2 h-2 bg-blue-600 rounded-full"></div>' : ''}
                    </div>
                </div>
            </div>
        `;
        
        div.addEventListener('click', () => {
            this.openNotificationModal(notification);
            if (!notification.read_at) {
                this.markAsRead(notification.id);
            }
        });
        
        return div;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);
        
        if (diffInHours < 1) {
            return 'Just now';
        } else if (diffInHours < 24) {
            return `${Math.floor(diffInHours)}h ago`;
        } else if (diffInHours < 48) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString();
        }
    }
    
    openNotificationModal(notification) {
        document.getElementById('modal-title').textContent = notification.title;
        document.getElementById('modal-content').innerHTML = `
            <p class="mb-4">${notification.message}</p>
            <div class="text-sm text-gray-500">
                <p><strong>Category:</strong> ${notification.category.replace('_', ' ')}</p>
                <p><strong>Priority:</strong> ${notification.priority}</p>
                <p><strong>Received:</strong> ${new Date(notification.created_at).toLocaleString()}</p>
            </div>
        `;
        
        // Add action buttons if available
        const actionsContainer = document.getElementById('modal-actions');
        actionsContainer.innerHTML = '';
        
        if (notification.action_data && notification.action_data.url) {
            const actionBtn = document.createElement('button');
            actionBtn.className = 'bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700';
            actionBtn.textContent = 'View Details';
            actionBtn.onclick = () => {
                window.location.href = notification.action_data.url;
            };
            actionsContainer.appendChild(actionBtn);
        }
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'bg-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-400';
        closeBtn.textContent = 'Close';
        closeBtn.onclick = () => this.closeModal();
        actionsContainer.appendChild(closeBtn);
        
        document.getElementById('notification-modal').classList.remove('hidden');
    }
    
    closeModal() {
        document.getElementById('notification-modal').classList.add('hidden');
    }
    
    async markAsRead(notificationId) {
        try {
            await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            // Update UI
            const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (element) {
                element.classList.remove('bg-blue-50', 'border-l-4', 'border-l-blue-500');
                const unreadDot = element.querySelector('.w-2.h-2.bg-blue-600');
                if (unreadDot) {
                    unreadDot.remove();
                }
            }
            
            // Update stats
            this.updateUnreadCount(-1);
            
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    async markAllAsRead() {
        try {
            await fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            // Reload notifications
            this.resetAndLoad();
            
        } catch (error) {
            console.error('Error marking all as read:', error);
        }
    }
    
    async clearAllNotifications() {
        if (!confirm('Are you sure you want to clear all notifications? This action cannot be undone.')) {
            return;
        }
        
        try {
            await fetch('/api/notifications/clear-all', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            // Reload notifications
            this.resetAndLoad();
            
        } catch (error) {
            console.error('Error clearing notifications:', error);
        }
    }
    
    clearFilters() {
        this.filters = { category: '', status: '', priority: '' };
        document.getElementById('category-filter').value = '';
        document.getElementById('status-filter').value = '';
        document.getElementById('priority-filter').value = '';
        this.resetAndLoad();
    }
    
    resetAndLoad() {
        this.currentPage = 1;
        this.hasMorePages = true;
        document.getElementById('loading-state').classList.remove('hidden');
        this.loadNotifications(1);
    }
    
    loadMoreNotifications() {
        if (this.hasMorePages) {
            this.loadNotifications(this.currentPage + 1);
        }
    }
    
    updateStats(stats) {
        document.getElementById('total-count').textContent = stats.total || 0;
        document.getElementById('unread-count').textContent = stats.unread || 0;
        document.getElementById('read-count').textContent = stats.read || 0;
        document.getElementById('today-count').textContent = stats.today || 0;
    }
    
    updateUnreadCount(change) {
        const element = document.getElementById('unread-count');
        const current = parseInt(element.textContent) || 0;
        element.textContent = Math.max(0, current + change);
        
        const readElement = document.getElementById('read-count');
        const currentRead = parseInt(readElement.textContent) || 0;
        readElement.textContent = currentRead - change;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new NotificationInbox();
});
</script>
@endsection
