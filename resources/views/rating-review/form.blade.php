<x-app-layout>
    @section('title', 'Rate & Review - ' . $otherUser->name)
    @section('description', 'Share your experience and rate your meeting with ' . $otherUser->name)

    <!-- Hero Section -->
    <x-hero-section
        title="Rate & Review"
        subtitle="Share your experience with <span class='font-semibold text-gradient-primary'>{{ $otherUser->name }}</span>"
        description="Your feedback helps build trust in our community"
        :showSteps="false"
    />

    <div class="min-h-screen bg-gray-50">
        <div class="py-8">
            <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Back Button -->
                <div class="mb-6">
                    <a href="{{ route('notifications.index') }}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-lg border border-gray-300 transition-colors duration-200 shadow-sm hover:shadow-md">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Back to Notifications
                    </a>
                </div>

                <!-- Success/Error Messages -->
                @if(session('success'))
                    <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                @if($errors->any())
                    <div class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <p class="font-medium">Please correct the following errors:</p>
                                <ul class="mt-1 list-disc list-inside text-sm">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Rating Form Card -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                    <!-- Header -->
                    <div class="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-6 text-white">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold mb-2">Rate Your Experience</h2>
                            <p class="text-white/80">How was your meeting with {{ $otherUser->name }}?</p>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div class="p-6 border-b border-gray-200 bg-gray-50">
                        <div class="flex items-center space-x-4">
                            <img src="{{ $otherUser->profile_picture_url }}" 
                                 alt="{{ $otherUser->name }}" 
                                 class="w-16 h-16 rounded-full border-2 border-gray-200">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900">{{ $otherUser->name }}</h3>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        {{ $booking->booking_date->format('M d, Y') }} at {{ $booking->booking_date->format('h:i A') }}
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ $booking->duration_hours }} hour{{ $booking->duration_hours > 1 ? 's' : '' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rating Form -->
                    <div class="p-6">
                        <form id="ratingForm" method="POST" action="{{ route('rating.submit') }}" class="space-y-6">
                            @csrf
                            <input type="hidden" name="booking_id" value="{{ $booking->id }}">

                            <!-- Star Rating -->
                            <div class="text-center">
                                <label class="block text-lg font-semibold text-gray-900 mb-4">
                                    How would you rate your experience?
                                </label>
                                <div class="flex justify-center space-x-2 mb-4">
                                    @for($i = 1; $i <= 5; $i++)
                                        <button type="button" 
                                                class="star-rating text-4xl text-gray-300 hover:text-yellow-400 transition-colors duration-200 focus:outline-none" 
                                                data-rating="{{ $i }}"
                                                onclick="setRating({{ $i }})">
                                            <svg class="w-10 h-10 fill-current" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </button>
                                    @endfor
                                </div>
                                <input type="hidden" name="rating" id="selectedRating" required>
                                <p id="ratingText" class="text-sm text-gray-600 mb-4">Click on stars to rate</p>
                            </div>

                            <!-- Review Text -->
                            <div>
                                <label for="review_text" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Share your experience (optional)
                                </label>
                                <textarea name="review_text"
                                          id="review_text"
                                          rows="4"
                                          class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-yellow-500 focus:ring-0 transition-colors duration-200 resize-none"
                                          placeholder="Tell others about your experience with {{ $otherUser->name }}...">{{ old('review_text') }}</textarea>
                                <div class="text-xs text-gray-500 mt-1">Maximum 1000 characters</div>
                            </div>

                            <!-- Anonymous Option -->
                            <div class="flex items-center">
                                <input type="checkbox"
                                       name="is_anonymous"
                                       id="is_anonymous"
                                       value="1"
                                       {{ old('is_anonymous') ? 'checked' : '' }}
                                       class="w-4 h-4 text-yellow-600 bg-gray-100 border-gray-300 rounded focus:ring-yellow-500 focus:ring-2">
                                <label for="is_anonymous" class="ml-2 text-sm text-gray-700">
                                    Submit this review anonymously
                                </label>
                            </div>

                            <!-- Validation Error Message -->
                            <div id="validationError" class="hidden bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-4">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span id="validationErrorText">Please select a rating before submitting.</span>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-center pt-4">
                                <button type="submit"
                                        id="submitBtn"
                                        class="px-8 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <span id="submitBtnText">Submit Review</span>
                                    <span id="submitBtnLoading" class="hidden">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Submitting...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedRating = {{ old('rating', 0) }};
        const ratingTexts = {
            1: 'Poor - Not satisfied',
            2: 'Fair - Below expectations',
            3: 'Good - Met expectations',
            4: 'Very Good - Exceeded expectations',
            5: 'Excellent - Outstanding experience'
        };

        // Initialize rating from old input if available
        document.addEventListener('DOMContentLoaded', function() {
            if (selectedRating > 0) {
                setRating(selectedRating);
            }
        });

        function setRating(rating) {
            selectedRating = rating;
            document.getElementById('selectedRating').value = rating;

            // Hide validation error if visible
            const errorDiv = document.getElementById('validationError');
            if (errorDiv) {
                errorDiv.classList.add('hidden');
            }

            // Update star colors
            const stars = document.querySelectorAll('.star-rating');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('text-gray-300');
                    star.classList.add('text-yellow-400');
                } else {
                    star.classList.remove('text-yellow-400');
                    star.classList.add('text-gray-300');
                }
            });

            // Update rating text
            document.getElementById('ratingText').textContent = ratingTexts[rating];
        }

        // Form submission validation
        document.getElementById('ratingForm').addEventListener('submit', function(e) {
            // Hide any previous error messages
            const errorDiv = document.getElementById('validationError');
            if (errorDiv) {
                errorDiv.classList.add('hidden');
            }

            if (selectedRating === 0) {
                e.preventDefault();

                // Show error message
                if (errorDiv) {
                    errorDiv.classList.remove('hidden');
                    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                return false;
            }

            // Show loading state but don't disable the button
            const submitBtnText = document.getElementById('submitBtnText');
            const submitBtnLoading = document.getElementById('submitBtnLoading');

            if (submitBtnText) {
                submitBtnText.classList.add('hidden');
            }
            if (submitBtnLoading) {
                submitBtnLoading.classList.remove('hidden');
            }

            // Allow normal form submission to proceed
            return true;
        });
    </script>
</x-app-layout>
