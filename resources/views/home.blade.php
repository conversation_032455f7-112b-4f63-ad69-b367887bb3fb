@section('title', 'Connect Through Meaningful Activities - SettingWala')
@section('description', 'Join exclusive social events and discover meaningful friendships. A premium platform for genuine activity partnerships built through real-world interactions.')





<x-app-layout>
    <!-- Clean Hero Section -->
    <section class="bg-white relative overflow-hidden min-h-screen flex items-center">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center min-h-[75vh]">
                <!-- Hero Content -->
                <div class="space-y-8" data-aos="fade-right">
                    @guest
                        <div class="space-y-6">
                            <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                Connect Through
                                <span class="text-indigo-600">Shared Activities</span>
                            </h1>
                            <p class="text-xl text-gray-600 leading-relaxed max-w-lg">
                                Join exclusive social events and discover genuine friendships.
                                Experience premium activity partnerships through real-world interactions.
                            </p>
                        </div>
                    @else
                        <div class="space-y-6">
                            <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                Welcome Back,
                                <span class="text-indigo-600">{{ Auth::user()->name }}!</span>
                            </h1>
                            <p class="text-xl text-gray-600 leading-relaxed max-w-lg">
                                Ready to discover new friendships? Explore upcoming activities
                                and connect with like-minded individuals in your area.
                            </p>
                        </div>
                    @endguest

                    <div class="flex flex-col sm:flex-row gap-4">
                        @guest
                            <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i data-lucide="users" class="w-5 h-5 mr-2"></i>
                                Start Connecting
                            </a>
                            <a href="{{ route('static.how-it-works') }}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-indigo-600 text-indigo-600 font-semibold rounded-full hover:bg-indigo-50 transition-all duration-300">
                                <i data-lucide="play-circle" class="w-5 h-5 mr-2"></i>
                                How It Works
                            </a>
                        @else
                            @if(\App\Models\MeetingAddress::hasAvailableEvents())
                                <a href="{{ route('event.address') }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <i data-lucide="calendar" class="w-5 h-5 mr-2"></i>
                                    Browse Events
                                </a>
                            @endif
                            <a href="{{ route('profile.edit') }}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-indigo-600 text-indigo-600 font-semibold rounded-full hover:bg-indigo-50 transition-all duration-300">
                                <i data-lucide="user" class="w-5 h-5 mr-2"></i>
                                Edit Profile
                            </a>
                        @endguest
                    </div>
                </div>

                <!-- Hero Visual -->
                <div class="lg:pl-8" data-aos="fade-left" data-aos-delay="200">
                    <div class="bg-white rounded-3xl shadow-2xl p-8 text-center border border-gray-100">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                            <i data-lucide="heart-handshake" class="w-12 h-12 text-white"></i>
                        </div>

                        @guest
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">
                                Join Our Community
                            </h3>
                            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                                Connect with verified individuals through curated activities.
                                Safe, authentic, and meaningful friendships await you.
                            </p>
                        @else
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">
                                Your Dashboard
                            </h3>
                            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                                Manage your profile, join activities, and connect with verified members.
                                Your journey to meaningful friendships continues here.
                            </p>
                        @endguest


                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Why Choose Our Platform?
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Experience premium activity partnerships through authentic events and verified connections.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200">
                <!-- Feature 1 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full mb-6">
                        <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Verified Profiles
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        All profiles are verified through Google OAuth to ensure authenticity and safety for genuine friendships.
                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full mb-6">
                        <i data-lucide="map-pin" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Offline Events
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Join curated events in your area to meet new friends in comfortable, real-world settings.
                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-600 to-green-700 rounded-full mb-6">
                        <i data-lucide="heart-handshake" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Secure & Private
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Your privacy is our priority. All data is encrypted and your personal information stays secure.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-20 bg-white relative overflow-hidden">
        <!-- Subtle Decorative Elements -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    How It Works
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Simple steps to discover meaningful connections through our premium platform.
                </p>
            </div>

            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8" data-aos="fade-up" data-aos-delay="200">
                <!-- Step 1 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6 text-white text-2xl font-bold">
                        1
                    </div>
                    <div class="mb-6">
                        <i data-lucide="user-plus" class="w-12 h-12 text-indigo-600 mx-auto"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        Sign Up
                    </h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Create your account using Google OAuth for quick and secure registration.
                    </p>
                    <div class="pt-4 border-t border-gray-200">
                        <span class="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">30 seconds</span>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6 text-white text-2xl font-bold">
                        2
                    </div>
                    <div class="mb-6">
                        <i data-lucide="edit-3" class="w-12 h-12 text-indigo-600 mx-auto"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        Complete Profile
                    </h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Add your interests, photos, and preferences to create an authentic profile.
                    </p>
                    <div class="pt-4 border-t border-gray-200">
                        <span class="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">5 minutes</span>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6 text-white text-2xl font-bold">
                        3
                    </div>
                    <div class="mb-6">
                        <i data-lucide="calendar" class="w-12 h-12 text-indigo-600 mx-auto"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        Join Activities
                    </h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Participate in curated activities to meet potential friends in beautiful settings.
                    </p>
                    <div class="pt-4 border-t border-gray-200">
                        <span class="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">Amazing experience</span>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6 text-white text-2xl font-bold">
                        4
                    </div>
                    <div class="mb-6">
                        <i data-lucide="users" class="w-12 h-12 text-indigo-600 mx-auto"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        Build Friendships
                    </h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Build meaningful friendships through authentic real-world interactions.
                    </p>
                    <div class="pt-4 border-t border-gray-200">
                        <span class="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">Lasting connections</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Trust & Values Section -->
    <section class="py-20 bg-gray-50 relative overflow-hidden">
        <!-- Background Decorative Elements -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20">
            <!-- Enhanced Header -->
            <div class="text-center mb-16" data-aos="fade-up">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                    <i data-lucide="sparkles" class="w-12 h-12 text-white"></i>
                </div>
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Why Choose <span class="text-indigo-600">SettingWala</span>?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Experience the difference with our premium platform designed for meaningful friendships.
                    <span class="font-semibold text-gray-900">Join thousands who found their perfect activity partners.</span>
                </p>
            </div>

            <!-- Enhanced Value Cards -->
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8" data-aos="fade-up" data-aos-delay="200">
                <!-- Quick Setup Card -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                        <i data-lucide="zap" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Quick</h3>
                    <p class="text-gray-600 mb-4">Setup</p>
                    <div class="text-sm text-gray-500 mb-6">
                        Get started in under 30 seconds with secure Google OAuth authentication
                    </div>
                    <!-- Progress indicator -->
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 h-2 rounded-full w-full"></div>
                    </div>
                </div>

                <!-- Safe Environment Card -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                        <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Safe</h3>
                    <p class="text-gray-600 mb-4">Environment</p>
                    <div class="text-sm text-gray-500 mb-6">
                        Verified profiles and secure meetups in trusted beautiful venues
                    </div>
                    <!-- Progress indicator -->
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 h-2 rounded-full w-full"></div>
                    </div>
                </div>

                <!-- Real Connections Card -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                        <i data-lucide="users" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Real</h3>
                    <p class="text-gray-600 mb-4">Connections</p>
                    <div class="text-sm text-gray-500 mb-6">
                        Meet genuine people through curated activities and shared interests
                    </div>
                    <!-- Progress indicator -->
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 h-2 rounded-full w-full"></div>
                    </div>
                </div>

                <!-- Lasting Relationships Card -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                        <i data-lucide="heart" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Lasting</h3>
                    <p class="text-gray-600 mb-4">Friendships</p>
                    <div class="text-sm text-gray-500 mb-6">
                        Build meaningful bonds that grow into lifelong friendships
                    </div>
                    <!-- Progress indicator -->
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 h-2 rounded-full w-full"></div>
                    </div>
                </div>
            </div>

            <!-- Bottom CTA -->
            <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="400">
                <div class="inline-flex items-center px-6 py-4 bg-white rounded-full text-gray-600 border border-gray-200 shadow-lg">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-indigo-600"></i>
                    Trusted by thousands of users across India
                    <i data-lucide="heart" class="w-5 h-5 ml-2 text-indigo-600"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced CTA Section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-blue-50 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 text-center relative z-20">
            <div data-aos="fade-up">
                <!-- Main CTA Card -->
                <div class="bg-white rounded-3xl shadow-2xl p-12 max-w-4xl mx-auto border border-gray-100">
                    <div class="mb-8">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                            <i data-lucide="users" class="w-12 h-12 text-white"></i>
                        </div>
                        <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                            Ready to Find Great Friendships?
                        </h2>
                        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                            Join our premium platform and discover authentic friendships through curated activities.
                        </p>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                        @guest
                            <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i data-lucide="users" class="w-5 h-5 mr-2"></i>
                                Start Connecting
                            </a>
                            <a href="{{ route('static.how-it-works') }}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-indigo-600 text-indigo-600 font-semibold rounded-full hover:bg-indigo-50 transition-all duration-300">
                                <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                                Learn More
                            </a>
                        @else
                            @if(\App\Models\MeetingAddress::hasAvailableEvents())
                                <a href="{{ route('event.address') }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <i data-lucide="calendar" class="w-5 h-5 mr-2"></i>
                                    Browse Activities
                                </a>
                            @endif
                            <a href="{{ route('profile.edit') }}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-indigo-600 text-indigo-600 font-semibold rounded-full hover:bg-indigo-50 transition-all duration-300">
                                <i data-lucide="user" class="w-5 h-5 mr-2"></i>
                                Complete Profile
                            </a>
                        @endguest
                    </div>


                </div>
            </div>
        </div>
    </section>
</x-app-layout>