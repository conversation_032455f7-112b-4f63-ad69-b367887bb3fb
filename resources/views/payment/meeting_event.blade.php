<x-app-layout>
    <!-- Hero Section -->
    <x-hero-section
        title="{{ $event->title }}"
        subtitle="Complete your payment to join this <span class='font-semibold text-gradient-primary'>exclusive event</span>"
        description="Secure your spot in this amazing event with our safe and easy payment process."
        :showSteps="false"
    />

    <!-- Main Content -->
    <div class="py-8 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            @if ($hasPaidForEvent)
                <!-- Payment Success Card -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                    <div class="flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Payment Successful!</h3>
                    <p class="text-green-700 mb-4">You have already paid for this event! You can now view the full details.</p>
                    <a href="{{ route('event.address.show', $event->id) }}"
                       class="inline-flex items-center px-6 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200">
                        View Event Details
                    </a>
                </div>
            @else
                <!-- Event Information Card -->
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        Event Details
                    </h2>
                    <div class="text-gray-700 mb-4">
                        {!! $event->description !!}
                    </div>
                    @if($event->event_date)
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <div>
                                    <p class="font-medium text-blue-900">Event Date & Time</p>
                                    <p class="text-blue-700">{{ $event->event_date->format('l, F j, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($event->location)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-green-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <div class="flex-1">
                                    <p class="font-medium text-green-900">Event Location</p>
                                    <p class="text-green-700 mb-2">{{ $event->location }}</p>

                                    <!-- Distance Display -->
                                    <div id="distance-info" class="text-sm text-green-600 hidden">
                                        <span id="distance-text"></span>
                                    </div>

                                    <!-- Map and Directions -->
                                    <div class="mt-3 space-y-2">
                                        @if($event->latitude && $event->longitude)
                                            <button onclick="openInMaps({{ $event->latitude }}, {{ $event->longitude }}, '{{ addslashes($event->location) }}')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"/>
                                                </svg>
                                                Get Directions
                                            </button>
                                            <button onclick="calculateDistance()"
                                                    class="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200 ml-2">
                                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                </svg>
                                                Check Distance
                                            </button>
                                        @else
                                            <button onclick="searchLocationOnMaps('{{ addslashes($event->location) }}')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                                </svg>
                                                Search on Maps
                                            </button>
                                            <div class="text-xs text-gray-500 mt-1">
                                                Exact coordinates not available
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Rules and Regulations Card -->
                @if(!empty($event->rules_and_regulations) && trim(strip_tags($event->rules_and_regulations)) !== '')
                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-semibold text-amber-900 mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Rules and Regulations
                        </h3>
                        <div class="text-amber-800">
                            {!! $event->rules_and_regulations !!}
                        </div>
                    </div>
                @endif

                <!-- Payment Card -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                        </svg>
                        Secure Payment
                    </h3>

                    <!-- Payment Amount Display -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-800">
                                    Event Access Fee
                                </h4>
                                <p class="text-sm text-gray-600">One-time payment for this event</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-indigo-600">
                                    ₹{{ number_format($paymentAmount, 2) }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Features -->
                    <div class="grid grid-cols-3 gap-3 mb-6">
                        <div class="text-center p-3 bg-green-50 rounded-lg">
                            <svg class="w-5 h-5 text-green-500 mx-auto mb-1" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                            </svg>
                            <span class="text-xs font-medium text-green-700">Secure Payment</span>
                        </div>
                        <div class="text-center p-3 bg-blue-50 rounded-lg">
                            <svg class="w-5 h-5 text-blue-500 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            <span class="text-xs font-medium text-blue-700">Instant Access</span>
                        </div>
                        <div class="text-center p-3 bg-purple-50 rounded-lg">
                            <svg class="w-5 h-5 text-purple-500 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                            </svg>
                            <span class="text-xs font-medium text-purple-700">Premium Event</span>
                        </div>
                    </div>

                    <!-- Payment Button -->
                    <div class="text-center">
                        <button id="razorpay-button"
                                class="w-full inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200"
                                style="background-color: #4F46E5; border-color: #4F46E5;"
                                onmouseover="this.style.backgroundColor='#3730A3'"
                                onmouseout="this.style.backgroundColor='#4F46E5'"
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                            </svg>
                            Pay Now - ₹{{ number_format($paymentAmount, 2) }}
                        </button>

                        <!-- Payment Security Info -->
                        <div class="mt-4 flex items-center justify-center space-x-4 text-sm text-gray-500">
                            <div class="flex items-center">
                                <span>Powered by Razorpay</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                                </svg>
                                <span>SSL Secured</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden Form for Razorpay -->
                <form id="razorpay-form" action="{{ route('payment.meeting-event.process') }}" method="POST">
                    @csrf
                    <input type="hidden" name="event_id" value="{{ $event->id }}">
                    <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
                    <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
                    <input type="hidden" name="razorpay_signature" id="razorpay_signature">
                </form>

                <!-- Razorpay Script -->
                <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
                <script>
                    document.getElementById('razorpay-button').onclick = function() {
                        var options = {
                            key: "{{ $razorpayKey }}",
                            amount: {{ $paymentAmount * 100 }}, // Amount in paise
                            currency: "INR",
                            name: "SettingWala",
                            description: "Payment for {{ $event->title }}",
                            image: "{{ asset('images/logo.png') }}",
                            handler: function (response) {
                                document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                                document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                                document.getElementById('razorpay_signature').value = response.razorpay_signature;
                                document.getElementById('razorpay-form').submit();
                            },
                            prefill: {
                                name: "{{ $user->name }}",
                                email: "{{ $user->email }}",
                                contact: "{{ $user->contact_number ?? '' }}"
                            },
                            theme: {
                                color: "#4F46E5"
                            }
                        };
                        var rzp = new Razorpay(options);
                        rzp.open();
                    };
                </script>
            @endif
        </div>
    </div>

    <!-- Location and Maps JavaScript -->
    @if($event->location)
    <script>
        // Event location coordinates
        const eventLat = {{ $event->latitude ?? 'null' }};
        const eventLng = {{ $event->longitude ?? 'null' }};
        const eventLocation = "{{ addslashes($event->location) }}";

        // Function to open location in maps with coordinates
        function openInMaps(lat, lng, location) {
            // Detect if user is on mobile
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // Try to open in native maps app
                const mapsUrl = `https://maps.google.com/maps?q=${lat},${lng}&z=15&t=m`;
                window.open(mapsUrl, '_blank');
            } else {
                // Open in Google Maps web
                const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
                window.open(mapsUrl, '_blank');
            }
        }

        // Function to search location on maps when coordinates are not available
        function searchLocationOnMaps(location) {
            // Encode the location for URL
            const encodedLocation = encodeURIComponent(location);

            // Detect if user is on mobile
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // Try to open in native maps app
                const mapsUrl = `https://maps.google.com/maps?q=${encodedLocation}`;
                window.open(mapsUrl, '_blank');
            } else {
                // Open in Google Maps web
                const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
                window.open(mapsUrl, '_blank');
            }
        }

        // Function to calculate distance from user's location
        function calculateDistance() {
            if (!eventLat || !eventLng) {
                // Show a user-friendly message instead of alert
                const distanceInfo = document.getElementById('distance-info');
                const distanceText = document.getElementById('distance-text');
                distanceText.innerHTML = `
                    <svg class="w-4 h-4 inline mr-1 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    Exact coordinates not available for distance calculation
                `;
                distanceInfo.classList.remove('hidden');
                return;
            }

            if (!navigator.geolocation) {
                const distanceInfo = document.getElementById('distance-info');
                const distanceText = document.getElementById('distance-text');
                distanceText.innerHTML = `
                    <svg class="w-4 h-4 inline mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Geolocation is not supported by this browser
                `;
                distanceInfo.classList.remove('hidden');
                return;
            }

            const distanceInfo = document.getElementById('distance-info');
            const distanceText = document.getElementById('distance-text');

            // Show loading state
            distanceText.innerHTML = `
                <svg class="w-4 h-4 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                Calculating distance...
            `;
            distanceInfo.classList.remove('hidden');

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;

                    // Calculate distance using Haversine formula
                    const distance = calculateHaversineDistance(userLat, userLng, eventLat, eventLng);

                    // Display distance
                    distanceText.innerHTML = `
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                        Distance: ${distance.toFixed(1)} km from your location
                    `;

                    // Also show travel time estimate
                    const travelTime = Math.round(distance * 2); // Rough estimate: 2 minutes per km
                    if (travelTime > 0) {
                        distanceText.innerHTML += ` (≈${travelTime} min by car)`;
                    }
                },
                function(error) {
                    let errorMessage = 'Unable to get your location. ';
                    let iconSvg = `<svg class="w-4 h-4 inline mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>`;

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage += 'Please enable location access in your browser.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage += 'Location information unavailable.';
                            break;
                        case error.TIMEOUT:
                            errorMessage += 'Location request timed out.';
                            break;
                        default:
                            errorMessage += 'Unknown error occurred.';
                            break;
                    }
                    distanceText.innerHTML = iconSvg + errorMessage;
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        }

        // Haversine formula to calculate distance between two points
        function calculateHaversineDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // Radius of the Earth in kilometers
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a =
                Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c;
            return distance;
        }

        // Auto-calculate distance on page load if user has previously granted location permission
        document.addEventListener('DOMContentLoaded', function() {
            // Only try auto-calculation if coordinates are available
            if (eventLat && eventLng && navigator.geolocation && navigator.permissions) {
                navigator.permissions.query({name: 'geolocation'}).then(function(result) {
                    if (result.state === 'granted') {
                        // User has already granted permission, calculate distance automatically
                        setTimeout(calculateDistance, 1000);
                    }
                }).catch(function(error) {
                    // Permissions API not supported, skip auto-calculation
                    console.log('Permissions API not supported');
                });
            }
        });
    </script>
    @endif
</x-app-layout>
